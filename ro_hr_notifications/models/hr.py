from datetime import timedelta
from odoo import models, fields,api,_

class Employee(models.Model):
    _inherit = 'hr.employee'

    
    @api.model
    def create_activities(self):
        # Find users in the specified group
        users = self.env['res.users'].search([('groups_id', 'in', self.env.ref('ro_hr_notifications.group_send_notification').id)])

        # Find certificates expiring in the next month
        today = fields.Date.today()
        expiration_date_limit = today + timedelta(days=30)
        certificates = self.search([('ro_health_certificate_expiration_date', '>=', today), ('ro_health_certificate_expiration_date', '<=', expiration_date_limit)])

        for certificate in certificates:
            # Check if there is already an activity for this certificate
            existing_activity = self.env['mail.activity'].search([
                ('res_model', '=', 'hr.employee'),
                ('res_id', '=', certificate.id),
            ])

            if not existing_activity:
                # Create a new activity for each user
                for user in users:
                    activity = self.env['mail.activity'].create({
                        'activity_type_id': self.env.ref('mail.mail_activity_data_todo').id,
                        # 'summary': f'Health Certificate Expiration: {certificate.name,certificate.ro_health_certificate_expiration_date}',
                        'summary': f'Health Certificate Expiration: {certificate.name}, {certificate.ro_health_certificate_expiration_date}',
                        'user_id': user.id,
                        'res_model': 'hr.employee',
                        'res_id': certificate.id,
                        # 'date_deadline': certificate.ro_health_certificate_expiration_date,
                        'date_deadline':today,
                        'res_model_id': self.env.ref('hr.model_hr_employee').id,
                    })
