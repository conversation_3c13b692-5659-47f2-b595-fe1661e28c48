<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="ir_cron_notification_activity_to_hr" model="ir.cron">
        <field name="name">Automatic Create Activity to Hr</field>
        <field name="interval_number">1</field>
        <field name="interval_type">months</field> <!-- Changed to 'months' -->
        <field name="numbercall">-1</field>
        <field name="nextcall" eval="(DateTime.now() + timedelta(days=30)).strftime('%Y-%m-%d 02:00:00')" /> <!-- Changed to add 30 days -->
        <field name="model_id" ref="hr.model_hr_employee"/> <!-- Changed to correct model reference -->
        <field name="state">code</field>
        <field name="code">model.create_activities()</field>
        <field name="active" eval="True"/>
    </record>
</odoo>
