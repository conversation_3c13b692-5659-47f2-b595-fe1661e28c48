<?xml version="1.0" encoding="UTF-8"?>
<odoo>
  <template id="req_contract_report_template">
    <t t-call="web.html_container">
      <t t-call="web.external_layout">
        <div class="page" style="direction: rtl; text-align: right;">
          <h2 style="text-align: center;">طلب فتح حساب مرتبات</h2>
          <p style="text-align: right;">تحريراً فى: .......... / .......... / ..........</p>
          <h2 style="text-align: right;"> السادة / بنك CIB </h2>
          <h2 style="text-align: right;"> تحية طيبة وبعد؛ </h2>
          <h2 style="text-align: right;"> برجاء التكرم بفتح حساب مرتبات</h2>

          <table class="table table-sm mt-4" style="width: 100%;">
            <t t-foreach="docs" t-as="doc">
              <tr>
                <th style="text-align: right;">للاستاذ:</th>
                <td t-esc="doc.name" style="text-align: right;"/>
              </tr>
              <tr>
                <th style="text-align: right;">الرقم القومي:</th>
                <td t-esc="doc.employee_id.identification_id or 'N/A'" style="text-align: right;"/>
              </tr>
              <tr>
                <th style="text-align: right;">الوظيفة:</th>
                <td t-esc="doc.job_id.name" style="text-align: right;"/>
              </tr>
              <tr>
                <th style="text-align: right;">الراتب الشهري:</th>
                <td t-esc="doc.ro_net_income if doc.ro_net_income >= 5000 else 5000"/>
              </tr>
              <tr>
                <th style="text-align: right;"> تاريخ العمل:</th>
                <td t-esc="doc.date_start"/>
              </tr>
            </t>
          </table>

          <p>
علما بأن حساب الشركة هو 100024861176  
وبرجاء خصم مصاريف فتح الحساب من حساب الشركة .
          </p>
          <p style="text-align: center;">
            وتفضلوا بقبول فائق الاحترام والتقدير؛؛؛؛
          </p>

          <p style="text-align: right;">رئيس مجلس الإدارة </p>
        </div>
      </t>
    </t>
  </template>

  <report id="report_employee_req_salary_acc" model="hr.contract" string="طلب فتح حساب مرتبات" report_type="qweb-pdf" name="ro_print_open_salary_acc.req_contract_report_template" file="ro_print_open_salary_acc.req_contract_report_template" />
</odoo>
