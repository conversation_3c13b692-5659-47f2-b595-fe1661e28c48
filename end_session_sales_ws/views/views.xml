<odoo>
    <data>
        <record id="account_payment_inherit_closed" model="ir.ui.view">
            <field name="name">Account payment inherit closed</field>
            <field name="model">account.payment</field>
            <field name="inherit_id" ref="account.view_account_payment_form"/>
            <field name="arch" type="xml">
                <xpath expr="//group[@name='group2']" position="inside">
                    <field name='py_end_session_transfer_id' attrs="{'invisible': [('py_end_session_transfer_id', '=', False)]}"/>
                </xpath>
            </field>
        </record>
        <record id="view_res_users_form_inherited" model="ir.ui.view">
            <field name="name">view.res.users.form.inherit</field>
            <field name="model">res.users</field>
            <field name="inherit_id" ref="base.view_users_form"/>
            <field name="arch" type="xml">
              <xpath expr="//div[@class='oe_title']//field[@name='partner_id']" position="after">
                  <field name="allowed_group_payment" widget="many2many_tags"/>
              </xpath>
            </field>
        </record>
        <record id="view_pos_payment_form_closed" model="ir.ui.view">
            <field name="model">pos.payment</field>
            <field name="inherit_id" ref="point_of_sale.view_pos_payment_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='session_id']" position="after">
                    <field name='py_end_session_transfer_id' attrs="{'invisible': [('py_end_session_transfer_id', '=', False)]}"/>
                </xpath>
            </field>
        </record>

        <record id="account_journal_inherit_closed" model="ir.ui.view">
            <field name="name">Account journal inherit closed</field>
            <field name="model">account.journal</field>
            <field name="inherit_id" ref="account.view_account_journal_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='type']" position="after">
                    <field name="is_main_treasury" attrs="{'invisible': [('type', '!=', 'cash')]}"/>
                    <field name="is_sub_treasury" attrs="{'invisible': [('type', '!=', 'cash')]}"/>
                    <field name="is_end_session"/>
                </xpath>
            </field>
        </record>

        <record id="wizard_end_session_form_view" model="ir.ui.view">
        <field name="name">end.session.form.view</field>
        <field name="model">end.session</field>
        <field name="arch" type="xml">
            <form>
                <group>
                    <group string="Amount">
                        <field name="is_move_created" invisible="1"/>
                        <field name="aw_cash_amount"/>
                        <field name="aw_visa_amount"/>
                    </group>
                    <group string="Actual">
                        <field name="aw_actual" attrs="{'readonly': [('is_move_created','=',True)]}"/>
                    </group>
                    <group string="Total" >
                        <field name="total_cash"/>
                        <field name="total_visa"/>
                    </group>
                    <field name="note" attrs="{'readonly': [('is_move_created','=',True)]}"/>
                </group>
                <footer>
                    <button name="post_cash_to_treasry" string="Post" type="object" class="btn-primary" attrs="{'invisible': [('is_move_created','=',True)]}"/>
                    <button name="%(end_session_sales_ws.action_report_end_session_sales)d" string="Print" type="action" class="btn-primary" attrs="{'invisible': [('is_move_created','=',False)]}"/>
                    <button special="cancel" string="Cancel" class="oe_link"/>
                </footer>
            </form>
        </field>
    </record>

<!--        <record id="end_sesion_form_vieww" model="ir.ui.view">-->
<!--            <field name="name">end_sesion_view</field>-->
<!--            <field name="model">end.session</field>-->
<!--            <field name="arch" type="xml">-->
<!--                <form>-->
<!--                    <sheet>-->
<!--                    <field name="is_move_created" invisible="0"/>-->
<!--                        <group col='4'>-->
<!--                            <group string="MS" >-->
<!--                                <field name="si_cash_amount"/>-->
<!--                                <field name="si_visa_amount"/>-->
<!--                            </group>-->
<!--                            <group string="AW" >-->
<!--                                <field name="aw_cash_amount"/>-->
<!--                                <field name="aw_visa_amount"/>-->
<!--                            </group>-->
<!--                            <group string="Total" >-->
<!--                                <field name="total_cash"/>-->
<!--                                <field name="total_visa"/>-->
<!--                            </group>-->
<!--                        </group>-->

<!--                        <group string="Actual" >-->
<!--                            <field name="si_actual" attrs="{'readonly': [('is_move_created','=',True)]}"/>-->
<!--                            <field name="aw_actual" attrs="{'readonly': [('is_move_created','=',True)]}"/>-->
<!--                        </group>-->

<!--                        <field name="note" attrs="{'readonly': [('is_move_created','=',True)]}"/>-->
<!--                        <field name="is_move_created" invisible='1'/>-->
<!--                    -->
<!--                    <footer>-->
<!--                        <button name="post_cash_to_treasry" string="Post" type="object" class="oe_highlight" attrs="{'invisible': [('is_move_created','=',True)]}"/>-->
<!--                        <button name="%(end_session_sales.action_report_end_session_sales)d" string="Print" type="action" class="oe_highlight" attrs="{'invisible': [('is_move_created','=',False)]}"/>-->
<!--                        <button string="Cancel" class="oe_highlight" special="cancel" />-->
<!--                    </footer>-->
<!--                    </sheet>-->
<!--                </form>-->
<!--            </field>-->
<!--        </record>-->

        <record model="ir.actions.act_window" id="action_end_sales_session">
            <field name="name">End Session</field>
            <field name="res_model">end.session</field>
            <field name="view_mode">form</field>
<!--            <field name="view_id" ref="end_sesion_form_view"/>-->
            <field name="target">new</field>
        </record>
        
        <menuitem id="menu_end_sales_session_payments" name="End&amp;Close session" 
            action="action_end_sales_session" parent="send_receive_request.menu_in_out_payment"
            sequence="500"/>


        <record id="action_whole_sale_orders_end_session" model="ir.actions.act_window">
            <field name="name">End Session Orders</field>
            <field name="res_model">account.payment</field>
            <field name="view_mode">tree,form</field>
            <!-- <field name="search_view_id" ref="view_whole_sales_filter"/> -->
            <field name="context">{'create':False}</field>
            <field name="domain">[('is_end_session','=',True)]</field>
        </record>

        <!-- <record model="ir.actions.act_window.view" id="action_whole_sale_orders_transactions_tree">
            <field name="sequence" eval="1"/>
            <field name="view_mode">tree</field>
            <field name="view_id" ref="whole_sale.view_transactions_order_tree"/>
            <field name="act_window_id" ref="action_whole_sale_orders_end_session"/>
        </record>
    
        <record model="ir.actions.act_window.view" id="action_whole_sale_orders_transactions_form">
            <field name="sequence" eval="2"/>
            <field name="view_mode">form</field>
            <field name="view_id" ref="account.view_account_payment_form"/>
            <field name="act_window_id" ref="action_whole_sale_orders_end_session"/>
        </record> -->

<!--        <menuitem id="menu_whole_sale_order_end_session"-->
<!--            name="End Session Orders"-->
<!--            action="action_whole_sale_orders_end_session"-->
<!--            parent="whole_sale.whole_sale_menu_payments"-->
<!--            sequence="100" groups="base.group_system"/>-->
    

    </data>
</odoo>
