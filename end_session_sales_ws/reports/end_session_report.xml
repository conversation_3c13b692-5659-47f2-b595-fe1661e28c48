<odoo>
    <data>

        <record id="paperformat_portrait" model="report.paperformat">
            <field name="name">Session Receipt</field>
            <field name="default" eval="True"/>
            <field name="format">custom</field>
            <field name="page_height">105</field>
            <field name="page_width">74</field>
            <field name="orientation">Portrait</field>
            <field name="margin_top">0</field>
            <field name="margin_bottom">0</field>
            <field name="margin_left">0</field>
            <field name="margin_right">0</field>
            <field name="header_line" eval="False"/>
            <field name="header_spacing">0</field>
            <field name="dpi">100</field>
        </record>

        <template id="custom_external_layout_standard">
            <t t-if="not company">
                <!-- Multicompany -->
                <t t-if="company_id">
                    <t t-set="company" t-value="company_id"/>
                </t>
                <t t-elif="o and 'company_id' in o">
                    <t t-set="company" t-value="o.company_id.sudo()"/>
                </t>
                <t t-else="else">
                    <t t-set="company" t-value="res_company"/>
                </t>
            </t>
            <div class="article o_report_layout_standard">
                <t t-raw="0"/>
            </div>
        </template>


        <template id="print_end_session_template">
            <t t-call="web.html_container">
                <t t-call="end_session_sales_ws.custom_external_layout_standard">
                    <div class="page" style="font_size:16pt !important;">
                        <t t-foreach="docs" t-as="doc">
                            <div class="col-md-6">
                                <br/>
                                <center>
                                    <u>تقفيل</u>
                                </center>
                                <br/>
                                <div style="font-size: 12px;direction: rtl;">
                                    <div style="float:right;width:50%">
                                        <p style="text-align: right; display:flex; flex-direction: column; align-items: center;margin-bottom: 0">

                                            تاريخ الإقفال:
                                            <span t-field="doc.create_date"/>
                                            <br/>
                                        </p>
                                    </div>
                                    <div style="float:left;width:50%">
                                        <p style="text-align: right; display:flex; flex-direction: column; align-items: center">
                                            
                                            الموظف:
                                            <span t-field="doc.user_id.name"/>
                                            <br/>
                                        </p>
                                    </div>
                                </div>

                                <br />
                                <br />

                                <center>
                                    <u></u>
                                </center>
                                <table class="table table-bordered"
                                        style="table-layout: fixed;width:100%;direction: rtl;font-size: 12px;float: left;">
                                    <tr>
                                        <td style="text-align: center;">مبلغ نقدي للنظام</td>
                                        <td style="text-align:right;">
                                            <span t-field="doc.aw_cash_amount"/>
                                        </td>


                                        <td style="text-align: center;">مبلغ فيزا للنظام</td>
                                        <td style="text-align:right;">
                                            <span t-field="doc.aw_visa_amount"/>
                                        </td>

                                    </tr>
                                </table>
                                
                                <br />
                                <br />
                                <center>
                                    <u>Total</u>
                                </center>
                                <table class="table table-bordered"
                                        style="table-layout: fixed;width:100%;direction: rtl;font-size: 12px;float: left;">
                                    <tr>
                                        <td style="text-align: center;">Total cash</td>
                                        <td style="text-align:right;">
                                            <span t-field="doc.total_cash"/>
                                        </td>


                                        <td style="text-align: center;">Total visa</td>
                                        <td style="text-align:right;">
                                            <span t-field="doc.total_visa"/>
                                        </td>

                                    </tr>
                                </table>

                                <center>
                                    <u>Actual</u>
                                </center>
                                <table class="table table-bordered"
                                        style="table-layout: fixed;width:100%;direction: rtl;font-size: 12px;float: left;">
                                    <tr>
                                        <td style="text-align: center;"> Cash</td>
                                        <td style="text-align:right;">
                                            <span t-field="doc.aw_actual"/>
                                        </td>

                                    </tr>
                                </table>
                                
                            </div>
                        </t>
                    </div>
                </t>
            </t>
        </template>


        <record id="action_report_end_session_sales" model="ir.actions.report">
            <field name="name">End Session Receipt</field>
            <field name="model">end.session</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">end_session_sales_ws.print_end_session_template</field>
            <field name="report_file">end_session_sales_ws.print_end_session_template</field>
            <field name="paperformat_id" ref="paperformat_portrait" />
            <field name="binding_model_id" ref="model_end_session" />
            <field name="binding_type">report</field>
        </record>
    </data>
</odoo>