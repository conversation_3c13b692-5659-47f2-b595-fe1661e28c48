<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">

        <!-- <record id="product_stock_move_report_stock_qty" model="ir.rule">
            <field name="name">product.stock.move.report.stock.qty</field>
            <field name="model_id" ref="model_stock_move_location"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
            <field name="groups" eval="[(4, ref('stock.group_stock_user')),(4, ref('stock.group_stock_manager'))]"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_unlink" eval="0"/>
            <field name="perm_read" eval="1"/>
        </record> -->

    </data>

</odoo>
