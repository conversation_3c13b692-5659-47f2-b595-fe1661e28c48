# -*- coding: utf-8 -*-

from datetime import datetime

from odoo import models, fields, api, _, SUPERUSER_ID
from odoo.exceptions import ValidationError, UserError


class AccountPayment(models.Model):
    _inherit = 'account.payment'

    is_end_session = fields.Boolean()
    is_closed = fields.Boolean(default=False, readonly=True, copy= False)

    py_end_session_transfer_id = fields.Many2one(
        string='End Session Entry',
        comodel_name='account.move',
        ondelete='restrict',
        readonly=True,
        copy= False
    )


class PosPayment(models.Model):
    _inherit = 'pos.payment'

    is_closed = fields.Bo<PERSON>an(default=False, readonly=True, copy= False)

    py_end_session_transfer_id = fields.Many2one(
        string='End Session Entry',
        comodel_name='account.move',
        ondelete='restrict',
        readonly=True,
        copy= False
    )


class AccountAccount(models.Model):
    _inherit = 'account.journal'

    is_sub_treasury = fields.Boolean(default=False, string="Sub Branch")
    is_main_treasury = fields.Boolean(default=False, string="Treasury Account")
    is_end_session = fields.Boolean(default=False, string="Is End Session Journal")
    user_allowed_id = fields.Many2one("res.users")

class ResUsers(models.Model):
    _inherit = 'res.users'

    allowed_group_payment = fields.Many2many('account.journal', "user_allowed_id")

class EndSession(models.TransientModel):
    _name = 'end.session'
    _description = 'End Session'

    aw_cash_journal = fields.Many2one('account.journal')

    aw_cash_amount = fields.Float(
        ' System Cash Amount', digits='Product Price', readonly=True)
    aw_visa_amount = fields.Float(
        ' System Visa Amount', digits='Product Price', readonly=True)

    total_cash = fields.Float('Total Cash', digits='Product Price', readonly=True)
    total_visa = fields.Float('Total Visa', digits='Product Price', readonly=True)

    is_move_created = fields.Boolean()
    note = fields.Char()
    
    user_id = fields.Many2one('res.users')

    aw_actual = fields.Float('Actual')


    @api.model
    def default_get(self, default_fields):
        # OVERRIDE
        # Add a flag meant to predict the account when the move.line change.
        # Don't set a default account. Let the prediction do this job.
        values = super(EndSession, self).default_get(default_fields)

        aw_cash_account_id = 0
        cash_journal = self.env.user.allowed_group_payment.filtered(lambda x:x.type=="cash" and x.is_sub_treasury)
        user_main_treasury = self.env.user.allowed_group_payment.filtered(lambda x:x.type=="cash" and x.is_main_treasury)
        # print()
        if len(cash_journal) != 1:
            raise ValidationError("There are no Cash Journal found in your team check (is cash) on journal groups or Sub Treasury must be one ")

        if len(user_main_treasury) != 1:
            raise ValidationError("There are no Cash Journal found in your team check (is cash) on journal groups or Main Treasury must be one ")

        res_aw = self.env['account.payment'].search(
            [('user_id','in',self.env.user.ids),('is_closed', '=', False),
             ('state', '=', 'posted'),('journal_id', '=', cash_journal.id)])

        res_pos_aw = self.env['pos.payment'].with_user(SUPERUSER_ID).search(
            [('session_id.user_id', 'in', self.env.user.ids), ('is_closed', '=', False)])

        aw_bank_out_ids = aw_bank_in_ids = 0
        cash_amount_aw = visa_amount_aw = 0
        values['note'] = "No payment to collect"
 
        if len(res_aw) > 0:
            cash_out_ids = res_aw.filtered(
                lambda x: x.journal_id.type == 'cash' and x.payment_type == 'outbound')
            cash_in_ids = res_aw.filtered(
                lambda x: x.journal_id.type == 'cash' and x.payment_type == 'inbound')

            bank_out_ids = res_aw.filtered(
                lambda x: x.journal_id.type == 'bank' and x.payment_type == 'outbound')
            bank_in_ids = res_aw.filtered(
                lambda x: x.journal_id.type == 'bank' and x.payment_type == 'inbound')

            cash_amount_aw += sum(cash_in_ids.mapped('amount')) - \
                sum(cash_out_ids.mapped('amount'))

            visa_amount_aw += sum(bank_in_ids.mapped('amount')) - \
                sum(bank_out_ids.mapped('amount'))

        # if (len(res_aw) > 0 or len(res_pos_aw) > 0) :
        values['aw_cash_amount'] = cash_amount_aw
        values['aw_visa_amount'] = visa_amount_aw
        values['aw_cash_journal'] = cash_journal.id

        values['total_cash'] = cash_amount_aw
        values['total_visa'] = visa_amount_aw

        values['aw_actual'] = cash_amount_aw

        values['user_id'] = self.env.user.id
        values['note'] = ''

        return values

    def post_cash_to_treasry(self):

        aw_move = False
        if (self.aw_cash_amount != 0) and not self.is_move_created:
            aw_end_amt = self.aw_actual 
            # - self.aw_cash_amount 

            ###Create Entry###
            main_treasury_journal = self.env.user.allowed_group_payment.filtered(
                lambda x: x.type == "cash" and x.is_main_treasury)
            # main_treasury_journal = self.env['account.journal'].search([('is_main_treasury', '=', True)])

            if not main_treasury_journal or len(main_treasury_journal) > 1:
                raise UserError(
                    _('There are no treasury account or more than 1.'))

            if len(main_treasury_journal) == 1:
                # cash_ids = aw_cash_in_ids if aw_cash_in_ids else aw_cash_out_ids

                ref = f"End or Close Session {main_treasury_journal.ro_crm_team_id.name}"

                # end_session_journal = self.env['account.journal'].search([('is_end_session', '=', True)])
                end_session_journal = self.env.user.allowed_group_payment.filtered(
                    lambda x: x.is_end_session)
                if not end_session_journal or len(end_session_journal) > 1:
                    raise UserError(_('There are no End Session or more than 1.'))
                values = {
                    'name': '/',
                    'date': fields.Date.today(),
                    'ref': ref,
                    'journal_id': end_session_journal.id,
                    'line_ids': [(0, 0, {
                        'name': ref,
                        'debit': self.aw_cash_amount,
                        'account_id': main_treasury_journal.default_account_id.id,
                        'partner_id': self.env.user.partner_id.id
                    }), (0, 0, {
                        'name': ref,
                        'credit': self.aw_cash_amount,
                        'account_id': self.aw_cash_journal.default_account_id.id,
                        'partner_id': self.env.user.partner_id.id
                    })]
                }

                aw_move = self.env['account.move'].create(values)
                aw_move.action_post()

                res_aw = self.env['account.payment'].search(
                    [('user_id','in',self.env.user.ids),('is_closed', '=', False)])

                if len(res_aw) > 0:
                    res_aw.is_closed = True
                    res_aw.py_end_session_transfer_id = aw_move
        
        if aw_move:
            self.is_move_created = True

        return {
            'context': self.env.context,
            'view_mode': 'form',
            'res_model': 'end.session',
            'res_id': self.id,
            'type': 'ir.actions.act_window',
            'target': 'new'
        }
