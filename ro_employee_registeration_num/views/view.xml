<?xml version='1.0' encoding='utf-8'?>
<odoo>
    <data>
        <record id="view_hr_employee_filter_inherit_register" model="ir.ui.view">
            <field name="name">hr.employee.registeration.view.filter.inherit</field>
            <field name="model">hr.employee</field>
            <field name="inherit_id" ref="hr.view_employee_filter"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='name']" position="attributes">
                    <attribute name="filter_domain">['|', '|', '|', ('registration_number', 'ilike', self), ('work_email', 'ilike', self), ('name', 'ilike', self), ('arabic_name', 'ilike', self)]</attribute>
                </xpath>
            </field>
        </record>

        <record id="view_hr_employee_form_inherit_register" model="ir.ui.view">
            <field name="name">hr.employee.registeration.view.form.inherit</field>
            <field name="model">hr.employee</field>
            <field name="inherit_id" ref="hr_payroll.payroll_hr_employee_view_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='registration_number']" position="attributes">
                    <attribute name="readonly">1</attribute>
                    <attribute name="force_save">1</attribute>
                </xpath>
            </field>
        </record>

        <record id="view_hr_contract_history_filter_inherit_register" model="ir.ui.view">
            <field name="name">hr.contract.history.registeration.view.filter.inherit</field>
            <field name="model">hr.contract.history</field>
            <field name="inherit_id" ref="hr_contract.hr_contract_history_view_search"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='name']" position="attributes">
                    <attribute name="filter_domain">['|', '|', ('employee_id.registration_number', 'ilike', self), ('employee_id.arabic_name', 'ilike', self), ('name', 'ilike', self)]</attribute>
                </xpath>
            </field>
        </record>

    </data>
    
</odoo>