<odoo>
    

    <record id="moves_report_wizard_form_view" model="ir.ui.view">
        <field name="name">moves_report_wizard view</field>
        <field name="model">wizard.product.stock.moves</field>
        <field name="arch" type="xml">
            <form string="Product Moves">
                <group>
                    <field name="product_ids" widget='many2many_tags'/>
                    <field name="location_ids" widget='many2many_tags'/>
                </group>
                <group>
                    <group>
                        <field name="date_from"/>
                    </group>
                    <group>
                        <field name="date_to"/>
                    </group>

                </group>
                <footer>
                    <button name="action_get_data" string="Confirm" type="object" class="oe_highlight"/>
                    <button string="Cancel" class="oe_highlight" special="cancel" />
                </footer>
            </form>
        </field>
    </record>
    <record model="ir.actions.act_window" id="action_moves_report_wizard">
        <field name="name">Product Moves</field>
        <field name="res_model">wizard.product.stock.moves</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="moves_report_wizard_form_view"/>
        <field name="target">new</field>
    </record>

    <menuitem id="menu_moves_report_wizard" name="Product Moves"
     groups="product_stock_move_qty.group_product_move_report"
     action="action_moves_report_wizard"
     parent="stock.menu_warehouse_report" sequence="159"/>

     
</odoo>