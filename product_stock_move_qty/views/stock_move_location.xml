<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data>

        <record id="stock_move_location_view_search" model="ir.ui.view">
            <field name="name">StockMoveLocationSearch</field>
            <field name="model">stock.move.location</field>
            <field name="arch" type="xml">
                <search string="Stock Moves">
                    <field name="product_id"/>
                    <field name="location_id" string="Location"/>
                    <field name="date"/>
                    <field name="location_name_to" string="Location To"/>
                    <field name="location_name_from" string="Location From"/>
                    <field name="warehouse_id" string="Warehouse"/>
                    
                    <filter string="Current Month" name="filter_current_month"  domain="[('date','&lt;',(datetime.date.today()+relativedelta(months=1)).strftime('%%Y-%%m-01')), ('date','&gt;=',datetime.date.today().strftime('%%Y-%%m-01'))]"/>
                    <filter string="Prev Month" name="filter_prev_month"  domain="[('date','&gt;=',(datetime.date.today()-relativedelta(months=1)).strftime('%%Y-%%m-01')), ('date','&lt;',datetime.date.today().strftime('%%Y-%%m-01'))]"/>

                    <filter string="Location" name="group_by_location" icon="terp-gtk-jump-to-rtl" domain="[]" context="{'group_by':'location_id'}"/>
                    <separator orientation="vertical"/>
                    <filter string="Category" name="group_by_category" icon="terp-gtk-jump-to-rtl" domain="[]" context="{'group_by':'categ_id'}"/>
                    <separator orientation="vertical"/>
                    <filter string="Product" name="group_by_product" icon="terp-accessories-archiver" domain="[]"  context="{'group_by':'product_id'}"/>
                    <separator orientation="vertical"/>
                    <filter string="Company" name="group_by_company" icon="terp-accessories-archiver" domain="[]"  context="{'group_by':'company_id'}"/>
                </search>
            </field>
        </record>

        <record id="filter_location_product" model="ir.filters">
            <field name="name">Location</field>
            <field name="model_id">stock.move.location</field>
            <field name="context">{'group_by': ['location_id', 'product_id']}</field>
        </record>

        <record id="filter_product_productlo" model="ir.filters">
            <field name="name">Product</field>
            <field name="model_id">stock.move.location</field>
            <field name="context">{'group_by': ['product_id', 'location_id']}</field>
        </record>

        <record id="stock_move_location_view_form" model="ir.ui.view" >
            <field name="name">StockMoveLocationViewForm</field>
            <field name="model">stock.move.location</field>
            <field name="arch" type="xml">
                <form string="Location Product Moves">
                    <group>
                        <field name="date"/>
                        <field name="description"/>
                        <field name="picking_id"/>
                        <field name="warehouse_id"/>
                        <field name="location_id"/>
                        <field name="move_type"/>
                    </group>
                    <group>
                        <field name="product_id"/>
                        <field name="categ_id"/>
                        <field name="uom_id"/>
                        <field name="qty_add"/>
                        <field name="qty_ded"/>
                        <field name="qty_current"/>
                    </group>
                </form>
            </field>
        </record>

        <record id="stock_move_location_view_tree" model="ir.ui.view">
            <field name="name">StockMoveLocationViewTree</field>
            <field name="model">stock.move.location</field>
            <field name="arch" type="xml">
                <tree string="Location Product Moves">
                    <field name="date"/>
                    <field name="product_id"/>
                    <field name="location_id" invisible="1"/>
                    <field name="location_name_from"/>
                    <field name="location_name_to"/>
                    <field name="move_type"/>
                    <field name="picking_id"/>
                    <field name="categ_id" invisible="1"/>
                    <field name="uom_id" invisible="1"/>
                    <field name="qty_add" sum="Qty Add"/>
                    <field name="qty_ded" sum="Qty Ded"/>
                    <field name="value" sum="Value" groups="account.group_account_manager"/>
                    <field name="total_value" sum="Total Value" groups="account.group_account_manager"/>
                    <field name="qty_current" sum="Qty Curr"/>
                    <field name="warehouse_id" invisible="1"/>
                </tree>
            </field>
        </record>

        <record id="product_stock_move_location_rule" model="ir.rule">
            <field name="name">product stock move location multi-company</field>
            <field name="model_id" ref="model_stock_move_location"/>
            <field eval="True" name="global"/>
            <field name="domain_force">['|',('company_id','=',False),('company_id', 'in', company_ids)]</field>
        </record>


        <!-- <record id="stock_move_location_action" model="ir.actions.act_window" >
            <field name="name">Product Moves Location</field>
            <field name="res_model">stock.move.location</field>
            <field name="view_mode">tree,form</field>
            <field name="context">{'group_by':['company_id','location_id', 'product_id']}</field>
            <field name="view_id" ref="stock_move_location_view_tree"/>
            <field name="search_view_id" ref="stock_move_location_view_search"/>
        </record> -->

        <record id="stock_move_location_action_all" model="ir.actions.act_window" >
            <field name="name">Product Moves Location</field>
            <field name="res_model">stock.move.location</field>
            <field name="view_mode">tree,form</field>
            <field name="context">{'group_by':['location_id', 'product_id']}</field>
            <field name="view_id" ref="stock_move_location_view_tree"/>
            <field name="search_view_id" ref="stock_move_location_view_search"/>
        </record>


        <record id="stock_move_location_action_product_moves" model="ir.actions.act_window" >
            <field name="name">Location Moves</field>
            <field name="res_model">stock.move.location</field>
            <field name="view_mode">tree,form</field>
            <field name="context">{'group_by':['location_id','product_id']}</field>
            <field name="domain">[('product_id', '=', active_id)]</field>
            <field name="view_id" ref="stock_move_location_view_tree"/>
            <field name="search_view_id" ref="stock_move_location_view_search"/>
        </record>

        <record id="stock_move_location_action_product_tmpl_moves" model="ir.actions.act_window" >
            <field name="name">Location Moves</field>
            <field name="res_model">stock.move.location</field>
            <field name="view_mode">tree,form</field>
            <field name="context">{'group_by':['location_id','product_id']}</field>
            <field name="domain">[('product_tmpl_id', 'in', [active_id])]</field>
            <field name="view_id" ref="stock_move_location_view_tree"/>
            <field name="search_view_id" ref="stock_move_location_view_search"/>
        </record>
        
        <record id="view_location_action_wizard_inherited" model="ir.ui.view">
            <field name="name">view.location_action_wizard.inherit</field>
            <field name="model">stock.move.location</field>
            <field name="inherit_id" ref="stock_move_location_view_tree"/>
            <field name="mode">primary</field>
            <field name="arch" type="xml">

                <xpath expr="//field[@name='location_name_from']" position="attributes">
                    <attribute name="optional">show</attribute>
                </xpath>
                <xpath expr="//field[@name='location_name_to']" position="attributes">
                    <attribute name="optional">show</attribute>
                </xpath>
                
                <xpath expr="//field[@name='move_type']" position="attributes">
                    <attribute name="optional">show</attribute>
                </xpath>
                <xpath expr="//field[@name='picking_id']" position="attributes">
                    <attribute name="optional">show</attribute>
                </xpath>
                
                <xpath expr="//field[@name='value']" position="after">
                    
                    <field name="location_available" optional="show"/>
                    <field name="available_qty" optional="show"/>
                    <field name="onhand_qty" optional="show"/>

                </xpath>

                <xpath expr="//field[@name='value']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                <xpath expr="//field[@name='total_value']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                <xpath expr="//field[@name='qty_current']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>

            </field>
        </record>

        <record id="view_location_action_info_inherited" model="ir.ui.view">
            <field name="name">view.location_action_info.inherit</field>
            <field name="model">stock.move.location</field>
            <field name="inherit_id" ref="stock_move_location_view_tree"/>
            <field name="mode">primary</field>
            <field name="arch" type="xml">

              <xpath expr="//field[@name='value']" position="attributes">
                <attribute name="invisible">1</attribute>
              </xpath>
              <xpath expr="//field[@name='total_value']" position="attributes">
                <attribute name="invisible">1</attribute>
              </xpath>
              <xpath expr="//field[@name='qty_current']" position="attributes">
                <attribute name="invisible">1</attribute>
              </xpath>

            </field>
        </record>
        <record id="stock_move_location_action_info" model="ir.actions.act_window" >
            <field name="name">Location Moves</field>
            <field name="res_model">stock.move.location</field>
            <field name="view_mode">tree,form</field>
            <!-- <field name="context">{'group_by':['company_id','location_id', 'product_id']}</field> -->
            <field name="view_id" ref="view_location_action_info_inherited"/>
            <field name="search_view_id" ref="stock_move_location_view_search"/>
        </record>
                

        <record model="ir.actions.server" id="product_moves_qty_domain_info">
            <field name="name">product moves qty domain</field>
            <field name="model_id" ref="model_stock_move_location"/>
            <field name="type">ir.actions.server</field>
            <field name="state">code</field>
            <field name="code">action = model.action_product_moves_qty_domain()</field>
        </record>

        <!-- <menuitem id="menu_action_stock_move_location_report" name="Stock Product Move" action="product_moves_qty_domain" sequence="160" parent=""/> -->
        
        <menuitem id="menu_action_stock_move_location_all_report" name="Stock Product Move" action="stock_move_location_action_all" sequence="160" parent="stock.menu_warehouse_report"/>
        
        <!-- without value or balance -->
        <menuitem id="menu_action_stock_moves_location_info_report" name="Location Moves" action="product_moves_qty_domain_info" sequence="165" parent="stock.menu_warehouse_report"/>
  </data>
</odoo>
