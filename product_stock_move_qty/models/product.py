# -*- coding: utf-8 -*-

from odoo import _, api, fields, models

class ProductTemplate(models.Model):
    _inherit = "product.template"
    def stock_move_location_action_product_tmpl(self):
        self.ensure_one()
        
        locations = self.env['stock.location'].search([('usage','=', 'internal')])
        current_user = self.env.user
        user_locations = locations.filtered(lambda x: current_user in x.warehouse_id.crm_team_id.member_ids)

        action = self.env["ir.actions.actions"]._for_xml_id("product_stock_move_qty.stock_move_location_action_product_moves")
        
        # action['context'] = {
        #         # 'search_default_filter_current_month':1 ,
        #         'search_default_group_by_location':1,
        #         'search_default_group_by_company':1
        #     }

        if self.env.user.has_group('base.group_system'):
            action['domain'] = [('product_id.product_tmpl_id', 'in', self.ids)]
        else:
            action['domain'] = [('product_id.product_tmpl_id', 'in', self.ids), ('location_id', 'in', user_locations.ids)]

        return action                                                                                                                                                                                                                 

class ProductProduct(models.Model):
    _inherit = "product.product"

    def stock_move_location_action_product(self):
        self.ensure_one()

        locations = self.env['stock.location'].search([('usage','=', 'internal')])
        current_user = self.env.user
        user_locations = locations.filtered(lambda x: current_user in x.warehouse_id.crm_team_id.member_ids)

        action = self.env["ir.actions.actions"]._for_xml_id("product_stock_move_qty.stock_move_location_action_product_tmpl_moves")
        # action['context'] = {
        #         # 'search_default_filter_current_month':1 ,
        #         'search_default_group_by_location':1,
        #         'search_default_group_by_company':1
        #     }
        # 'search_default_location_id':  user_locations[0].id if len(user_locations) > 0 else 1,

        if self.env.user.has_group('base.group_system'):
            action['domain'] = [('product_id.id', 'in', self.ids)]
        else:
            action['domain'] = [('product_id.id', 'in', self.ids), ('location_id', 'in', user_locations.ids)]
            

        return action
