# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError

class AccountMove(models.Model):

    _inherit = "account.move"

    delivery_employee_id = fields.Many2one(
        string='Delivery Employee',
        comodel_name='hr.employee',
        readonly=True,
        # compute='_compute_delivery_employee_id',
        store=True,
    )

    order_wd = fields.Selection(
        string='Order WD',
        selection=[('not_wd', 'Not-WD'), ('walk_in', 'Walk-in'), ('delivery', 'Delivery')],
        default='not_wd'
    )

    sale_employee_id = fields.Many2one(
        string='Sale Employee',
        comodel_name='hr.employee',
        readonly=True,
        copy=False
    )

    # @api.depends('line_ids')
    # def _compute_delivery_employee_id(self):
    #     for move in self:
    #         if len(move.line_ids.sale_line_ids.order_id):
    #             sale_order = move.line_ids.sale_line_ids.order_id[0]
    #             move.delivery_employee_id = sale_order.picking_ids.delivery_employee_id
    #         else:
    #             move.delivery_employee_id = False

    def copy(self, default=None):
        if self.env.user.has_group('account.group_account_manager'):
            return super().copy(default=default)
        else:
            raise UserError(_('Can\'t be duplicated'))