# -*- coding: utf-8 -*-

from datetime import datetime

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class AccountPaymentRegister(models.TransientModel):
    _inherit = "account.payment.register"

    def _create_payments(self):
        payment = super()._create_payments()

        if len(self.line_ids) > 0:
            sale_order = self.env['sale.order'].search([('invoice_ids', '=', self.line_ids[0].move_id.id)])

            if sale_order and sale_order.delivery_employee_id:
                sale_order.write({'delivery_date_in': datetime.now()})

        return payment