# -*- coding: utf-8 -*-
{
    'name': "Sales Order Employee",

    'summary': """
        Add sales/delivery employee on sales order""",

    'author': "Roaya",
    'website': "http://www.roayadm.com",

    # Categories can be used to filter modules in modules listing
    # Check https://github.com/odoo/odoo/blob/12.0/odoo/addons/base/data/ir_module_category_data.xml
    # for the full list
    'category': 'Sales',
    'version': '16.0',

    # any module necessary for this one to work correctly
    'depends': ['hr', 'sale','ro_auto_assign','delivery','partner_phone_search'],
    # pyramids_auto_assign', 'set_shipping_cost', 'sale_coupon
    'data': [
        'security/ir.model.access.csv',
        'views/employee.xml',
        'views/sale_team.xml',
        'views/sale_order.xml',
        'views/stock_picking.xml',
        'views/account_move.xml',
        'views/choose_delivery_carrier.xml',
        'wizard/delivery_wizard.xml'
    ],
}
