<odoo>
    <data>
        <record id="vpicktree_tree_delivery" model="ir.ui.view">
            <field name="model">stock.picking</field>
            <field name="inherit_id" ref="stock.vpicktree"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='origin']" position="after">
                    <field name='area'  invisible="1"/>
                    <field name='delivery_employee_id' optional="show" options='{"no_create":True, "no_create_edit": True, "no_open": True}'/>
                </xpath>
                <xpath expr="//button[@name='do_unreserve']" position="after">
                    <button name="py_assign_delivery" type="object" string="Assign Delivery"/>
                </xpath>
            </field>
        </record>
        <record id="view_picking_form_deliver_inherit" model="ir.ui.view">
            <field name="model">stock.picking</field>
            <field name="inherit_id" ref="stock.view_picking_form"/>
            <field name="arch" type="xml">
                <xpath expr="//group[1]/group[1]" position="inside">
                    <field name='area' invisible="1"/>
                    <field name='order_wd' invisible="1"/>
                    <field name='delivery_employee_id' force_save="1" options='{"no_create":True, "no_create_edit": True, "no_open": True}'/>
<!--                    <field name='delivery_barcode' password="1" force_save="1" attrs="{'readonly':[('state','not in',('draft', 'assigned'))]}"/>-->
<!--                    <field name='processing_employee' attrs="{'readonly':[('state','!=','assigned')]}"/>-->
                </xpath>
            </field>
        </record>
        <act_window
            id="assign_delivery_action"
            name="Assign Delivery"
            res_model="assign.delivery"
            binding_model="stock.picking"
            view_mode="form" target="new"/>
    </data>
</odoo>