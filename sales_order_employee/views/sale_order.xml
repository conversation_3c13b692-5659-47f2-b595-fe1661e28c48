<odoo>
    <data>
        <record id="view_order_form_employee_inherit" model="ir.ui.view">
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale.view_order_form"/>
            <field eval="17" name="priority"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='partner_id']" position="after">
                    <field name='barcode' attrs="{'readonly':[('state','not in',('draft','sent'))]}"/>
                    <field name='barcode_stored' invisible="1"/>
                    <field name='area' invisible="1"/>
                    <field name='order_wd' invisible="1"/>
                    <field name='delivery_barcode' force_save="1" attrs="{'readonly':[('state','not in',('draft','sent'))]}"/>
<!--                    <field name='delivery_date_out'/>-->
<!--                    <field name='delivery_date_in'/>-->
                    <field name='delivery_duration'/>
                </xpath>
                <xpath expr="//field[@name='validity_date']" position="before">
                    <field name='sale_employee_id' force_save="1" required='0'
                    options="{'no_create':True, 'no_create_edit': True, 'no_open': True}"/>
                    <field name='delivery_employee_id' force_save="1" options="{'no_create':True, 'no_create_edit': True, 'no_open': True}"/>
                </xpath>
                <xpath expr="//field[@name='phone']" position="after">
                    <field name="user_id" widget="many2one_avatar_user"/>
                    <field name="team_id" options="{'no_create': True}"/>
                </xpath>
                <xpath expr="//group[@name='sales_person']//field[@name='user_id']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
                <xpath expr="//group[@name='sales_person']//field[@name='team_id']" position="attributes">
                    <attribute name="invisible">1</attribute>
                </xpath>
            </field>
        </record>
    </data>
</odoo>