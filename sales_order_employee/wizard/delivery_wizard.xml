<odoo>
    <data>
        <record model="ir.ui.view" id="action_assign_delivery_wizard_view">
            <field name="name">assign.delivery.form</field>
            <field name="model">assign.delivery</field>
            <field name="arch" type="xml">
                <form string="Assign Delivery">
                    <group colspan="4" col="4">
                        <field name="delivery_employee_id" force_save="1" options='{"no_create":True, "no_create_edit": True, "no_open": True}'/>
                        <field name="delivery_barcode" password="1"/>
                    </group>
                    <footer>
                        <button string="Assign" name="action_assign_delivery" type="object" class="oe_highlight"/>
                        or
                        <button string="Cancel" class="oe_link" special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>
    
        <record id="action_assign_delivery_wizard" model="ir.actions.act_window">
            <field name="name">Assign Delivery</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">assign.delivery</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
        </record>
    
    </data>
</odoo>