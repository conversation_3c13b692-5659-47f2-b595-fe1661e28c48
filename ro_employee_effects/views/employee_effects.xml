<odoo>
  <data>
    
    <record id="view_employee_effects_search" model="ir.ui.view">
      <field name="name">Employee Effects Search</field>
      <field name="model">employee.effects</field>
      <field name="arch" type="xml">
        <search string="Effects">
          <field name="employee_id" filter_domain="['|', ('employee_id.registration_number', 'ilike', self), ('employee_id', 'ilike', self)]"/>
          <field name="name"/>
          <field name="time_off_id" />
        </search>
      </field>
    </record>

    <record id="view_employee_effects_tree" model="ir.ui.view">
      <field name="name">Employee Effects</field>
      <field name="model">employee.effects</field>
      <field name="arch" type="xml">
        <tree string="Effects">
          <field name="name"/>
          <field name="employee_id" />
          <field name="time_off_id" />
          <field name="date" />
          <field name="duration_type"/>
        </tree>
      </field>
    </record>

    <record id="view_employee_effects_form" model="ir.ui.view">
      <field name="name">Employee Effects</field>
      <field name="model">employee.effects</field>
      <field name="arch" type="xml">
        <form>
          <sheet>
            <div>
              <h1>
                  <field name="name" placeholder="Name..."/>
              </h1>
            </div>

            <group>
              <group>
                <field name='employee_id' />
                <field name='time_off_id' />
                <field name='date' />

                <label for="duration"/>
                <div class="o_row">
                  <field name="duration"/>
                  <field name="duration_type"/>
                </div> 

                <field name='note' />
              </group>
              <group>
              </group>
            </group>


          </sheet>
          <div class="oe_chatter">
            <field name="message_follower_ids" widget="mail_followers" groups="base.group_user"/>
            <field name="activity_ids" widget="mail_activity"/>
            <field name="message_ids" widget="mail_thread"/>
          </div>
        </form>
      </field>
    </record>

    
    <!-- Employee Effects -->
    <record id="employee_effects_action_window" model="ir.actions.act_window">
      <field name="name">Employee Effects</field>
      <field name="res_model">employee.effects</field>
      <field name="type">ir.actions.act_window</field>
      <field name="view_mode">tree,form</field>
      <field name="help" type="html">
        <p class="o_view_nocontent_smiling_face">
          Create a Employee Effects Order
        </p>
      </field>
    </record>    
    <menuitem action="employee_effects_action_window" id="employee_effects_menu" name="Employee Effects" sequence="30" parent="hr_holidays.menu_hr_holidays_approvals"/>
    

  </data>
</odoo>