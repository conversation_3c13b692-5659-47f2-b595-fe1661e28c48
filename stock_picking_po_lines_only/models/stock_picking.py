from odoo import models, fields
from odoo.exceptions import ValidationError

class StockPicking(models.Model):
    
    _inherit = "stock.picking"
    
    def button_validate(self):
        self.ensure_one()
        if self.picking_type_code == 'incoming':
            invalid_moves = []
            for move in self.move_ids:
                if not move.purchase_line_id:
                    invalid_moves.append(move)
            if invalid_moves:
                raise ValidationError(f"Move lines: {[move.product_id.name for move in invalid_moves]} have no purchase.order.line")
        return super().button_validate()
