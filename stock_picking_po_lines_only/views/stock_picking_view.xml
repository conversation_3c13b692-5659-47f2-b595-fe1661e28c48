<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="view_picking_form_readonly_on_done" model="ir.ui.view">
            <field name="inherit_id" ref="stock.view_picking_form"/>
            <field name="model">stock.picking</field>
            <field name="arch" type="xml">
                <xpath expr="//sheet/div[@name='button_box']/button[@name='action_picking_move_tree']" position="attributes">
                    <attribute name="attrs">{'invisible': ['|', '&amp;', ('show_operations', '=', True), '|', ('is_locked', '=', True), ('state', '=', 'done'), '|', '&amp;', ('state', '=', 'done'), ('picking_type_code', '=', 'incoming'), '&amp;', ('state', '=', 'done'), ('is_locked', '=', True)]}</attribute>
                </xpath>
                <xpath expr="//field[@name='move_ids_without_package']" position="attributes">
                    <attribute name="attrs">{'readonly':['|', '&amp;', ('state', '=', 'done'), ('picking_type_code', '=', 'incoming'), '&amp;', ('state', '=', 'done'), ('is_locked', '=', True)]}</attribute>
                </xpath>
                <xpath expr="//field[@name='move_line_ids_without_package']" position="attributes">
                    <attribute name="attrs">{'readonly': ['|', '|', ('show_operations', '=', False), ('state', '=', 'cancel'), '|', '&amp;', ('state', '=', 'done'), ('picking_type_code', '=', 'incoming'), '&amp;', ('state', '=', 'done'), ('is_locked', '=', True)], 'invisible': [('show_reserved', '=', False)]}</attribute>
                </xpath>
                <xpath expr="//field[@name='move_line_nosuggest_ids']" position="attributes">
                    <attribute name="attrs">{'readonly': ['|', '|', ('show_operations', '=', False), ('state', '=', 'cancel'), '|', '&amp;', ('state', '=', 'done'), ('picking_type_code', '=', 'incoming'), '&amp;', ('state', '=', 'done'), ('is_locked', '=', True)], 'invisible': [('show_reserved', '=', True)]}</attribute>
                </xpath>
            </field>
        </record>
    </data>
</odoo>