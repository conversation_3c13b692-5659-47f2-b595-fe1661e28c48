<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">

    <t t-name="SaleOrderListInheret" t-inherit="pos_sale.SaleOrderList" t-inherit-mode="extension" owl="1">
        <xpath expr="//div[hasclass('order-list')]" position="replace">
            <div class="order-list">
                <t t-foreach="props.orders" t-as="order" t-key="order.id">
                    <t t-if="order.state != 'done' and 'cancel'">
                        <SaleOrderRow order="order" highlightedOrder="highlightedOrder" />

                    </t>
                </t>
            </div>
        </xpath>
    </t>
</templates>
