<?xml version="1.0" encoding="UTF-8"?>
<odoo>

    <record id="action_sale_order_call_center" model="ir.actions.act_window">
        <!-- <field name="type">ir.actions.act_window</field> -->
        <field name="name">Call Center</field>
        <field name="res_model">sale.order</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{ 'default_is_call_center': True}</field>
        <field name="domain">[('is_call_center','=',True)]</field>
    </record>

    <record id="view_sale_order_call_center_tree" model="ir.ui.view">
        <field name="name">sale.order.call.center.tree</field>
        <field name="model">sale.order</field>
        <field name="arch" type="xml">
            <tree string="Quotation" class="o_sale_order" multi_edit="1" sample="1">
                <field name="name" string="Number" readonly="1" decoration-bf="1"/>
                <field name="create_date" string="Creation Date" widget="date" optional="show"/>
                <field name="commitment_date" widget="date" optional="hide"/>
                <field name="expected_date" widget="date" optional="hide"/>
                <field name="partner_id" readonly="1"/>
                <field name="user_id" widget="many2one_avatar_user" optional="show"/>
                <field name="activity_ids" widget="list_activity" optional="show"/>
                <field name="team_id" optional="hide"/>
                <field name="tag_ids" optional="hide" widget="many2many_tags" options="{'color_field': 'color'}"/>
<!--                <field name="company_id" groups="base.group_multi_company" optional="show" readonly="1"/>-->
                <field name="company_id" optional="show" readonly="1"/>
                <field name="amount_untaxed" sum="Total Tax Excluded" widget="monetary" optional="hide"/>
                <field name="amount_tax" sum="Tax Total" widget="monetary" optional="hide"/>
                <field name="amount_total" sum="Total Tax Included" widget="monetary" decoration-bf="1" optional="show"/>
                <field name="state" decoration-success="state == 'sale' or state == 'done'" decoration-info="state == 'draft' or state == 'sent'" widget="badge" optional="show"/>
                <field name="invoice_status" optional="hide"/>
                <field name="message_needaction" invisible="1"/>
                <field name="currency_id" invisible="1"/>
                <field name="confirmation_date" optional="show"/>
                <field name="payment_date" force_save="1" readonly="1" optional="show"/>
            </tree>
        </field>
    </record>

    <record model="ir.ui.view" id="sale_order_group_inherited_view">
        <field name="name">sale.order.g.inherited.view</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='partner_id']" position="after">
                <field name="invoice_status" invisible="1"/>
                <field name="partner_invoice_id" invisible="1"/>
            </xpath>
            <xpath expr="//page[@name='other_information']" position="attributes">
                <attribute name="groups">sales_team.group_sale_manager</attribute>
            </xpath>
            <xpath expr="//page[@name='customer_signature']" position="attributes">
                <attribute name="groups">sales_team.group_sale_manager</attribute>
            </xpath>
        </field>
    </record>

    <record model="ir.ui.view" id="sale_order_optional_group_inherited_view">
        <field name="name">sale.order.optional.inherited.view</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale_management.sale_order_form_quote"/>
        <field name="arch" type="xml">
            <xpath expr="//page[@name='optional_products']" position="attributes">
                <attribute name="groups">sales_team.group_sale_salesman_all_leads</attribute>
            </xpath>
        </field>
    </record>

    <record model="ir.ui.view" id="sale_order_call_center_inherited_view">
        <field name="name">sale.order.call.center.inherited.view</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="mode">primary</field>
        <field name="priority">30</field>
        <field name="arch" type="xml">

            <xpath expr="//field[@name='partner_id']" position="after">
                <field name="is_call_center" invisible="1"/>
                <field name="is_assign" invisible="1"/>
                <field name="call_center_check" invisible="1"/>
            </xpath>

            <xpath expr="//header/button[1]" position="before">
                <button name="assign_to_sales" groups="employee_call_center.group_call_center" type="object" string="Assgin" attrs="{'invisible':[('is_assign' , '=', True)]}"/>
            </xpath>

            <xpath expr="//field[@name='order_line']" position="attributes">
                <attribute name="attrs">{'readonly': [('state', 'in', ('done','cancel'))]}</attribute>
            </xpath>

            <xpath expr="//button[@name='action_confirm'][2]" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//field[@name='payment_term_id']" position="after">
                <field name='call_center_employee_id' force_save="1" required='0'
                    options="{'no_create':True, 'no_create_edit': True, 'no_open': True}"/>
                <field name='call_center_barcode' required='0' password="1"
                    attrs="{'readonly': [('state', 'in', ('done'))]}"/>

                <field name='call_center_manager_code' password="1" states="draft"/>
            </xpath>
            
        </field>
    </record>

    <record model="ir.ui.view" id="sale_order_call_center_readonly">
        <field name="name">sale.order.call.center.inherited.view</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='partner_id']" position="after">
                <field name="is_call_center" invisible="1"/>
                <field name="is_assign" invisible="1"/>
                <field name="call_center_check" invisible="1"/>
            </xpath>

            <!-- make order line cols readonly if call center check depends-->
            <xpath expr="//field[@name='order_line']//tree//field[@name='product_id']" position="attributes">
                <attribute name="attrs">{'readonly': ['|',('product_updatable', '=', False), ('parent.call_center_check','=',False)], 'required': [('display_type', '=', False)]}</attribute>
            </xpath>
            <xpath expr="//field[@name='order_line']//tree//field[@name='price_unit']" position="attributes">
                <attribute name="attrs">{'readonly': ['|', ('qty_invoiced','>',0), ('parent.call_center_check','=',False)]}</attribute>
            </xpath>
            <xpath expr="//field[@name='order_line']//tree//field[@name='discount']" position="attributes">
                <attribute name="attrs">{'readonly': [('parent.call_center_check','=',False)]}</attribute>
            </xpath>
            <!-- make cols readonly if call center check depends-->
            <xpath expr="//button[@name='action_cancel']" position="attributes">
                <attribute name="attrs">{'invisible': ['|','|', ('state', 'not in', ['draft', 'sent', 'sale']), ('id', '=', False), ('call_center_check','=',False)]}</attribute>
            </xpath>

            <xpath expr="//field[@name='pricelist_id']" position="attributes">
                <attribute name="attrs">{'readonly':['|', ['state','not in',['draft','sent']], ('call_center_check','=',False)]}</attribute>
            </xpath>

        </field>
    </record>

    <record model="ir.actions.act_window.view" id="action_sale_call_center_tree">
        <field name="sequence" eval="1"/>
        <field name="view_mode">tree</field>
        <field name="view_id" ref="view_sale_order_call_center_tree"/>
        <field name="act_window_id" ref="action_sale_order_call_center"/>
    </record> 

    <record model="ir.actions.act_window.view" id="action_sale_call_center_form">
        <field name="sequence" eval="2"/>
        <field name="view_mode">form</field>
        <field name="view_id" ref="sale_order_call_center_inherited_view"/>
        <field name="act_window_id" ref="action_sale_order_call_center"/>
    </record>

    <menuitem
            id="menu_action_sale_order_call_center"
            action="action_sale_order_call_center"
            sequence="25"
            web_icon="employee_call_center/static/src/img/call.png"
            groups='employee_call_center.group_call_center'/>

    <menuitem
            id="menu_action_sale_order_call_center_orders"
            name="Orders"
            action="action_sale_order_call_center"
            sequence="1"
            parent="employee_call_center.menu_action_sale_order_call_center"
            groups='employee_call_center.group_call_center'/>

    <record id="view_is_call_center_crm_tag" model="ir.ui.view">
        <field name="model">crm.tag</field>
        <field name="inherit_id" ref="sales_team.sales_team_crm_tag_view_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='color']" position="after">
                <field name="is_call_center" />
            </xpath>
        </field>
    </record>

    <record id="view_employee_form_call_center_inherit" model="ir.ui.view">
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr.view_employee_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@id='o_work_employee_container']//group[1]" position="inside">
                <field name='is_call_center' />
            </xpath>
        </field>
    </record>
            
</odoo>
