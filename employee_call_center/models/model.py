from odoo import models, fields, api,_
from odoo.exceptions import ValidationError, MissingError


class CRMTag(models.Model):
    _inherit="crm.tag"

    is_call_center = fields.Boolean()

class SaleOrder(models.Model):
    _inherit="sale.order"

    is_call_center = fields.Boolean()
    is_assign = fields.Boolean()
    sent_to_ecommerce = fields.Boolean(default=False)
    call_center_check = fields.Boolean(compute="check_call_center")
    payment_date = fields.Datetime()
    confirmation_date = fields.Datetime(compute='get_date_confirmation')
    call_center_employee_id = fields.Many2one(
        string='Call Center Employee',
        comodel_name='hr.employee',
        readonly=True,
        copy=False
    )
    call_center_barcode = fields.Char(string="Code", copy=False)
    call_center_manager_code = fields.Char(string="Manager Code", copy=False)


    @api.depends('partner_id')
    def check_call_center(self):
        for order in self:
            order.call_center_check = False
            if self.env.user.has_group('employee_call_center.group_call_center') or order.is_call_center == False:
                order.call_center_check = True

    def get_date_confirmation(self):
        for order in self:
            order.confirmation_date = False
            if order.state in ('sale','done'):
                order.confirmation_date = order.date_order

    # def _compute_payment_date(self):
    #     for this in self:
    #         this.payment_date = False
    #         dates = []
    #         invoices = this.invoice_ids.filtered(lambda x:x.state == 'posted' and x.amount_residual == 0)
    #         if len(invoices) > 0:
    #             # for payment_info in json.loads(invoices.invoice_payments_widget).get('content', []):
    #             #     dates.append(payment_info.get('date', ''))

    #             this.payment_date = invoices[-1].payment_date
    #             # ', '.join(dates[-1])

    @api.onchange('call_center_barcode')
    def _onchange_call_barcode(self):
        if self.call_center_barcode and self.team_id:
            employee = self.env['hr.employee'].search([('barcode', '=', self.call_center_barcode), ('is_call_center', '=', True)])

            if not employee:
                raise ValidationError(_('Wrong Code.'))
            else:
                self.call_center_employee_id = employee

    @api.model
    def create(self, vals):
        res = super(SaleOrder, self).create(vals)
        for order in res:
            if order.is_call_center:
                # order.is_assign = True
                tags = self.env['crm.tag'].search([('is_call_center','=',True)])
                for tag in tags:
                    order.tag_ids = [(4, tag.id)]
        return res

    def write(self, values):
        """Override default Odoo write function and extend."""
        res =  super(SaleOrder, self).write(values)
        for order in self:
            if order.is_call_center:
                if values.get('team_id'):
                    team = self.env['crm.team'].browse(values['team_id']).name

                    if team == 'Ecomerce':
                        order.sent_to_ecommerce = True
                    else:
                        order.sent_to_ecommerce = False

                # order.is_assign = True
                # tags = self.env['crm.tag'].search([('is_call_center','=',True)])
                # for tag in tags:
                #     order.tag_ids = [(4, tag.id)]
        return res

    def assign_to_sales(self):
        for order in self:
            order.is_assign = True
            for member in order.team_id.member_ids:
                order.activity_schedule('employee_call_center.mail_activity_data_cc_assign', user_id=member.id)

            if not order.user_id:
                raise MissingError(f"Salesperson fields is missing in Sale Order {order.name}")

            from_user = order.env.user.partner_id
            to_user = order.user_id.partner_id
            link = order.env['ir.config_parameter'].sudo().get_param(
                'web.base.url') + f"/web#id={order.id}&view_type=form&model=sale.order"
            message = f"<p>{from_user.name} has assigned sale order: <a href={link} target='_blank'>{order.name}</a> to you.</p>"

            channels = order.env['mail.channel'].search([('channel_partner_ids', 'in', [from_user.id]),
                                                         ('channel_partner_ids', 'in', [to_user.id]),
                                                         ('channel_type', '=', 'chat'),
                                                         ('group_public_id', '=', False)])
            channels = channels.filtered(lambda x: len(x.channel_partner_ids) == 2)

            if len(channels) == 0:
                vals = {
                    'channel_partner_ids': [fields.Command.link(to_user.id), fields.Command.link(from_user.id)],
                    'channel_member_ids': [fields.Command.create({'partner_id': to_user.id, 'is_pinned': False}),
                                           fields.Command.create({'partner_id': from_user.id, 'is_pinned': True})],
                    'channel_type': 'chat',
                    'name': f'{to_user.name}, {from_user.name}'
                }
                channels = [order.env['mail.channel'].create(vals)]

            channels[0].message_post(message_type='comment', attachment_ids=[], body=message, partner_ids=[], subtype_xmlid='mail.mt_comment')

    def action_confirm(self):
        for so in self:
            activitys = so.activity_search(['employee_call_center.mail_activity_data_cc_assign'])
            for act in activitys:
                act.action_done()
        super().action_confirm()

    def action_cancel(self):
        # if self.is_call_center and self.state == 'draft':
        #     call_center_manager = self.env['hr.employee'].sudo().search([('is_call_center', '=', True), ('sale_report_access', '=', True)])
        #
        #     if not self.call_center_manager_code:
        #         raise UserError('You have to provide call center manager code.')
        #     elif self.call_center_manager_code != call_center_manager.barcode:
        #         raise UserError('The manger code is wrong.')

        for so in self:
            activitys = so.activity_search(['employee_call_center.mail_activity_data_cc_assign'])
            for act in activitys:
                act.action_done()
        return super(SaleOrder, self).action_cancel()


class AccountMove(models.Model):
    _inherit = 'account.move'

    call_center_move = fields.Boolean('Call center move', compute='_compute_call_center_move')

    @api.depends('line_ids')
    def _compute_call_center_move(self):
        for move in self:
            if len(move.line_ids.sale_line_ids.order_id):
                sale_order = move.line_ids.sale_line_ids.order_id[0]
                move.call_center_move = sale_order.picking_ids.is_call_center
            else:
                move.call_center_move = False
