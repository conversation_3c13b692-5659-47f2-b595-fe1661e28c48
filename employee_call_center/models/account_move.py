import logging

from odoo import models, fields, api,_
from odoo import tools
from datetime import datetime
from num2words import num2words
import json


from odoo.exceptions import UserError, ValidationError


from odoo.addons.sale.models.account_move import AccountMove


def _invoice_paid_hook(self):
    # OVERRIDE
    res = super(AccountMove, self)._invoice_paid_hook()
    todo = set()
    for invoice in self.filtered(lambda move: move.is_invoice()):
        for line in invoice.invoice_line_ids:
            for sale_line in line.sale_line_ids:
                todo.add((sale_line.order_id, invoice.name))
    for (order, name) in todo:
        order.message_post(body=_("Invoice %s paid", name))
        order.payment_date = datetime.now()
    return res

AccountMove._invoice_paid_hook = _invoice_paid_hook
