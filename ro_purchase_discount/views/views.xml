<odoo>
  <data>

    <record id="ro_purchase_order_form" model="ir.ui.view">
      <field name="model">purchase.order</field>
      <field name="inherit_id" ref="purchase.purchase_order_form"/>
      <field name="arch" type="xml">

        <xpath expr="//field[@name='order_line']/tree/field[@name='price_unit']" position="before">
          <field name="ro_unit_price_bef_dis" attrs="{'readonly':[('parent.ro_user_in_group','!=',True)]}" force_save="1"/>
          <field name="ro_discount_percent" />
        </xpath>

        <xpath expr="//field[@name='order_line']/tree/field[@name='price_unit']" position="attributes">
            <attribute name="readonly">1</attribute>
            <attribute name="force_save">1</attribute>
        </xpath>

      </field>
    </record>

  </data>
</odoo>