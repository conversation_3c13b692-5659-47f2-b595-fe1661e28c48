from odoo import _, api, fields, models


class PurchaseOrderLine(models.Model):
    _inherit = 'purchase.order.line'

    ro_unit_price_bef_dis = fields.Float(string='Price Before Disc')
    ro_discount_percent = fields.Float(string='Discount %')


    def _compute_price_unit_and_date_planned_and_name(self):
        super(PurchaseOrderLine, self)._compute_price_unit_and_date_planned_and_name()
        for line in self:
                line.ro_unit_price_bef_dis = line.price_unit
                line._onchange_ro_fields()


    @api.onchange('ro_unit_price_bef_dis', 'ro_discount_percent')
    def _onchange_ro_fields(self):
        for line in self:
            if  line.ro_discount_percent >= 0:
                # Calculate the price_unit based on ro_unit_price_bef_dis and ro_discount_percent
                line.price_unit = line.ro_unit_price_bef_dis - (line.ro_unit_price_bef_dis * (line.ro_discount_percent / 100))
            else:
                line.price_unit = line.ro_unit_price_bef_dis  
