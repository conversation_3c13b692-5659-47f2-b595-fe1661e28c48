from odoo import models, fields, api

class StockPicking(models.Model):
    _inherit = 'stock.picking'

    show_add_line_to_admin = fields.Boolean(string="Show Add Line to Administrator", compute='_compute_show_add_line_to_admin')

    @api.depends('picking_type_code', 'state', 'is_locked')
    def _compute_show_add_line_to_admin(self):
        for record in self:
            
            record.show_add_line_to_admin = self.env.user.user_has_groups('stock.group_stock_manager') 
            

