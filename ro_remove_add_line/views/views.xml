<odoo>
  <record id="view_stock_picking_form_inherit" model="ir.ui.view">
    <field name="name">stock.picking.form.inherit</field>
    <field name="model">stock.picking</field>
    <field name="inherit_id" ref="stock.view_picking_form"/>
    <field name="arch" type="xml">
      <xpath expr="//field[@name='partner_id']" position="after">
        <field name="show_add_line_to_admin" invisible="1"/>
      </xpath>
      <xpath expr="//field[@name='move_ids_without_package']" position="attributes">
        <attribute name="attrs">{'readonly': ['|', '&amp;', ('show_add_line_to_admin', '=', False),('picking_type_code', '!=', 'internal'), 
         '&amp;', ('state', 'in', ('done','validate2',)), ('is_locked', '=', True)]}</attribute>
      </xpath>
      <xpath expr="//field[@name='move_line_ids_without_package']" position="attributes">
        <attribute name="attrs">{'readonly': ['|', '|', '|', '&amp;', ('show_add_line_to_admin', '=', False),('picking_type_code', 'not in', ('internal','incoming')),
          ('show_operations', '=', False), ('state', '=', 'cancel'), '&amp;', ('state', '=', 'done'), ('is_locked', '=', True)],
          'invisible': [('show_reserved', '=', False)]}</attribute>
 
      </xpath>

    </field>
  </record>
  <record id="inherit_view_stock_move_operations" model="ir.ui.view">
    <field name="model">stock.move</field>
    <field name="inherit_id" ref="stock.view_stock_move_nosuggest_operations"/>
    <field name="arch" type="xml">

      <xpath expr="//field[@name='move_line_nosuggest_ids']" position="before">
        <field name="ro_show_add_line_to_admin" />
        <field name="ro_picking_type_code" />
      </xpath>

      <xpath expr="//field[@name='move_line_nosuggest_ids']" position="attributes">
        <attribute name="attrs">{'readonly': ['|', '|', ('state', '=', 'cancel'), '&amp;', ('ro_show_add_line_to_admin', '=', False),
        ('ro_picking_type_code', '!=', 'internal'), '&amp;', ('state', '=', 'done'), ('is_locked', '=', True)]}
        
        </attribute>
      </xpath>

    </field>
  </record>
</odoo>
