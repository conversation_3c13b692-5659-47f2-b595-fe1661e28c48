# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError

class stockQuant(models.Model):

    _inherit = 'stock.quant'


    @api.constrains('quantity','company_id')
    def _check_quantity(self):
        self.ensure_one()
        q = self.quantity
        com = self.company_id.id  

        if q < 0  and com > 0 and not ("Inventory adjustment" in self.location_id.name or "Virtual Locations" in self.location_id.display_name):
	        raise ValidationError(_('Not enough quantity To Proceed.'))