from odoo import models, fields,api
from odoo.addons.hr_payroll.models.hr_salary_attachment import HrSalaryAttachment
from odoo.tools.date_utils import start_of
from dateutil.relativedelta import relativedelta
from math import ceil

@api.depends('state', 'total_amount', 'monthly_amount')
def _compute_estimated_end(self):
    for record in self:
        if record.state not in ['close', 'cancel'] and record.has_total_amount and record.monthly_amount:
            record.date_estimated_end = start_of(record.date_start + relativedelta(months=ceil(record.total_amount / record.monthly_amount)), 'month')
        else:
            record.date_estimated_end = False
HrSalaryAttachment._compute_estimated_end = _compute_estimated_end

class InheritHrPayrollReport(models.Model):
    _inherit = "hr.payroll.report"

    struct_id = fields.Many2one('hr.payroll.structure', 'Structure', readonly=True)
    payslip_run_id = fields.Many2one('hr.payslip.run', 'Batch', readonly=True)
    
    def _select(sel, additional_rules):
        rec = super()._select(additional_rules)
        return rec + """,
                p.struct_id as struct_id,
                p.payslip_run_id as payslip_run_id
                """

    def _group_by(self, additional_rules):
            return super()._group_by(additional_rules) + """,
                    p.struct_id,
                    p.payslip_run_id"""
        
class Payslip(models.Model):
    _inherit = 'hr.payslip'

    #allowances
    ro_bonas= fields.Float('حافز', related='contract_id.ro_bonas')
    ro_additional_transfer = fields.Float('انتقالات الاضافى')
    ro_number_of_nights = fields.Integer(string='عدد السهرات')
    total_value_of_nights = fields.Float(string='قيمة السهرات', compute='_compute_total_value_of_nights')
    ro_quarter_value = fields.Float('قيمة الكوارتر', related='contract_id.ro_quarter_value')
    
    ro_target_layers = fields.Float('تارجت شرائح')
    ro_commission = fields.Float('نسبة تحقيق الحافز')
    ro_reward_perc = fields.Float('نسبه تحقيق الكوارتر')

    ro_kpi = fields.Float('KPI')
    ro_overtime_hours = fields.Float('ساعات إضافية')
    ro_holyday_allow = fields.Float('بدل إجازه')
    ro_other_allow = fields.Float('أخرى')
    ro_ramadan_perc = fields.Float('نسبه تحقيق منحة رمضان')
    
    ro_stop_perc = fields.Float('نسبه الإيقاف')
    
 
    #
    ro_shift = fields.Monetary(related='contract_id.ro_shift', readonly=True)
    ro_company_contribution = fields.Monetary(related='contract_id.ro_company_contribution', readonly=True)
    ro_replacement = fields.Monetary(related='contract_id.ro_replacement', readonly=True)
    ro_regularity = fields.Monetary(related='contract_id.ro_regularity', readonly=True)
    number_of_days = fields.Integer('Number of Days')
    days_before_appointment = fields.Float('Days Before Appointment', compute='_compute_days_before_appointment')

    # deductions
    number_of_penalty_days = fields.Float('عدد ايام الجزاء')
    discount_value_per_day = fields.Float('قيمه خصم باليوم', compute='_compute_discount_value_per_day')
    ro_rent= fields.Float('نسبة خصم تالف')
    ro_discount_reg= fields.Float('خصم بدل انتظام')
    ro_discount_premium= fields.Float('خصم بيرميوم ')
    ro_discount_returns= fields.Float('خصم مرتجعات ')
    ro_deficit_deduction= fields.Float('خصم عجز نقدية ')
    ro_direct_discount= fields.Float('خصم مباشر يوم30 ')
    ro_10_day_deduction= fields.Float('خصم مباشر يوم10 ')
    ro_quarter_deduction= fields.Float('خصومات الكوارتر')
    ro_quarter_tax= fields.Float('ضريبة الكوارتر')
    ro_absence_punsh= fields.Float('جزاء غياب')

    ro_absence = fields.Float('غياب بدون إذن')
    ro_unpaid_leave = fields.Float('إجازه بدون أجر')
    ro_sick = fields.Float('مرضي')
    ro_late = fields.Float('تأخيرات')

    ro_previuos_tax = fields.Float('ضريبة سابقه')

    # basic_salary = fields.Float()
    


   
    overtime_allow = fields.Float('قيمة الساعات الإضافية(L)', compute="get_lines_amount")
    holyday_allow_amount = fields.Float('قيمة بدل الإجازه(L)', compute="get_lines_amount")
    commission_allow = fields.Float('نسبة تحقيق الحافز(L)', compute="get_lines_amount")
    net_quarter_allow = fields.Float('صافي الكوارتر بعد الضريبة(L)', compute="get_lines_amount")
    net_salary = fields.Float('صافي المرتب', compute="get_lines_amount")

    damadge_ded = fields.Float('قيمة خصم التالف(L)', compute="get_lines_amount")
    absence_ded = fields.Float('قيمة الغياب بدون إذن(L)', compute="get_lines_amount")
    sick_ded = fields.Float('قيمة الإجازة المرضي(L)', compute="get_lines_amount")
    late_ded = fields.Float('قيمة التأخيرات(L)', compute="get_lines_amount")
    unpaid_ded = fields.Float('قيمة الإجازه بدون أجر(L)', compute="get_lines_amount")
    insurance_ded = fields.Float('تأمينات اجتماعية(L)', compute="get_lines_amount")
    box_5 = fields.Float('صندوق 5/10000(L)', compute="get_lines_amount")

    @api.depends('line_ids')
    def get_lines_amount(self):
        for rec in self:
            
            rec.overtime_allow = sum(rec.line_ids.filtered(lambda x:x.code == 'OVERTIMEHOURS').mapped('total'))
            rec.holyday_allow_amount = sum(rec.line_ids.filtered(lambda x:x.code == 'VACATION').mapped('total'))
            rec.commission_allow = sum(rec.line_ids.filtered(lambda x:x.code == 'COM').mapped('total'))
            rec.net_quarter_allow = sum(rec.line_ids.filtered(lambda x:x.code=='NETQ').mapped('total'))

            rec.damadge_ded = sum(rec.line_ids.filtered(lambda x:x.code == 'SCRAP').mapped('total'))
            rec.absence_ded = sum(rec.line_ids.filtered(lambda x:x.code == 'ABS').mapped('total'))
            rec.sick_ded = sum(rec.line_ids.filtered(lambda x:x.code == 'ABSICK').mapped('total'))
            rec.late_ded = sum(rec.line_ids.filtered(lambda x:x.code == 'LATE').mapped('total'))
            rec.unpaid_ded = sum(rec.line_ids.filtered(lambda x:x.code == 'UNPAIDLEAVE').mapped('total'))
            rec.insurance_ded = sum(rec.line_ids.filtered(lambda x:x.code == 'INDED').mapped('total'))
            rec.box_5 = sum(rec.line_ids.filtered(lambda x:x.code == 'BOX').mapped('total'))

            rec.net_salary = sum(rec.line_ids.filtered(lambda x:x.code == 'NETAFTER').mapped('total'))
            
    
    @api.depends('ro_number_of_nights')
    def _compute_total_value_of_nights(self):
        for record in self:
           
            record.total_value_of_nights = record.ro_number_of_nights * (record.employee_id.job_id.ro_night_rate or 60)
    
    @api.depends( 'ro_shift', 'ro_company_contribution', 'ro_replacement', 'ro_regularity', 'number_of_days', 'days_before_appointment')
    def _compute_days_before_appointment(self):
        for record in self:
            total_value = ((record.contract_id.wage + record.ro_shift + record.ro_bonas) / 30) * record.number_of_days
            record.days_before_appointment = total_value
    @api.depends('ro_bonas', 'contract_id.wage', 'number_of_penalty_days')
    def _compute_discount_value_per_day(self):
        for record in self:
            if record.number_of_penalty_days != 0:
                total_value = ((record.ro_bonas + record.contract_id.wage + record.ro_shift) / 30 ) * record.number_of_penalty_days 
                record.discount_value_per_day = total_value
            else:
                record.discount_value_per_day = 0.0

    
   

