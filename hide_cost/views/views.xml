<odoo>
    <data>
        <!-- inhert product form view  -->
        <record id="product_product_tree_view_inherit" model="ir.ui.view">
            <field name="model">product.product</field>
            <field name="inherit_id" ref="product.product_product_tree_view"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='standard_price']" position="attributes">    
                    <attribute name="groups">hide_cost.group_manager_cost</attribute>
                </xpath>
            </field>
        </record>
        <record id="product_variant_easy_edit_view_inherit" model="ir.ui.view">
            <field name="model">product.product</field>
            <field name="inherit_id" ref="product.product_variant_easy_edit_view"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='standard_price']" position="attributes">    
                    <attribute name="groups">hide_cost.group_manager_cost</attribute>
                </xpath>
            </field>
        </record>
        <record id="product_template_form_view_inherit" model="ir.ui.view">
            <field name="model">product.template</field>
            <field name="inherit_id" ref="product.product_template_form_view"/>
            <field name="arch" type="xml">
                <xpath expr="//label[@for='standard_price']" position="attributes">    
                    <attribute name="groups">hide_cost.group_manager_cost</attribute>
                </xpath>
                <xpath expr="//div[@name='standard_price_uom']" position="attributes">    
                    <attribute name="groups">hide_cost.group_manager_cost</attribute>
                </xpath>
                <xpath expr="//page[@name='purchase']" position="attributes">    
                    <attribute name="groups">hide_cost.group_manager_cost</attribute>
                </xpath>
            </field>
        </record>
        <record id="product_template_tree_view_inherit" model="ir.ui.view">
            <field name="model">product.template</field>
            <field name="inherit_id" ref="product.product_template_tree_view"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='standard_price']" position="attributes">    
                    <attribute name="groups">hide_cost.group_manager_cost</attribute>
                </xpath>
            </field>
        </record>
        <record id="view_product_margin_form_inherit" model="ir.ui.view">
            <field name="model">product.product</field>
            <field name="inherit_id" ref="product_margin.view_product_margin_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='standard_price']" position="attributes">    
                    <attribute name="groups">hide_cost.group_manager_cost</attribute>
                </xpath>
                <xpath expr="//field[@name='total_margin']" position="attributes">    
                    <attribute name="groups">hide_cost.group_manager_cost</attribute>
                </xpath>
                <xpath expr="//field[@name='expected_margin']" position="attributes">    
                    <attribute name="groups">hide_cost.group_manager_cost</attribute>
                </xpath>
                <xpath expr="//field[@name='total_margin_rate']" position="attributes">    
                    <attribute name="groups">hide_cost.group_manager_cost</attribute>
                </xpath>
                <xpath expr="//field[@name='expected_margin_rate']" position="attributes">    
                    <attribute name="groups">hide_cost.group_manager_cost</attribute>
                </xpath>
            </field>
        </record>
        <record id="view_product_margin_graph_inherit" model="ir.ui.view">
            <field name="model">product.product</field>
            <field name="inherit_id" ref="product_margin.view_product_margin_graph"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='total_margin']" position="attributes">    
                    <attribute name="groups">hide_cost.group_manager_cost</attribute>
                </xpath>
            </field>
        </record>
        <record id="view_product_margin_tree_inherit" model="ir.ui.view">
            <field name="model">product.product</field>
            <field name="inherit_id" ref="product_margin.view_product_margin_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='total_margin']" position="attributes">    
                    <attribute name="groups">hide_cost.group_manager_cost</attribute>
                </xpath>
                <xpath expr="//field[@name='expected_margin']" position="attributes">    
                    <attribute name="groups">hide_cost.group_manager_cost</attribute>
                </xpath>
                <xpath expr="//field[@name='total_margin_rate']" position="attributes">    
                    <attribute name="groups">hide_cost.group_manager_cost</attribute>
                </xpath>
                <xpath expr="//field[@name='expected_margin_rate']" position="attributes">    
                    <attribute name="groups">hide_cost.group_manager_cost</attribute>
                </xpath>
            </field>
        </record> 
        <record id="view_product_stock_tree_inherit" model="ir.ui.view">
            <field name="model">product.product</field>
            <field name="inherit_id" ref="stock_account.product_product_stock_tree_inherit_stock_account"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='avg_cost']" position="attributes">    
                    <attribute name="groups">hide_cost.group_manager_cost</attribute>
                </xpath>
                <xpath expr="//field[@name='total_value']" position="attributes">    
                    <attribute name="groups">hide_cost.group_manager_cost</attribute>
                </xpath>
            </field>
        </record>

        <record id="view_stock_valuation_layer_tree_inherit" model="ir.ui.view">
            <field name="model">stock.valuation.layer</field>
            <field name="inherit_id" ref="stock_account.stock_valuation_layer_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='unit_cost']" position="attributes">    
                    <attribute name="groups">hide_cost.group_manager_cost</attribute>
                </xpath>
                <xpath expr="//field[@name='value']" position="attributes">    
                    <attribute name="groups">hide_cost.group_manager_cost</attribute>
                </xpath>
            </field>
        </record>

        <record id="view_stock_quant_tree_inherit" model="ir.ui.view">
            <field name="model">stock.quant</field>
            <field name="inherit_id" ref="stock_account.view_stock_quant_tree_editable_inherit"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='value']" position="attributes">    
                    <attribute name="groups">hide_cost.group_manager_cost</attribute>
                </xpath>
            </field>
        </record>

        <!-- PR View -->
        <record id="view_purchase_requisition_form_hide_inherit" model="ir.ui.view">
            <field name="model">purchase.requisition</field>
            <field name="inherit_id" ref="purchase_requisition.view_purchase_requisition_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='line_ids']/tree/field[@name='price_unit']" position="attributes">    
                    <attribute name="groups">hide_cost.group_manager_cost</attribute>
                </xpath>
            </field>
        </record>

        <!-- inhert sale order form view  -->
        <record id="sale_margin_sale_order_line_inherit" model="ir.ui.view">
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale_margin.sale_margin_sale_order_line"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='purchase_price']" position="attributes">    
                    <attribute name="groups">hide_cost.group_manager_cost</attribute>
                </xpath>
            </field>
        </record>
        <record id="sale_margin_sale_order_line_form_inherit" model="ir.ui.view">
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale_margin.sale_margin_sale_order_line_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='purchase_price']" position="attributes">    
                    <attribute name="groups">hide_cost.group_manager_cost</attribute>
                </xpath>
                <xpath expr="//field[@name='margin']" position="attributes">    
                    <attribute name="groups">hide_cost.group_manager_cost</attribute>
                </xpath>
                <xpath expr="//field[@name='margin_percent']" position="attributes">    
                    <attribute name="groups">hide_cost.group_manager_cost</attribute>
                </xpath>
            </field>
        </record>
        <record id="sale_margin_sale_order_inherit" model="ir.ui.view">
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale_margin.sale_margin_sale_order"/>
            <field name="arch" type="xml">
                <xpath expr="//label[@for='margin']" position="attributes">    
                    <attribute name="groups">hide_cost.group_manager_cost</attribute>
                </xpath>
                <xpath expr="//div[hasclass('text-nowrap')]" position="attributes">    
                    <attribute name="groups">hide_cost.group_manager_cost</attribute>
                </xpath>
            </field>
        </record>
        <record id="sale_margin_sale_order_pivot_inherit" model="ir.ui.view">
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale_margin.sale_margin_sale_order_pivot"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='margin_percent']" position="attributes">    
                    <attribute name="groups">hide_cost.group_manager_cost</attribute>
                </xpath>
                <xpath expr="//field[@name='margin_percent']" position="after">
                    <field name="margin" groups="hide_cost.group_manager_cost"/>
                </xpath>
            </field>
        </record>
        <record id="sale_margin_sale_order_graph_inherit" model="ir.ui.view">
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale_margin.sale_margin_sale_order_graph"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='margin_percent']" position="attributes">    
                    <attribute name="groups">hide_cost.group_manager_cost</attribute>
                </xpath>
                <xpath expr="//field[@name='margin_percent']" position="after">
                    <field name="margin" groups="hide_cost.group_manager_cost"/>
                </xpath>
            </field>
        </record>

    </data>
</odoo>