<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    <t t-inherit="point_of_sale.OrderReceipt" t-inherit-mode="extension" owl="1">
        <xpath expr="//t[@t-esc='receipt.cashier']/.." position="after">
            <t t-if="receipt.partner">
                <div class="receiptcashier">
                    <div>--------------------------------</div>
                    <!-- <div>بيانات العميل</div> -->
<!--                    <br/>-->
                    <t t-if="receipt.partner.name">
                        <div><t t-esc="receipt.partner.name" /></div>
                    </t>
                    <t t-elif="receipt.partner.parent_name">
                        <div><t t-esc="receipt.partner.parent_name" /></div>
                    </t>
                    <t t-if="receipt.partner.phone">
                        <div><t t-esc="receipt.partner.phone" /></div>
                    </t>
                    <t t-if="receipt.partner.mobile">
                        <div><t t-esc="receipt.partner.mobile" /></div>
                    </t>

                    <t t-if="receipt.partner.address">
                        <div><t t-esc="receipt.partner.address" /></div>
                    </t>
                </div>
            </t>
        </xpath>

        <xpath expr="//div[hasclass('pos-receipt')]/div[hasclass('pos-receipt-amount')]" position="after">
        
            <span>عدد الأصناف: <t t-esc="receipt.orderlines.length"/></span>

        </xpath>
        
         <xpath expr="//div[hasclass('pos-receipt')]/br[3]" position="replace">       
        </xpath>

        <xpath expr="//div[hasclass('pos-receipt')]/br[3]" position="replace">       
        </xpath>
        <xpath expr="//div[hasclass('pos-receipt')]/br[4]" position="replace">       
        </xpath>

        <xpath expr="//div[hasclass('pos-receipt')]/br[5]" position="replace">        
        </xpath>

         <!-- <xpath expr="//div[hasclass('pos-receipt')]/br[6]" position="replace">        
        </xpath> -->


    </t>
</templates>
