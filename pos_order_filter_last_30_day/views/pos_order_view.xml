<?xml version="1.0" encoding="UTF-8"?>
<odoo>

    <!-- <record id="view_pos_order_filter_last_30_day_filter" model="ir.ui.view">
        <field name="model">pos.order</field>
        <field name="inherit_id" ref="point_of_sale.view_pos_order_filter"/>
        <field name="arch" type="xml">
            <xpath expr="//filter[@name='order_date']" position="after">
                <filter string="Last 30 days" name="filter_last_month_date_order" domain="[('date_order','&gt;', (context_today() - datetime.timedelta(days=30)).strftime('%Y-%m-%d'))]"/>
            </xpath>
        </field>
    </record> -->

    <record id="action_pos_pos_form_last_30_days" model="ir.actions.act_window">
        <field name="name">Recent Orders</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">pos.order</field>
        <field name="view_mode">tree,form,kanban,pivot</field>
        <field name="view_id" eval="False"/>
        <field name="domain">[('date_order','&gt;', (context_today() - datetime.timedelta(days=30)).strftime('%Y-%m-%d'))]</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_empty_folder">
                No orders found
            </p><p>
                To record new orders, start a new session.
            </p>
        </field>
    </record>

</odoo>
