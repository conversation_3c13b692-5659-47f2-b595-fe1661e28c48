<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="action_whole_transfer_rfq" model="ir.actions.act_window">
        <field name="name">Purchase Orders</field>
        <field name="res_model">purchase.order</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'quotation_only': True}</field>
        <!-- <field name="view_id" ref="purchase.purchase_order_view_tree"/> -->
        
        <field name="domain">[('state','in',('draft','sent')),'|',('user_id','=',uid),('picking_type_id.warehouse_id.crm_team_id.member_ids','=',uid)]</field>
    </record>
    
    <record model="ir.actions.act_window.view" id="action_wt_rfq_tree">
        <field name="sequence" eval="1"/>
        <field name="view_mode">tree</field>
        <field name="view_id" ref="purchase.purchase_order_view_tree"/>
        <field name="act_window_id" ref="action_whole_transfer_rfq"/>
    </record>

    <record model="ir.ui.view" id="purchase_order_wt_inherited_view">
        <field name="name">purchase_order_wt_inherited_view</field>
        <field name="model">purchase.order</field>
        <field name="inherit_id" ref="purchase.purchase_order_form"/>
        <field name="mode">primary</field>
        <field name="priority">30</field>
        <field name="arch" type="xml">

            <xpath expr="//button[@name='button_confirm'][1]" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//button[@name='button_confirm'][2]" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//button[@name='action_rfq_send']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            
            <!-- <xpath expr="//field[@name='picking_type_id']" position="after">
                <field name="company_id"/>
            </xpath> -->

            <xpath expr="//page[@name='purchase_delivery_invoice']//field[@name='company_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='picking_type_id']" position="attributes">
                <attribute name="readonly">1</attribute>
                <attribute name="force_save">1</attribute>
            </xpath>
            
        </field>
    </record>
    
    <record model="ir.actions.act_window.view" id="action_wt_rfq_form">
        <field name="sequence" eval="2"/>
        <field name="view_mode">form</field>
        <field name="view_id" ref="purchase_order_wt_inherited_view"/>
        <field name="act_window_id" ref="action_whole_transfer_rfq"/>
    </record> 
    
    <menuitem
            id="menu_action_wt_purchase"
            name="Purchase"
            sequence="5"
            parent="whole_transfer.whole_transfer_menu_root"
            groups="whole_transfer.group_whole_transfer_user"/>
            
    <menuitem
            id="menu_action_whole_transfer_rfq"
            action="action_whole_transfer_rfq"
            sequence="2"
            parent="menu_action_wt_purchase"/>
            
</odoo>
