import logging

from odoo import models, fields, api, _, SUPERUSER_ID
from odoo.exceptions import UserError, ValidationError
from odoo.addons.purchase_stock.models.purchase import PurchaseOrder


def _create_picking(self):
    StockPicking = self.env['stock.picking']
    for order in self.filtered(lambda po: po.state in ('purchase', 'done')):
        if any(product.type in ['product', 'consu'] for product in order.order_line.product_id):
            order = order.with_company(order.company_id)
            pickings = order.picking_ids.filtered(lambda x: x.state not in ('done', 'cancel'))
            if not pickings:
                res = order._prepare_picking()
                picking = StockPicking.with_user(SUPERUSER_ID).create(res)
            else:
                picking = pickings[0]
            moves = order.order_line._create_stock_moves(picking)
            moves = moves.filtered(lambda x: x.state not in ('done', 'cancel'))._action_confirm()
            seq = 0
            for move in sorted(moves, key=lambda move: move.date):
                seq += 5
                move.sequence = seq
            moves._action_assign()

            if not moves.picking_id.partner_id:
                moves.picking_id.partner_id = picking.partner_id.id

            picking.message_post_with_view('mail.message_origin_link',
                values={'self': picking, 'origin': order},
                subtype_id=self.env.ref('mail.mt_note').id)
            
            
            warehouse = picking.location_dest_id.warehouse_id

            mail_activity_data = []
            model_stock_picking = self.env.ref('stock.model_stock_picking')
            mail_activity_data_todo = self.env.ref('mail.mail_activity_data_todo')
            for user in warehouse.crm_team_id.member_ids:
                data = {
                    'res_id': picking.id,
                    'res_model_id': model_stock_picking.id,
                    'user_id': user.id,
                    'summary': 'Purchase Product Request Schedule',
                    'activity_type_id': mail_activity_data_todo.id,
                    'date_deadline': fields.Date.today()
                }

                mail_activity_data.append(data)

            self.env['mail.activity'].with_user(SUPERUSER_ID).create(mail_activity_data)

    return True


PurchaseOrder._create_picking = _create_picking



class PurchaseOrder(models.Model):
    _inherit = 'purchase.order'

    @api.model
    def _get_picking_type(self, company_id):
        res = super(PurchaseOrder, self)._get_picking_type(company_id)
        picking_type = self.env['stock.picking.type'].search([('code', '=', 'incoming'), ('is_return', '=', False), ('warehouse_id.company_id', '=', company_id)])
        if picking_type:
            picking_type = picking_type.filtered(lambda x:self.env.user in x.warehouse_id.crm_team_id.member_ids)
        if not picking_type:
            picking_type = self.env['stock.picking.type'].search([('code', '=', 'incoming'), ('warehouse_id', '=', False)])
        return picking_type[:1]