# -*- coding: utf-8 -*-

from odoo import models, fields, api,_, SUPERUSER_ID

from odoo.osv import expression
from odoo.tools import DEFAULT_SERVER_DATETIME_FORMAT
from odoo.tools.float_utils import float_compare, float_is_zero, float_round
from odoo.exceptions import UserError

class PickingTypeEdit(models.Model):
    _inherit='stock.picking'

    state = fields.Selection(selection_add=[
        ('validate2', 'Second Validate'),
        ('done', )])

    flag = fields.Boolean(default=True)

    enable_edit = fields.Boolean(default=False, compute='_enable_edit' )

    @api.depends('name', 'picking_type_code')
    def _enable_edit(self):
        for record in self:
            if record.state in ['confirmed', 'assigned'] and record.picking_type_code == 'internal' and record.env.user.id in record.warehouse_team_to_id.member_ids.ids:
                record.enable_edit = False
            else:
                record.enable_edit = True

    
    
    def action_revalidate(self):
        if self.picking_type_id.code=='internal':
            self.state='assigned'
            
    def action_picking_move_line_tree(self):
       
        return {
            'name': _('Stock Move Lines'),
            'type': 'ir.actions.act_window',
            # 'view_type': 'tree',
            'view_mode': 'tree',
            # 'view_id': self.env.ref('stock.view_stock_move_line_operation_tree').id,
            'view_id': self.env.ref('internal_transfer_dual_validate.view_picking_move_line_tree').id,
            'res_model': 'stock.move.line',
            'context': {'show_lots_m2o': True, 'show_reserved_quantity': True},
            #'context': {'show_lots_text': False, 'show_reserved_quantity': True},
            'domain': [('picking_id', 'in', self.ids)],
            'target': 'current',
        }



    
    def write(self, values):
        if self.state in ['confirmed', 'assigned'] and self.picking_type_code == 'internal' and self.env.user.id in self.warehouse_team_to_id.member_ids.ids and 'state' not in values and self.warehouse_team_to_id != self.warehouse_team_from_id:
            raise UserError(
                    _("You cannot edit on this transfer ."))
        
        if self.state in ['validate2', 'cancel', 'done'] and self.picking_type_code == 'internal' and 'date_done' not in values and self.env.user.id in self.warehouse_team_to_id.member_ids.ids and self.warehouse_team_to_id != self.warehouse_team_from_id:
            print(values)
            raise UserError(
                    _("You cannot edit on this transfer ."))
        result = super(PickingTypeEdit, self).write(values)
    
        return result
    

    @api.model
    def create(self, values):
        res = super(PickingTypeEdit, self).create(values)

        if res.picking_type_code == 'internal':
            res.partner_id = False

            mail_activity_data = []

            for user in res.warehouse_team_from_id.member_ids:
                    data = {
                        'res_id': res.id,
                        'res_model_id': self.env.ref('stock.model_stock_picking').id,
                        'user_id': user.id,
                        'summary': 'Product Request Schedule',
                        'activity_type_id': self.env.ref('mail.mail_activity_data_todo').id,
                        'date_deadline': fields.Date.today()
                    }
                    mail_activity_data.append(data)

            self.env['mail.activity'].with_user(SUPERUSER_ID).create(mail_activity_data)

        return res

    


    def action_confirm(self):
        for rec in self.filtered(lambda x:x.picking_type_id.code=='internal'):
            if self.env.user in rec.warehouse_team_to_id.member_ids and rec.warehouse_team_to_id != rec.warehouse_team_from_id:
                raise UserError(
                    _("You cannot mark it as ready. Please wait for the other employee to do it ."))

        # if self.env.user.id != self.user_id.id:
        return super(PickingTypeEdit, self).action_confirm()
    
    def button_validate(self):
        
        if self.picking_type_id.code=='internal' and self.flag:
            # self.env.user.has_group('base.group_system') or 
            # if self.env.user.id == self.user_id.id and self.env.user in self.warehouse_team_from_id.member_ids:
            if self.env.user.has_group('base.group_system') or self.env.user in self.warehouse_team_from_id.member_ids:
                
                for picking in self.filtered(lambda x: x.picking_type_code in ('internal')):
                    for line in picking.move_ids:
                        if line.quantity_done > line.reserved_availability:
                            raise UserError(line.product_id.name + " Has No available quantity")

                self.flag = False
                self.state='validate2'
                mail_activity_data = []
                self.activity_ids.with_user(SUPERUSER_ID).action_done()

                for user in self.warehouse_team_to_id.member_ids:
                    data = {
                        'res_id': self.id,
                        'res_model_id': self.env.ref('stock.model_stock_picking').id,
                        'user_id': user.id,
                        'summary': 'Validation Arrival Schedule',
                        'activity_type_id': self.env.ref('mail.mail_activity_data_todo').id,
                        'date_deadline': fields.Date.today(),
                    }
                    mail_activity_data.append(data)

                self.env['mail.activity'].with_user(SUPERUSER_ID).create(mail_activity_data)
            else:
                raise UserError(
                    _("You cannot validate. Please wait for the other employee to approve ."))

        elif self.picking_type_id.code=='internal' and not self.flag:
            if self.env.user.has_group('base.group_system') or self.env.user in self.warehouse_team_to_id.member_ids:
                result = super(PickingTypeEdit, self).button_validate()
                self.activity_ids.with_user(SUPERUSER_ID).action_done()
                return result
                
            else:
                raise UserError(
                    _("You cannot validate. Please wait for the other employee to approve ."))

        else:
            # if self.env.user.id != self.user_id.id:
            return super(PickingTypeEdit, self).button_validate()
            # else:
            #     raise UserError(
            #         _("You cannot validate. Please wait for the other employee to approve ."))
                         
