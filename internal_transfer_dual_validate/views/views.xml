<odoo>
    <data>
        <record model="ir.ui.view" id="stock_picking_search_validate2">
            <field name="model">stock.picking</field>
            <field name="inherit_id" ref="stock.view_picking_internal_search"/>
            <field name="arch" type="xml">
                <xpath expr="//filter[@name='available']" position="after">
                    <filter name="available2" string="Second Validate" domain="[('state', '=', 'validate2')]"/>
                </xpath>
            </field>
        </record>


        <record model="ir.ui.view" id="stock_picking_form_inherit">
            <field name="name">stock picking form view inherit</field>
            <field name="model">stock.picking</field>
            <field name="inherit_id" ref="stock.view_picking_form"/>
            <field name="arch" type="xml">
                <xpath expr="//header" position="inside">
                    <button name="button_validate" string="Second Validate" 
                    type="object" class="oe_highlight" attrs="{'invisible':[('state','!=','validate2')]}"/>

                    <!-- <button name="action_revalidate" string="ReCheckavailabilty" 
                     type="object" attrs="{'invisible':[('state','!=','validate2')]}"/> -->

                </xpath> 
                <xpath expr="//field[@name='owner_id']" position="after">
                    <!-- <field name="need_edits" /> -->
                    <field name="warehouse_team_to_id"  invisible="1"/>
                    <field name="enable_edit" invisible="1"/>
                </xpath>
                <xpath expr="//button[@name='action_picking_move_tree']" position="before">
                    <button name="action_picking_move_line_tree" attrs="{'invisible': ['|', '|', ('show_operations', '=', False), ('state', '=', 'cancel'), '&amp;', ('state', '=', 'done'), ('is_locked', '=', True)]}" class="oe_stat_button" icon="fa-arrows-v" type="object" help="List view of operations" contextcontext="{'default_picking_id': picking_id, 'default_move_id': id, 'default_product_id': product_id, 'default_location_id': location_id, 'default_location_dest_id': location_dest_id, 'default_company_id': company_id}">
                        <div class="o_form_field o_stat_info">
                            <span class="o_stat_text">Detailed Operations</span>
                        </div>
                    </button>
                </xpath>

                <!-- stop edit in special case -->
                <xpath expr="//field[@name='move_line_ids_without_package']" position="attributes">
                    <attribute name="attrs">{'readonly': ['|', '|', '|', ('show_operations', '=', False), ('state', '=', 'cancel'), '&amp;', ('state', '=', 'done'), ('is_locked', '=', True), ('state', 'in', ['confirmed', 'assigned']),('enable_edit', '=', False)]}</attribute>
                </xpath>

                <xpath expr="//field[@name='move_ids_without_package']" position="attributes">
                    <attribute name="attrs">{'readonly': ['|', ('is_locked', '=', True), ('state', 'in', ['validate2','confirmed', 'assigned','done'])]}</attribute>
                    <!-- , ('enable_edit', '=', False) -->
                </xpath>
            </field>
        </record>
        
        <record id="view_stock_move_line_tree_done_qty" model="ir.ui.view">
            <field name="name">view.stock.picking.form.done.qty</field>
            <field name="model">stock.move.line</field>
            <field name="inherit_id" ref="stock.view_stock_move_line_detailed_operation_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='qty_done']" position="attributes">
                    <attribute name="attrs">{'readonly': ['|',('parent.state', 'in', ('validate2','done','cancel')),('state', 'in', ('done', 'cancel')), ('is_locked', '=', True)]}</attribute>
                </xpath>
            </field>
        </record>
        <record id="view_stock_move_line_tree_done_qty_simple" model="ir.ui.view">
            <field name="name">view.stock.picking.form.done.qty.simple</field>
            <field name="model">stock.move.line</field>
            <field name="inherit_id" ref="stock.view_stock_move_line_operation_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='qty_done']" position="attributes">
                    <attribute name="attrs">{'readonly': ['|', '|',('parent.state', 'in', ('validate2','done','cancel')), '&amp;', ('state', '=', 'done'), ('is_locked', '=', True), '&amp;', ('package_level_id', '!=', False), ('parent.picking_type_entire_packs', '=', True)]}</attribute>
                </xpath>
            </field>
        </record>


        <record model="ir.ui.view" id="stock_move_line_search_validate2">
            <field name="model">stock.move.line</field>
            <field name="inherit_id" ref="stock.stock_move_line_view_search"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='product_id']" position="replace">
                    <field name="picking_id" string="Transfer"/>
                </xpath>
                <xpath expr="//field[@name='picking_id']" position="replace">
                    <field name="product_id"/>
                </xpath>
            </field>
        </record>
        <record id="view_picking_move_line_tree" model="ir.ui.view">
            <field name="name">stock.picking.move.line.tree</field>
            <field name="model">stock.move.line</field>
            <field eval="50" name="priority"/>
            <field name="arch" type="xml">
                <!--TODO: Parent need to be reviewd-->
                <tree decoration-danger="state != 'done' " decoration-muted="state == 'cancel' " string="Stock Move Line" editable="bottom">
                    <field name="company_id" invisible="1" force_save="1"/>
                    <field name="picking_id" invisible="1" force_save="1"/>
                    <field name="move_id" invisible="1" force_save="1"/>
                    <field name="picking_type_entire_packs" invisible="1"/>
                    <field name="product_uom_category_id" invisible="1"/>
                    <field name="product_id" readonly='1'/>
                    <field name="package_level_id" invisible="1"/>
                    <field name="lots_visible" invisible="1"/>
                    <field name="location_id" options="{'no_create': True}" attrs="{'readonly': ['&amp;', ('package_level_id', '!=', False), ('picking_type_entire_packs', '=', True)]}" invisible="not context.get('show_source_location')" domain="[('id', 'child_of', location_id), '|', ('company_id', '=', False), ('company_id', '=', company_id)]" />
                    <field name="location_dest_id" width="0.75" attrs="{'readonly': ['&amp;', ('package_level_id', '!=', False), ('picking_type_entire_packs', '=', True)]}" invisible="not context.get('show_destination_location')" domain="[('id', 'child_of', location_dest_id), '|', ('company_id', '=', False), ('company_id', '=', company_id)]" />
                    <field name="lot_id" groups="stock.group_production_lot" attrs="{'invisible': [('lots_visible', '=', False)], 'readonly': ['&amp;', ('package_level_id', '!=', False), ('picking_type_entire_packs', '=', True)]}" invisible="not context.get('show_lots_m2o')" domain="[('product_id', '=', product_id), ('company_id', '=', company_id)]" context="{                             'active_picking_id': picking_id,                             'default_company_id': company_id,                             'default_product_id': product_id,                         }"/>
                    <field name="package_id" attrs="{'readonly': ['&amp;', ('package_level_id', '!=', False), ('picking_type_entire_packs', '=', True)]}" invisible="not context.get('show_package')" groups="stock.group_tracking_lot"/>
                    <field name="result_package_id" attrs="{'readonly': ['&amp;', ('package_level_id', '!=', False), ('picking_type_entire_packs', '=', True)]}" groups="stock.group_tracking_lot"/>
                    <field name="owner_id" attrs="{'readonly': ['&amp;', ('package_level_id', '!=', False), ('picking_type_entire_packs', '=', True)]}" invisible="not context.get('show_owner')" groups="stock.group_tracking_owner"/>
                    <field name="reserved_uom_qty" invisible="not context.get('show_reserved_quantity')" readonly="1"/>
                    <field name="state" readonly='1'/>
                    <field name="is_locked" invisible="1"/>
                    <field name="picking_code" invisible="1"/>
                    <field name="qty_done" attrs="{'readonly': ['|', '&amp;', ('state', '=', 'done'), ('is_locked', '=', True), '&amp;', ('package_level_id', '!=', False), ('picking_type_entire_packs', '=', True)]}"/>
                    <field name="product_uom_id" options="{'no_open': True, 'no_create': True}" readonly='1' string="Unit of Measure" groups="uom.group_uom"/>
                    <field name="create_date" optional="hide"/>
                </tree>
            </field>
        </record>
    </data>
</odoo>
