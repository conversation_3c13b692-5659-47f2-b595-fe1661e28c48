-*- coding: utf-8 -*-

from collections import defaultdict
from datetime import datetime
from dateutil.relativedelta import relativedelta
from itertools import groupby

from odoo import api, fields, models, SUPERUSER_ID, _
from odoo.addons.stock.models.stock_rule import ProcurementException


class StockLocation(models.Model):
    _inherit = 'stock.location'

    warehouse_team_id = fields.Many2one(comodel_name='crm.team', string='Warehouse Team',
                                        compute='_compute_warehouse_team_id', store=True, readonly=False)

    py_warehouse_id = fields.Many2one(
        'stock.warehouse', 'Warehouse', compute='_compute_warehouse_team_id', store=True, readonly=False)
    
    ro_is_main_location = fields.Boolean(compute='_compute_warehouse_team_id', store=True)

    @api.depends("name")
    def _compute_warehouse_team_id(self):
        for location in self:

            location.warehouse_team_id = False
            location.py_warehouse_id = False
            location.ro_is_main_location = False
            warehouse_id = location.warehouse_id

            if warehouse_id:
                location.warehouse_team_id = warehouse_id.crm_team_id
                location.py_warehouse_id = warehouse_id
                location.ro_is_main_location = True if not warehouse_id.parent_warehouse_id else False


class AccountAnalyticAccount(models.Model):

    _inherit = 'account.analytic.account'

    product_category_id = fields.Many2one(
        string='Product Category',
        comodel_name='product.category'
    )

    crm_team_id = fields.Many2one(
        string='CRM Team',
        comodel_name='crm.team'
    )

    _sql_constraints = [
        ('crmTeamIdUniq', 'unique(product_category_id, crm_team_id)',
         'Team and Category is in another analytic account!'),
    ]


class StockWarehouse(models.Model):

    _inherit = 'stock.warehouse'

    crm_team_id = fields.Many2one(
        string='CRM Team',
        comodel_name='crm.team'
    )

    
    parent_warehouse_id = fields.Many2one(
        string='Parent Stock Warehouse',
        comodel_name='stock.warehouse'
    )

    '''_sql_constraints = [
        ('crmTeamIdUniq', 'unique(crm_team_id)', 'Team is in another warehouse!'),
    ]'''


class AccountJournal(models.Model):

    _inherit = 'account.journal'

    crm_team_id = fields.Many2one(
        string='CRM Team',
        comodel_name='crm.team'
    )


class SaleOrderLine(models.Model):

    _inherit = 'sale.order.line'

    def _prepare_invoice_line(self, **optional_values):
        res = super()._prepare_invoice_line(**optional_values)

        if self.order_id.team_id and self.product_id.categ_id:

            account_analytic_id = self.env['account.analytic.account'].search(
                [('product_category_id', '=', self.product_id.categ_id.id), ('crm_team_id', '=', self.order_id.team_id.id)])

            if len(account_analytic_id) == 0:
                account_analytic_id = self.env['account.analytic.account'].search(
                    [('product_category_id', '=', self.product_id.categ_id.parent_id.id), ('crm_team_id', '=', self.order_id.team_id.id)])

            res['analytic_account_id'] = account_analytic_id

        return res


class SaleOrderInh(models.Model):

    _inherit = 'sale.order'

    @api.onchange('team_id')
    def _onchange_field_team(self):

        if self.team_id:
            warehouse = self.env['stock.warehouse'].search(
                [('crm_team_id', '=', self.team_id.id)], limit=1)
            if warehouse:
                self.warehouse_id = warehouse


class PurchaseOrderLine(models.Model):

    _inherit = 'purchase.order.line'

    @api.model
    def _prepare_purchase_order_line_from_procurement(self, product_id, product_qty, product_uom, company_id, values, po):

        res = super(PurchaseOrderLine, self)._prepare_purchase_order_line_from_procurement(
            product_id, product_qty, product_uom, company_id, values, po)

        # add analytic line based on product category and sales team
        analytic_account = self._get_analytic_account(po, product_id)
        if analytic_account:
            res['account_analytic_id'] = analytic_account

        return res

    def _get_analytic_account(self, po, product_id):

        result = False

        so = po._get_sale_orders().ids

        if so:
            account_analytic_id = self.env['account.analytic.account'].search([
                ('product_category_id', '=', product_id.categ_id.id), ('crm_team_id', '=', so[0].team_id.id)])

            if account_analytic_id:
                result = account_analytic_id.id

        return result


class AccountPayment(models.TransientModel):
    _inherit = "account.payment.register"

    team_id = fields.Many2one(
        string='Team',
        comodel_name='crm.team'
    )

    @api.model
    def default_get(self, fields_list):

        res = super().default_get(fields_list)

        team_id = self._context.get('team_sales')

        if team_id:
            res['team_id'] = team_id
        else:
            res['team_id'] = False

        return res

    journal_id = fields.Many2one(
        domain="[]")
        # domain="[('crm_team_id', '=', team_id),('company_id', '=', company_id), ('type', 'in', ('bank', 'cash'))]")

    @api.onchange('payment_date')
    def _compute_journal_filter(self):
        b={}
        for rec in self:
            user_flag = self.env['res.users'].has_group('account.group_account_manager')
            
            if user_flag == True:
                b ={'domain': {
                        'journal_id': "[('company_id', '=', company_id), ('type', 'in', ('bank', 'cash'))]",
                    }}
            else:
                b ={'domain': {
                        'journal_id': "[('crm_team_id', '=', team_id),('company_id', '=', company_id), ('type', 'in', ('bank', 'cash'))]",
                    }}
            # b ={'domain': {
            #             'journal_id': [('crm_team_id', '=', self.team_id),('company_id', '=', self.env.company.id), ('type', 'in', ('bank', 'cash'))],
            #         }}
        return b

    @api.depends('can_edit_wizard', 'company_id')
    def _compute_journal_id(self):
        super(AccountPayment, self)._compute_journal_id()
        for wizard in self:
            if wizard.team_id:
                user_flag = self.env['res.users'].has_group('account.group_account_manager')
                if user_flag == True:
                    wizard.journal_id = self.env['account.journal'].search(
                        [('type', 'in', ['bank', 'cash']),
                            ('company_id', '=', wizard.company_id.id)], limit=1)  
                else:  
                    wizard.journal_id = self.env['account.journal'].search(
                        [('crm_team_id', '=', wizard.team_id.id), ('type', 'in', ['bank', 'cash']),
                            ('company_id', '=', wizard.company_id.id)], limit=1)


class AccountInvoice(models.Model):
    _inherit = "account.move"

    order_id = fields.Many2one(
        string='Sale Order',
        comodel_name='sale.order',
        compute='_compute_sale_order',
        store=True
    )

    @api.depends('invoice_origin')
    def _compute_sale_order(self):
        for move in self:
            sale_order = self.env['sale.order'].search(
                [('name', '=', move.invoice_origin)])

            if sale_order:
                move.order_id = sale_order
            else:
                move.order_id = False


class AccountInvoiceLine(models.Model):
    _inherit = "account.move.line"

    @api.onchange('product_id')
    def _onchange_amount_pr_id(self):

        if self.move_id.team_id and self.product_id.categ_id:

            account_analytic_id = self.env['account.analytic.account'].search(
                [('product_category_id', '=', self.product_id.categ_id.id), ('crm_team_id', '=', self.move_id.team_id.id)])

            if len(account_analytic_id) == 0:
                account_analytic_id = self.env['account.analytic.account'].search(
                    [('product_category_id', '=', self.product_id.categ_id.parent_id.id), ('crm_team_id', '=', self.move_id.team_id.id)])

            self.analytic_account_id = account_analytic_id
