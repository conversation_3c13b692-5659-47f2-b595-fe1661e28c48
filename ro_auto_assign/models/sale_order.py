# -*- coding: utf-8 -*-


from odoo import api, fields, models, SUPERUSER_ID, _

from odoo.addons.sale.models.sale_order import SaleOrder


def _create_analytic_account(self, prefix=None):
    """ Create a new analytic account for the given orders.

    :param str prefix: if specified, the account name will be '<prefix>: <so_reference>'.
        If not, the account name will be the Sales Order reference.
    :return: None
    """
    pass
    # for order in self:
    #     analytic = self.env['account.analytic.account'].create(order._prepare_analytic_account_data(prefix))
    #     order.analytic_account_id = analytic
SaleOrder._create_analytic_account = _create_analytic_account

def _action_confirm(self):
    """ Implementation of additional mechanism of Sales Order confirmation.
        This method should be extended when the confirmation should generated
        other documents. In this method, the SO are in 'sale' state (not yet 'done').
    """
    pass
    # create an analytic account if at least an expense product
    # for order in self:
    #     if any(expense_policy not in [False, 'no'] for expense_policy in order.order_line.product_id.mapped('expense_policy')):
    #         if not order.analytic_account_id:
    #             order._create_analytic_account()
SaleOrder._action_confirm = _action_confirm

class SaleOrderInh(models.Model):
    _inherit = 'sale.order'

    

    @api.onchange('team_id')
    def _onchange_field_team(self):

        if self.team_id:
            warehouse = self.env['stock.warehouse'].search(
                [('crm_team_id', '=', self.team_id.id)], limit=1)
            if warehouse:
                self.warehouse_id = warehouse

    @api.depends('user_id', 'company_id','team_id')
    def _compute_warehouse_id(self):
        res = super(SaleOrderInh, self)._compute_warehouse_id()
        for order in self:
            if order.team_id:
                warehouse = self.env['stock.warehouse'].search(
                    [('crm_team_id', '=', order.team_id.id)], limit=1)
                if warehouse:
                    order.warehouse_id = warehouse
        
        return res
        # for order in self:
        #     default_warehouse_id = self.env['ir.default'].with_company(
        #         order.company_id.id).get_model_defaults('sale.order').get('warehouse_id')
        #     if order.company_id and order.company_id != order._origin.company_id:
        #         warehouse = default_warehouse_id
        #     else:
        #         warehouse = self.env['stock.warehouse']
        #     if order.state in ['draft', 'sent']:
        #         order.warehouse_id = warehouse or order.user_id.with_company(order.company_id.id)._get_default_warehouse_id()
        #     # In case we create a record in another state (eg: demo data, or business code)
        #     if not order.warehouse_id:
        #         order.warehouse_id = self.env.user._get_default_warehouse_id()
