# -*- coding: utf-8 -*-


from odoo import api, fields, models, SUPERUSER_ID, _

class StockLocation(models.Model):
    _inherit = 'stock.location'

    warehouse_team_id = fields.Many2one(comodel_name='crm.team', string='Warehouse Team',
                                        compute='_compute_warehouse_team_id', store=True, readonly=False)

    ro_is_main_location = fields.Boolean(compute='_compute_warehouse_team_id', store=True)

    @api.depends("name")
    def _compute_warehouse_team_id(self):
        for location in self:

            location.warehouse_team_id = False
            location.ro_is_main_location = False
            warehouse_id = location.warehouse_id

            if warehouse_id:
                location.warehouse_team_id = warehouse_id.crm_team_id
                location.ro_is_main_location = True if not warehouse_id.parent_warehouse_id else False
