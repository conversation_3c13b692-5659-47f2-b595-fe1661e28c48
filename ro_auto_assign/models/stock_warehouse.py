# -*- coding: utf-8 -*-


from odoo import api, fields, models, SUPERUSER_ID, _

class StockWarehouse(models.Model):

    _inherit = 'stock.warehouse'

    crm_team_id = fields.Many2one(
        string='CRM Team',
        comodel_name='crm.team'
    )

    # Only user can validate transfer
    stock_user_id = fields.Many2one(
        string='User Stock',
        comodel_name='res.users'
    )

    
    parent_warehouse_id = fields.Many2one(
        string='Parent Stock Warehouse',
        comodel_name='stock.warehouse'
    )
