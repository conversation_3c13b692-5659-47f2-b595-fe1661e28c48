odoo.define('ro_add_field_order_pos.add_field', function (require) {

    "use strict";
    const { _t } = require('web.core');


    const TicketScreen = require('point_of_sale.TicketScreen');
    const Registries = require('point_of_sale.Registries');
    
    const PosTicketScreen = TicketScreen => class extends TicketScreen {
       
        getJournal(order) {
            const paymentLines = order.get_paymentlines();
            const journals = new Set();

            paymentLines.forEach(paymentLine => {
                const paymentMethod = paymentLine.payment_method;
                journals.add(paymentMethod.journal_id[1]); // [1] is the name of the journal
            });

            return Array.from(journals).join(', '); // Convert Set to Array and join with commas
        }
    };
    Registries.Component.extend(TicketScreen, PosTicketScreen);

    return PosTicketScreen;
});

    

    