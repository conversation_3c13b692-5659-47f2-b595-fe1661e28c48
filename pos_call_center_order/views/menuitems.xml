<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="action_call_center_sale_orders" model="ir.actions.act_window">
            <field name="res_model">sale.order</field>
            <field name="view_mode">tree</field>
            <field name="domain">[('is_call_center','=',True),('user_id', '=', uid), ('state', 'in', ('sale','done')),'|' ,('delivery_employee_id','=',False),('is_delivered','=', False)]</field>
            <!-- <field name="search_view_id" ref="view_account_invoice_report_search"/> -->
            <field name="view_id" ref="pos_call_center_sale_order_tree"/>
        </record>

        <menuitem
            id="menu_call_center_sale_orders"
            name="Call Center Orders"
            action="action_call_center_sale_orders"
            parent="point_of_sale.menu_point_of_sale"
            sequence="4"/>
    </data>
</odoo>