<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="pos_call_center_sale_order_mark_as_delivered" model="ir.actions.server">
            <field name="name">Mark as Delivered</field>
            <field name="model_id" ref="sale.model_sale_order"/>
            <field name="groups_id" eval="[(4, ref('point_of_sale.group_pos_user'))]"/>
            <field name="binding_model_id" ref="sale.model_sale_order" />
            <field name="state">code</field>
            <field name="binding_view_types">tree</field>
            <field name="code">
                for rec in records:
                    rec.write({'is_delivered': True})
            </field>
        </record>
    </data>
</odoo>