# -*- coding: utf-8 -*-

import logging
from psycopg2 import sql

from odoo import models, fields, api
from odoo import tools
from datetime import datetime, time, timedelta

_logger = logging.getLogger(__name__)



class StockMoveProductInit(models.Model):
    _name = "stock.move.product.line"
    _description = "Stock Move Product Line"
    _auto = False
    _check_company_auto = True

    
    product_id = fields.Many2one(
        'product.product', string='Product', readonly=True)
    location_id = fields.Many2one(
        "stock.location", string="Location", readonly=True)

    company_id = fields.Many2one('res.company', string='Company')

    location_usage = fields.Selection(
        related='location_id.usage', string="Location Type")

    qty_init = fields.Float(string='init Qty', readonly=True,
                            digits=(16, 2), group_operator="sum")
    qty_end = fields.Float(string='End Qty', readonly=True,
                           digits=(16, 2), group_operator="sum")

    add_in = fields.Float(string='In', readonly=True,
                          digits=(16, 2), group_operator="sum")
    ded_in = fields.Float(string='Out', readonly=True,
                          digits=(16, 2), group_operator="sum")
    
    total_value = fields.Float(string='Total Value',digits='WS Price')
   

    def _view_internal(self, date_from_filter, date_to_filter):
        if date_to_filter:
            date_to_filter = date_to_filter + timedelta(days=1)
        view_str = """
            select
            ROW_NUMBER() OVER () AS id,
            location_id,
            location_usage,
            product_id,
            sum(qty_init) qty_init,
            sum(qty_current_sum) qty_end,

            case
                when sum(qty_current_sum) = 0 
                    then 0
                
                else
                    coalesce (sum(total_line_value))                                
            end as total_value, 
            --sum(total_line_value) total_value
            
            company_id,

            sum(qty_ded_init) ded_init,
            sum(qty_ded_end) ded_end,
            sum(qty_ded_in) ded_in,
            sum(qty_add_init) add_init,
            sum(qty_add_end) add_end,
            sum(qty_add_in) add_in

            from	
            (

                with stock_valuation_layer_total as (
                    select  min(distinct svl.id) id ,  sum(distinct svl.value) valuation_value, min(distinct svl.stock_move_id) move_id, min(distinct svl.product_id) product_id, min(distinct svl.company_id) company_id from stock_valuation_layer svl
                        left join stock_move sm on sm.id = svl.stock_move_id 
                        left join stock_scrap ss on svl.product_id = ss.product_id and sm.picking_id = ss.picking_id
                        group by svl.stock_move_id
                    )

                select
                distinct *,
                sum(qty_current_sum) over (partition by product_id, warehouse_id order by id asc rows between 
                unbounded preceding and current row) as qty_current
                from

                (

                    select
                    concat('in',sml.id) id,
                    sml.move_id,
                    sl.id as location_id,
                    sl.usage as location_usage,
                    sml.product_id,
                    pr.id product_tmpl_id,
                    uom_template.id uom_id,
                    sml.product_uom_id,
                    wh.id warehouse_id,
                    coalesce (sm.partner_id,
                    sp.partner_id) as partner_id,
                    sml.reference as description,
                    (sml.qty_done / nullif(coalesce(uom.factor, 1) / coalesce(uom_template.factor, 1), 0.0)) as qty_on_hand,
                    (sml.qty_done / nullif(coalesce(uom.factor, 1) / coalesce(uom_template.factor, 1), 0.0)) as qty_current_sum,

                    case
                        when sll.usage = 'internal' and spt.code = 'internal'
                            then 0
                        
                        else
                            coalesce (svlt.valuation_value/sm.product_qty,0) * (sml.qty_done / nullif(coalesce(uom.factor, 1) / coalesce(uom_template.factor, 1), 0.0))
                                        
                    end as total_line_value,
                    --(sml.qty_done / nullif(coalesce(uom.factor, 1) / coalesce(uom_template.factor, 1), 0.0)) * sm.value as total_line_value,
                    (sml.qty_done / nullif(coalesce(uom.factor, 1) / coalesce(uom_template.factor, 1), 0.0)) qty_add,
                    0 qty_ded,

                    sml.company_id,

                    ------
                    case
     					--when sml.qty_done > 0 sml.date < '2023-7-1'::date  --1
                        when sml.qty_done > 0 %s
                            then (sml.qty_done / nullif(coalesce(uom.factor, 1) / coalesce(uom_template.factor, 1), 0.0))
                            else 0 
                        end qty_init,
                    -----
                    case
                        --when sml.date < '2023-7-1'::date  --2
                        when sml.qty_done > 0 %s 
                            then (sml.qty_done / nullif(coalesce(uom.factor, 1) / coalesce(uom_template.factor, 1), 0.0))
                            else 0 
                        end qty_add_init,

                    (sml.qty_done / nullif(coalesce(uom.factor, 1) / coalesce(uom_template.factor, 1), 0.0)) qty_add_end,

                    case
                        --when sml.date >= '2023-7-1'::date  --3
                        when sml.qty_done > 0 %s 
                            then (sml.qty_done / nullif(coalesce(uom.factor, 1) / coalesce(uom_template.factor, 1), 0.0))
                            else 0 
                        end qty_add_in,
                  
					-------------
                    0 qty_ded_init,
                    0 qty_ded_end,
                    0 qty_ded_in,
                    ---- adj ----
                   
                    ---- end adj ----

                    ---- orders -----

                    ---- end orders -----
                    sml.picking_id,
                    sll.complete_name as location_name_from ,
                    sl.complete_name as location_name_to,
                    case
                        when 
                            sll.usage != 'internal' and spt.code = 'internal'
                            then 'incoming' 
                        else coalesce(spt.code,'incoming') end move_type,
                    spt.code
                   

                  from
                                stock_move_line sml
                        left join stock_move sm on
                                sm.id = sml.move_id
                                --left join stock_valuation_layer svl ON sm.id=svl.stock_move_id
                        left join stock_location sll on
                                sml.location_id = sll.id
                        left join stock_location sl on
                                sml.location_dest_id = sl.id
                        left join stock_picking sp on
                                sp.id = sml.picking_id

                        left join stock_warehouse wh on
                                wh.view_location_id = CAST(split_part(sl.parent_path,'/',2) as int)

                        left join stock_picking_type spt on
                                sp.picking_type_id = spt.id
                        left join product_product pp on
                                sml.product_id = pp.id
                        left join product_template pr on
                                                pp.product_tmpl_id = pr.id

                        left join stock_valuation_layer_total svlt on
                                svlt.product_id = pp.id and svlt.move_id = sm.id

                        left join uom_uom uom on
                                                sml.product_uom_id = uom.id	        
                        left join uom_uom uom_template on
                                uom_template.id = pr.uom_id



                        where
                    --sl.usage = 'internal'
                    -- and 
                    sml.state != 'cancel'
                    and sml.company_id = sl.company_id
                    and sm.product_qty != 0
                    and sml.state = 'done'

                  --  and pr.id = 21338
                    --and sml.date <= '2023-12-1'::date  --6

                    %s
                        union all

                        select
                    concat('out',sml.id) id,
                    sml.move_id,
                    sl.id as location_id,
                    sl.usage as location_usage,
                    sml.product_id,
                    pr.id product_tmpl_id,
                    uom_template.id uom_id,
                    sml.product_uom_id,
                    wh.id warehouse_id,
                    coalesce (sm.partner_id,
                    sp.partner_id) as partner_id,
                    sml.reference as description,
                    -(sml.qty_done / nullif(coalesce(uom.factor, 1) / coalesce(uom_template.factor, 1), 0.0)) as qty_on_hand,
                    -(sml.qty_done / nullif(coalesce(uom.factor, 1) / coalesce(uom_template.factor, 1), 0.0)) as qty_current_sum,
                    case
                        when sll.usage = 'internal' and spt.code = 'internal'
                            then 0
                        
                        else
                            coalesce (svlt.valuation_value/sm.product_qty,0) * (sml.qty_done / nullif(coalesce(uom.factor, 1) / coalesce(uom_template.factor, 1), 0.0))
                                        
                    end as total_line_value,

                   -- -(sml.qty_done / nullif(coalesce(uom.factor, 1) / coalesce(uom_template.factor, 1), 0.0)) * sm.value as total_line_value,
                    0 qty_add,
                    (sml.qty_done / nullif(coalesce(uom.factor, 1) / coalesce(uom_template.factor, 1), 0.0)) qty_ded,

                    sml.company_id,

                    ------
                    case
                        --when sml.date < '2023-7-1'::date --7
                        when sml.qty_done > 0 %s
                            then -(sml.qty_done / nullif(coalesce(uom.factor, 1) / coalesce(uom_template.factor, 1), 0.0))
                            else 0 
                        end qty_init,
                    -----

                    0 qty_add_init,
                    0 qty_add_end,
                    0 qty_add_in,
					-------------------
                    case
                        --when sml.date < '2023-7-1'::date  --8
                        when sml.qty_done > 0 %s
                            then (sml.qty_done / nullif(coalesce(uom.factor, 1) / coalesce(uom_template.factor, 1), 0.0))
                            else 0 
                        end qty_ded_init,
                    (sml.qty_done / nullif(coalesce(uom.factor, 1) / coalesce(uom_template.factor, 1), 0.0)) qty_ded_end,
                    case
                        --when sml.date >= '2023-7-1'::date   --9
                        when sml.qty_done > 0 %s 
                            then (sml.qty_done / nullif(coalesce(uom.factor, 1) / coalesce(uom_template.factor, 1), 0.0))
                            else 0 
                        end qty_ded_in,
                   
                    ---- adj ----
                  
                    ---- end adj ----

                    ---- orders -----
                   
                    ---- end orders -----

                    sml.picking_id,
                    /*to_char(sm.location_dest_id,'999') as location_name_to,*/
                    sl.complete_name as location_name_from,
                                sll.complete_name as location_name_to,
                    case
                        when 
                            sll.usage != 'internal' and spt.code = 'internal'
                            then 'outgoing' 
                        else coalesce(spt.code,'outgoing') end move_type,
                    spt.code
                

                  from
                                stock_move_line sml
                        left join stock_move sm on
                                sm.id = sml.move_id
                                --left join stock_valuation_layer svl ON sm.id=svl.stock_move_id
                        left join stock_location sll on
                                sml.location_dest_id = sll.id
                        left join stock_location sl on
                                sml.location_id = sl.id
                        left join stock_picking sp on
                                sp.id = sml.picking_id

                        left join stock_warehouse wh on
                                wh.view_location_id = CAST(split_part(sl.parent_path,'/',2) as int)

                        left join stock_picking_type spt on
                                sp.picking_type_id = spt.id
                        left join product_product pp on
                                sml.product_id = pp.id
                        left join product_template pr on
                                pp.product_tmpl_id = pr.id
                        
                        left join stock_valuation_layer_total svlt on
                                svlt.product_id = pp.id and svlt.move_id = sm.id

                        left join uom_uom uom on
                                sml.product_uom_id = uom.id
                        left join uom_uom uom_template on
                                uom_template.id = pr.uom_id




                        where
                              --  sl.usage = 'internal'
                        --and 
                        sml.state != 'cancel'
                        and sml.company_id = sl.company_id
                        and sm.product_qty != 0
                        and sml.state = 'done'  

     				--	and pr.id = 21338
    -- 					and sml.date <= '2023-12-1'::date  --16
                        %s


                    union all

                select
                    concat('othblc',svl.id) id,
                    null move_id,
                    null location_id,
                    null location_usage,
                    svl.product_id,
                    null product_tmpl_id,
                    null uom_id,
                    null product_uom_id,
                    null warehouse_id,
                    null as partner_id,
                    svl.description as description,
                    svl.quantity as qty_on_hand,
                    svl.quantity as qty_current_sum,

                    case 
                        when svl.quantity >= 0
                        then svl.value 
                        else
                            svl.value * -1    
                    end as total_line_value,
                    
                    coalesce(case when svl.quantity >= 0
                    then quantity end,0) as qty_add,
                    
                    coalesce(case when svl.quantity < 0
                    then abs(quantity) end,0) as qty_ded,
                    
                    svl.company_id,

                    0 qty_init,
                    0 qty_add_init,
                    0 qty_add_end,
                    0 qty_add_in,
                    
                    0 qty_ded_init,
                    0 qty_ded_end,
                    0 qty_ded_in,
                    
                    null picking_id,
                    
                    null location_name_from ,
                    null location_name_to,
                    
                    null as move_type,
                    
                    null as code
    
                    from stock_valuation_layer svl where svl.stock_move_id is null
                    %s

                ) sml

            ) sml2

            Group by product_id, location_id, location_usage, company_id
            Having location_usage = 'internal' and ( sum(qty_init)!=0 or sum(qty_ded_in)!=0 or sum(qty_add_in)!=0 )

        """ % (
            "and sml.date < '%s'" % (
                date_from_filter) if date_from_filter else "", # 1
            "and sml.date < '%s'" % (
                date_from_filter) if date_from_filter else "", # 2
            "and sml.date >= '%s'" % (
                date_from_filter) if date_from_filter else "",  # 3
           
            "and sml.date <= '%s'" % (
                date_to_filter) if date_to_filter else "", # 6
            "and sml.date < '%s'" % (
                date_from_filter) if date_from_filter else "", # 7
            "and sml.date < '%s'" % (
                date_from_filter) if date_from_filter else "", # 8
            "and sml.date >= '%s'" % (
                date_from_filter) if date_from_filter else "",  # 9
            "and sml.date <= '%s'" % (date_to_filter) if date_to_filter else "", #16
            "and svl.create_date < '%s'" % (date_to_filter) if date_to_filter else "" #16
        )

        return view_str

    def init(self):
        tools.drop_view_if_exists(self.env.cr, self._table)
        date_from_filter = self._context.get('date_from_filter')
        date_to_filter = self._context.get('date_to_filter')

        self.env.cr.execute(sql.SQL("CREATE or REPLACE VIEW {} as ({})").format(
            sql.Identifier(self._table), sql.SQL(self._view_internal(date_from_filter, date_to_filter))))
        # res = self.env.cr.fetchone()
        # print(res)
        # print("res")
        # print(re)
