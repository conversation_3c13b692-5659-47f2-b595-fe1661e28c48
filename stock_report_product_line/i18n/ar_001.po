# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_report_product_line
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-11-27 12:26+0000\n"
"PO-Revision-Date: 2023-11-27 12:26+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: stock_report_product_line
#: model:ir.model.fields,help:stock_report_product_line.field_stock_move_product_line__location_usage
msgid ""
"* Vendor Location: Virtual location representing the source location for products coming from your vendors\n"
"* View: Virtual location used to create a hierarchical structures for your warehouse, aggregating its child locations ; can't directly contain products\n"
"* Internal Location: Physical locations inside your own warehouses,\n"
"* Customer Location: Virtual location representing the destination location for products sent to your customers\n"
"* Inventory Loss: Virtual location serving as counterpart for inventory operations used to correct stock levels (Physical inventories)\n"
"* Production: Virtual counterpart location for production operations: this location consumes the components and produces finished products\n"
"* Transit Location: Counterpart location that should be used in inter-company or inter-warehouses operations"
msgstr ""

#. module: stock_report_product_line
#: model:ir.model.fields,field_description:stock_report_product_line.field_stock_move_product_line__adj_n_qty
msgid "Adj n Qty"
msgstr "تسويه سالبة"

#. module: stock_report_product_line
#: model:ir.model.fields,field_description:stock_report_product_line.field_stock_move_product_line__adj_p_qty
msgid "Adj p Qty"
msgstr "تسويه موجبه"

#. module: stock_report_product_line
#: model:ir.model.fields,field_description:stock_report_product_line.field_wizard_stock_move_product_line__date_from
msgid "Date From"
msgstr "من تاريخ"

#. module: stock_report_product_line
#: model:ir.model.fields,field_description:stock_report_product_line.field_wizard_stock_move_product_line__date_to
msgid "Date To"
msgstr "الي تاريخ"

#. module: stock_report_product_line
#: model:ir.model.fields,field_description:stock_report_product_line.field_stock_move_product_line__qty_end
msgid "End Qty"
msgstr "رصيد ختامي"

#. module: stock_report_product_line
#: model:ir.model.fields,field_description:stock_report_product_line.field_stock_move_product_line__add_in
msgid "In"
msgstr "وارد"

#. module: stock_report_product_line
#: model:ir.model.fields,field_description:stock_report_product_line.field_stock_move_product_line__qty_add_internal
msgid "In Internal"
msgstr "تحميلات موجبه"

#. module: stock_report_product_line
#: model:ir.model.fields,field_description:stock_report_product_line.field_stock_move_product_line__location_id
#: model:ir.model.fields,field_description:stock_report_product_line.field_wizard_stock_move_product_line__location_ids
#: model_terms:ir.ui.view,arch_db:stock_report_product_line.stock_move_product_line_view_search
msgid "Location"
msgstr ""

#. module: stock_report_product_line
#: model:ir.model.fields,field_description:stock_report_product_line.field_stock_move_product_line__ded_in
msgid "Out"
msgstr "منصرف"

#. module: stock_report_product_line
#: model:ir.model.fields,field_description:stock_report_product_line.field_stock_move_product_line__qty_ded_internal
msgid "Out Internal"
msgstr "تحميلات سالبه"

#. module: stock_report_product_line
#: model:ir.model.fields,field_description:stock_report_product_line.field_stock_move_product_line__po_qty
msgid "PO Qty"
msgstr "مشتريات"

#. module: stock_report_product_line
#: model:ir.model.fields,field_description:stock_report_product_line.field_stock_move_product_line__po_return_qty
msgid "PO return Qty"
msgstr "مرتجع مشتريات"

#. module: stock_report_product_line
#: model:ir.model.fields,field_description:stock_report_product_line.field_stock_move_product_line__product_id
#: model:ir.model.fields,field_description:stock_report_product_line.field_wizard_stock_move_product_line__product_ids
#: model_terms:ir.ui.view,arch_db:stock_report_product_line.stock_move_product_line_view_search
msgid "Product"
msgstr "المنتج"

#. module: stock_report_product_line
#: model:ir.model.fields,field_description:stock_report_product_line.field_stock_move_product_line__so_qty
msgid "SO Qty"
msgstr "مبيعات"

#. module: stock_report_product_line
#: model:ir.model.fields,field_description:stock_report_product_line.field_stock_move_product_line__so_return_qty
msgid "SO return Qty"
msgstr "مرتجع مبيعات"

#. module: stock_report_product_line
#: model:ir.model.fields,field_description:stock_report_product_line.field_stock_move_product_line__qty_init
msgid "init Qty"
msgstr "رصيد افتتاحي"
