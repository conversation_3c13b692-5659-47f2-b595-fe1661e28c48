# -*- coding: utf-8 -*-

from odoo import models, fields, api, _, exceptions
from odoo.exceptions import UserError
from datetime import datetime, time


class WizardStockMoveProductInit(models.TransientModel):
    _name = 'wizard.stock.move.product.line'
    _description = 'Wizard Stock Move Product Line'
  
    product_ids = fields.Many2many('product.product')
    location_ids = fields.Many2many('stock.location',domain="[('usage', '=', 'internal')]")

    date_from = fields.Date("Date From", required=True)
    date_to = fields.Date("Date To", required=True)

    def action_get_data(self):

        view_id = self.env.ref('stock_report_product_line.stock_move_product_line_view_tree').id
        domain = []

        if self.product_ids:
            domain.append(('product_id','in',self.product_ids.ids))
        if self.location_ids:
            domain.append(('location_id','in',self.location_ids.ids))

        if len(domain) > 0 or self.date_from or self.date_to:
            self.env['stock.move.product.line'].with_context({
                'date_from_filter': self.date_from,
                'date_to_filter': self.date_to
            }).init()
            return {
                    'name': _('Stock Move Product Line'),
                    'view_mode': 'tree',
                    'res_model': 'stock.move.product.line',
                    'domain': domain,
                    'context': {
                                'group_by':['location_id']
                                },
                    'view_id': view_id,
                    'type': 'ir.actions.act_window'
                }
        else:
            return {
                    'name': _('Stock Move Product Line'),
                    'view_mode': 'tree',
                    'res_model': 'stock.move.product.line',
                    'context': {
                    },
                    'view_id': view_id,
                    'type': 'ir.actions.act_window'
                }
