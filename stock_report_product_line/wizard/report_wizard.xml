<odoo>
    <record id="wizard_stock_move_product_line_view" model="ir.ui.view">
        <field name="name">wizard_stock_move_product_line_view</field>
        <field name="model">wizard.stock.move.product.line</field>
        <field name="arch" type="xml">
            <form string="Account Filtration">
                <group>
                    <group>
                        <field name="date_from"/>
                        <field name="date_to"/>
                    </group>
                    <group>
                        <field name="product_ids" widget="many2many_tags"/>
                        <field name="location_ids" widget="many2many_tags"/>
                    </group>
                </group>
                <footer>
                    <button name="action_get_data" string="Run" type="object" class="oe_highlight"/>
                    <button string="Cancel" class="oe_highlight" special="cancel" />
                </footer>
            </form>
        </field>
    </record>
    <record model="ir.actions.act_window" id="action_wizard_stock_move_product_line">
        <field name="name">Stock Move Product Line</field>
        <field name="res_model">wizard.stock.move.product.line</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="wizard_stock_move_product_line_view"/>
        <field name="target">new</field>
    </record>

    <menuitem id="menu_wizard_stock_move_product_line" name="Stock Move Product Line"
        action="action_wizard_stock_move_product_line"
        parent="stock.menu_warehouse_report" sequence="259"/>
</odoo>