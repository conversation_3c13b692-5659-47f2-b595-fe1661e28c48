<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data>
        <record id="stock_move_product_line_view_search" model="ir.ui.view">
            <field name="name">stock_move_product_line_view_search</field>
            <field name="model">stock.move.product.line</field>
            <field name="arch" type="xml">
                <search>
                    <field name="product_id"/>
                    <field name="location_id" string="Location"/>
                    <group expand="1" string="Group by">
                        <filter string="Product" name="product_id" context="{'group_by': 'product_id'}"/>
                        <filter string="Location" name="location_id" context="{'group_by': 'location_id'}"/>
                    </group>
                </search>
            </field>
        </record>

        <record id="stock_move_product_line_view_tree" model="ir.ui.view">
            <field name="name">stock_move_product_line_tree</field>
            <field name="model">stock.move.product.line</field>
            <field name="arch" type="xml">
                <tree string="Location Product Moves">
                    <field name="location_usage"  invisible="1"/>
                    <field name="product_id"  optional="show"/>
                    <field name="location_id" optional="show"/>
                    <field name="qty_init" optional="show"/>
                    <field name="add_in" optional="show"/>
                    <field name="ded_in" optional="show"/>
                    <field name="qty_end" optional="show"/>
                    <field name="total_value" sum="Total Value"/> 

                    
                    </tree>
            </field>
        </record>

        <record id="stock_move_product_line_action_detaild" model="ir.actions.act_window" >
            <field name="name">stock_move_product_line_action</field>
            <field name="res_model">stock.move.product.line</field>
            <field name="context">{'group_by':['product_id']}</field>
            <field name="view_mode">tree</field>
            <field name="view_id" ref="stock_move_product_line_view_tree"/>
            <field name="search_view_id" ref="stock_move_product_line_view_search"/>
        </record>
    </data>
</odoo>