# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError

class ResPartner(models.Model):
    _inherit = 'res.partner'
    
    ro_registration_number = fields.Char(string="Registration Number")

    @api.constrains('phone', 'vat', 'mobile' , 'ro_registration_number')
    def _check_unique_fields(self):
        for partner in self:
            domain = []

            if partner.phone:
                domain += [('phone', '=', partner.phone)]

            if partner.mobile:
                if domain:
                    domain.insert(0, '|')

                domain += [('mobile', '=', partner.mobile)]
            
            if partner.ro_registration_number and (partner.company_type == 'company' or not partner.parent_id.id):
                if domain:
                    domain.insert(0, '|')

                domain += [('ro_registration_number', '=', partner.ro_registration_number)]

            if partner.vat and (partner.company_type == 'company' or not partner.parent_id.id):
                if domain:
                    domain.insert(0, '|')

                domain += [('vat', '=', partner.vat)]

            #>1 because current partner is counted
            if domain and self.search_count(domain) > 1:
                raise ValidationError(_("Phone, Mobile and Vat must be unique!"))
    
    # Overwrite main method
    def _phone_format(self, number, country=None, company=None, force_format='E164'):
        return number
                
    def _sanitize_phone(self, phone):
        # Remove leading '+2' and spaces
        sanitized_phone = phone.lstrip('+2').replace(' ', '')
        return sanitized_phone

    @api.model_create_multi
    def create(self, vals_list):
        for vals in vals_list:
            if vals.get('phone', False):
                vals['phone'] = self._sanitize_phone(vals['phone'])

            if vals.get('mobile', False):
                vals['mobile'] = self._sanitize_phone(vals['mobile'])

        return super(ResPartner, self).create(vals_list)
    
    def write(self, vals):
        if vals.get('phone', False):
            vals['phone'] = self._sanitize_phone(vals['phone'])

        if vals.get('mobile', False):
            vals['mobile'] = self._sanitize_phone(vals['mobile'])

        return super(ResPartner, self).write(vals)
