from odoo import models, fields, api,_, SUPERUSER_ID
from num2words import num2words

class SaleOrder(models.Model):
    _inherit = "sale.order"

    py_total_discount = fields.Float(
        'Total discount',
        compute='_compute_py_discount_total'
    )

    total_price = fields.Float('Total Price', compute='_total_price')

    @api.depends('order_line')
    def _compute_py_discount_total(self):
        total_discount = 0
        for record in self:
            for rec in record.order_line:
                total_discount += (rec.price_unit * rec.product_uom_qty) - rec.price_subtotal
            record.py_total_discount = float(total_discount)

    @api.depends('order_line')
    def _total_price(self):
        total_price = 0
        for record in self:
            for rec in record.order_line:
                total_price += (rec.price_unit * rec.product_uom_qty)
            record.total_price = total_price

    def change_size_page_layout(self, items):
        paper_format = self.env.ref('ro_receipt_layout.paperformat_portrait')
        paper_format.with_user(SUPERUSER_ID).page_height = 120 + (len(items) * 20)



class AccountMove(models.Model):
    _inherit = "account.move"

    py_total_discount = fields.Float(
        'Total discount',
        compute='_compute_py_discount_total',
    )

    py_payments = fields.Text("payments", compute='_method_py_payments')
    total_amount_before_discount = fields.Float('Total Price', compute='_total_amount_before_discount')

    @api.depends('invoice_line_ids')
    def _total_amount_before_discount(self):
        total_price = 0
        for record in self:
            for rec in record.invoice_line_ids:
                total_price += (rec.price_unit * rec.quantity)
            record.total_amount_before_discount = total_price
    
    def _method_py_payments(self):
        for record in self:
            json_values = record._get_reconciled_info_JSON_values()
            pay_text = ''
            if json_values:
                for val in json_values:

                    account_payment = self.env['account.payment'].browse(val['account_payment_id'])
                    
                    employee_casher_id = account_payment.casher_employee_id.name
                    journal_type = account_payment.journal_id.type
                    amount = str(val['amount'])
                    
                    pay_text += str(employee_casher_id + '_+_' + journal_type +  '_+_'  + amount + '*_*')
                
                record.py_payments = pay_text
            else:
                record.py_payments = pay_text

    @api.depends('invoice_line_ids')
    def _compute_py_discount_total(self):
        total_discount = 0
        for record in self:
            for rec in record.invoice_line_ids:
                total_discount += (rec.price_unit * rec.quantity) - rec.price_subtotal
            record.py_total_discount = float(total_discount)

    def change_size_page_layout(self, items):
        paper_format = self.env.ref('ro_receipt_layout.paperformat_portrait')
        paper_format.with_user(SUPERUSER_ID).page_height = 120 + (len(items) * 20)