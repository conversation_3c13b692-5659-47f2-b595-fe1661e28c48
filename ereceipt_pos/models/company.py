# -*- coding: utf-8 -*-

from odoo import api, fields, models, _


class ResCompany(models.Model):
    _inherit = 'res.company'

    eta_id = fields.Char(store=True, related='partner_id.eta_id',
                         readonly=False)

    eta_type = fields.Selection(
        store=True, related='partner_id.eta_type', readonly=False
    )

    eta_name = fields.Char(store=True, related='partner_id.eta_name',
                           readonly=False)

    # For notifications

    #date_from_notifications = fields.Datetime('date_from_notifications', default=fields.Datetime.now)

    eta_portal_url = fields.Char(
        string='Portal Url', default="https://invoicing.eta.gov.eg")
    eta_api_base_url = fields.Char(
        string='Api Base Url', default="https://api.invoicing.eta.gov.eg")
    eta_id_srv_base_url = fields.Char(
        string='Id Srv Base Url', default="https://id.eta.gov.eg")
    eta_client_id = fields.Char(string='Client ID')
    eta_client_secret = fields.Char(string='Client Secret')
    eta_generated_access_token = fields.Char(
        string='Generated Access Token', readonly=True)
    eta_receipt_type_person_minimum = fields.Float(
        string='Type Receipt Person Minimum', default=150000)
    
    eta_version = fields.Char(string='Version', default='1.0')
    eta_receipt_version = fields.Char(string='Receipt Version', default='1.2')

    # For Setting Expire Token
    eta_token_timeout = fields.Datetime(string='Token Timeout')


class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    eta_portal_url = fields.Char(
        related='company_id.eta_portal_url', readonly=False)
    eta_api_base_url = fields.Char(
        related='company_id.eta_api_base_url', readonly=False)
    eta_id_srv_base_url = fields.Char(
        related='company_id.eta_id_srv_base_url', readonly=False)
    eta_client_id = fields.Char(
        related='company_id.eta_client_id', readonly=False)
    eta_client_secret = fields.Char(
        related='company_id.eta_client_secret', readonly=False)
    eta_receipt_type_person_minimum = fields.Float(
        related='company_id.eta_receipt_type_person_minimum', readonly=False)
