# -*- coding: utf-8 -*-

from odoo import api, fields, models, _


class RECSerial(models.Model):
    _name = 'receipt.serial'
    _description = 'Receipt Serial'
    _check_company_auto = True

    # V1.2
    name = fields.Char(string='Name', required=True)

    eta_receipt_serial = fields.Char(string='Serial', required=True)
    eta_receipt_os_version = fields.Char(string='OS Version')
    eta_receipt_model_framework = fields.Char(string='Model Framework')
    eta_receipt_pre_shared_key = fields.Char(string='PRE Shared Key')

    
    eta_receipt_api_base_url = fields.Char(
        string='Api Base Url', default="https://api.invoicing.eta.gov.eg")
    eta_receipt_id_srv_base_url = fields.Char(
        string='Id Srv Base Url', default="https://id.eta.gov.eg")
    eta_receipt_client_id = fields.Char(string='Client ID')
    eta_receipt_client_secret = fields.Char(string='Client Secret')
    eta_receipt_version = fields.Char(string='Version', default='1.2')
    eta_receipt_type_person_minimum = fields.Float(
        string='Type Person Minimum', default=150000)
    eta_receipt_generated_access_token = fields.Char(
        string='Generated Access Token', readonly=True)

    # For Setting Expire Token
    eta_receipt_token_timeout = fields.Datetime(string='Token Timeout')

    active = fields.Boolean(string='Active', default=True)

    company_id = fields.Many2one(
        'res.company', 'Company', required=True, index=True, default=lambda self: self.env.company)

    eta_receipt_company_branch_id = fields.Many2one(
        string='Company Branch',
        comodel_name='eta.company.branch',
        ondelete='restrict',
        required=True,
        help='Mandatory when issuer is of type B, \
            otherwise optional. The code of the branch as registered with tax authority for the \
            company submitting the document.[1234]'
    )

    eta_receipt_last_uuid = fields.Char(
        string='Last Receipt UUID',
        readonly=True,
        copy=False
    )
