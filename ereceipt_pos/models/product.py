# -*- coding: utf-8 -*-

from odoo import api, fields, models, _


class ProductTemplate(models.Model):
    _inherit = 'product.template'
    
    eta_code_type = fields.Selection(
        string='Code Type', compute='_compute_eta_code_type', inverse='_set_eta_code_type',
        selection=[('gs1', 'GS1'), ('egs', 'EGS')], store=True,
        help='Coding schema used to encode the code type. Must be GS1 or EGS for this version.[GS1]'
    )

    eta_item_code = fields.Char(
        'Item Code', compute='_compute_eta_item_code',
        inverse='_set_eta_item_code', store=True,
        help='Code of the goods or services item being sold. GS1 codes targeted for managing goods, EGS codes targeted for managing goods – goods or services.[10003752]'
        )
    
    @api.depends('product_variant_ids', 'product_variant_ids.eta_code_type')
    def _compute_eta_code_type(self):
        unique_variants = self.filtered(lambda template: len(template.product_variant_ids) == 1)
        for template in unique_variants:
            template.eta_code_type = template.product_variant_ids.eta_code_type
        for template in (self - unique_variants):
            template.eta_code_type = False

    def _set_eta_code_type(self):
        for template in self:
            if len(template.product_variant_ids) == 1:
                template.product_variant_ids.eta_code_type = template.eta_code_type
                
    @api.depends('product_variant_ids', 'product_variant_ids.eta_item_code')
    def _compute_eta_item_code(self):
        unique_variants = self.filtered(lambda template: len(template.product_variant_ids) == 1)
        for template in unique_variants:
            template.eta_item_code = template.product_variant_ids.eta_item_code
        for template in (self - unique_variants):
            template.eta_item_code = False

    def _set_eta_item_code(self):
        for template in self:
            if len(template.product_variant_ids) == 1:
                template.product_variant_ids.eta_item_code = template.eta_item_code

    @api.model_create_multi
    def create(self, vals_list):
        ''' Store the initial standard price in order to be able to retrieve the cost of a product template for a given date'''
        templates = super(ProductTemplate, self).create(vals_list)

        # This is needed to set given values to first variant after creation
        for template, vals in zip(templates, vals_list):
            related_vals = {}
            if vals.get('eta_code_type'):
                related_vals['eta_code_type'] = vals['eta_code_type']
            if vals.get('eta_item_code'):
                related_vals['eta_item_code'] = vals['eta_item_code']
            if related_vals:
                template.write(related_vals)

        return templates

class ProductProduct(models.Model):
    _inherit = 'product.product'

    eta_code_type = fields.Selection(
        string='Code Type',
        selection=[('gs1', 'GS1'), ('egs', 'EGS')],
        help='Coding schema used to encode the code type. Must be GS1 or EGS for this version.[GS1]'
    )

    eta_item_code = fields.Char(
        string='Item Code',
        help='Code of the goods or services item being sold. GS1 codes targeted for managing goods, EGS codes targeted for managing goods – goods or services.[10003752]'
    )
