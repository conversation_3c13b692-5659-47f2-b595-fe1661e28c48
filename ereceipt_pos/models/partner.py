# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError

ADDRESS_FIELDS = ('eta_building_number', 'street', 'street2',
                  'zip', 'city', 'state_id', 'country_id')


class ResPartner(models.Model):
    _inherit = 'res.partner'

    eta_id = fields.Char(string='Registration Number', copy=False,
                         help='Registration number. For business in Egypt must be registration number.[***********]',
                         compute='_compute_eta_fields', readonly=False, store=True)

    eta_passport_national_id = fields.Char(
        string='National/Passport Id', copy=False)

    eta_type = fields.Selection(
        string='Issuer Type',
        selection=[('B', 'Business In Egypt'), ('P', 'Natural Person'),
                   ('F', 'Foreigner')],
        compute='_compute_eta_type',
        readonly=False, store=True,
        help='Type of the issuer - supported values - B for business in Egypt, \
                    P for natural person, F for foreigner. \
                        Note that P and F are reserved values for future use.[B]'
    )

    @api.depends('company_type', 'name')
    def _compute_eta_type(self):
        for record in self:
            record.eta_type = 'P' if record.company_type == 'person' else 'B'

    eta_name = fields.Char(string='ETA Name',
                           help='Registration name of the company.[My company]', compute='_compute_eta_fields', readonly=False, store=True)

    # Address
    # https://sdk.sit.invoicing.eta.gov.eg/codes/branch/
    eta_branchId = fields.Char(string='Branch Id', help='Mandatory when issuer is of type B, \
        otherwise optional. The code of the branch as registered with tax authority for the \
            company submitting the document.[1234]')
    eta_country_id = fields.Many2one('res.country', string='Issuer Country', help='Country \
        represented by ISO-3166-2 2 symbol code of the countries. \
            Must be EG for internal business issuers.[EG]',
                                     compute='_compute_eta_fields', readonly=False, store=True)
    eta_governate = fields.Char(string='ETA Governate',
                                help='Governorate information as textual value.[Giza Governorate]',
                                compute='_compute_eta_fields', readonly=False, store=True)

    eta_region_city = fields.Char(
        string='ETA Region City', help='Region and city information as textual value.[Dokki]',
        compute='_compute_eta_fields', readonly=False, store=True)
    eta_street = fields.Char(
        string='ETA Street', help='street information.[17 Nabil Al Wakad]',
        compute='_compute_eta_fields', readonly=False, store=True)
    eta_building_number = fields.Char(
        string='Building Number', help='building information.[17]')

    # optional
    eta_postalCode = fields.Char(
        string='Postal Code', help="Optional: Postal code",
        compute='_compute_eta_fields', readonly=False, store=True)
    eta_floor = fields.Char(string='Floor', help="Optional: the floor number")
    eta_room = fields.Char(
        string='Room', help="Optional: the room/flat number in the floor")
    eta_landmark = fields.Char(
        string='ETA Landmark', help="Optional: nearest landmark to the address")
    eta_additionalInformation = fields.Text(
        string='ETA Additional Information', help="Optional: any additional information to the address")

    '''_sql_constraints = [
        ('eta_id_unique',
         'unique(eta_id)',
         'Registration Number has to be unique!')
    ]

    @api.constrains('eta_id', 'parent_id')
    def _validate_eta_id_vat(self):
        for record in self:
            if record.eta_id and not record.parent_id:
                partner = self.env['res.partner'].search([('eta_id', '=', record.eta_id)])

                if len(partner) > 1:
                    raise ValidationError(
                        _("Registration Number has to be unique!"))'''

    @api.depends('vat', 'eta_passport_national_id', 'name', 'country_id', 'state_id', 'city', 'street', 'zip')
    def _compute_eta_fields(self):
        for record in self:
            record.eta_id = record.vat
            record.eta_name = record.name
            record.eta_country_id = record.country_id
            record.eta_governate = record.state_id.name
            record.eta_region_city = record.city
            record.eta_street = record.street
            record.eta_postalCode = record.zip

    @api.model
    def _get_default_address_format(self):
        super()._get_default_address_format()
        return "%(eta_building_number)s\n%(street)s\n%(street2)s\n%(city)s %(state_code)s %(zip)s\n%(country_name)s"

    @api.model
    def _address_fields(self):
        """Returns the list of address fields that are synced from the parent."""
        super()._address_fields()
        return list(ADDRESS_FIELDS)
