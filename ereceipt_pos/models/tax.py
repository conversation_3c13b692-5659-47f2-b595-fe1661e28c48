# -*- coding: utf-8 -*-

from odoo import api, fields, models, _


class AccountTax(models.Model):
    _inherit = 'account.tax'

    eta_code = fields.Char(string='Code')
    eta_english_description = fields.Char(string='English Description')
    eta_arabic_description = fields.Char(string='Arabic Description')

    eta_sub_code = fields.Char(string='Subtype Code')
    eta_sub_english_description = fields.Char(string='Subtype English Description')
    eta_sub_arabic_description = fields.Char(string='Subtype Arabic Description')