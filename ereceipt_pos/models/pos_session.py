# -*- coding: utf-8 -*-

from odoo import api, fields, models, _


class PosSession(models.Model):
    _inherit = 'pos.session'

    eta_receipt_company_branch_id = fields.Many2one(
        string='Company Branch',
        comodel_name='eta.company.branch',
        ondelete='restrict',
        readonly=True,
        help='Mandatory when issuer is of type B, \
            otherwise optional. The code of the branch as registered with tax authority for the \
            company submitting the document.[1234]'
    )

    # As https://sdk.sit.invoicing.eta.gov.eg/codes/activity-types/
    eta_receipt_taxpayer_activity_code_id = fields.Many2one(
        string='Taxpayer Activity Code',
        comodel_name='eta.activity.types',
        domain="[('eta_receipt_company_branch_id', '=', eta_receipt_company_branch_id)]",
        ondelete='restrict',
        readonly=True,
        help='Tax activity code of the business issuing the document \
            representing the activity that caused it to be issued.[9478]'
    )

    eta_receipt_serial_id = fields.Many2one(
        string='Receipt Serial',

        comodel_name='receipt.serial',
        ondelete='restrict'
    )

    def close_session_from_ui(self, bank_payment_method_diff_pairs=None, eta_receipt_last_uuid=None):
        """Calling this method will try to close the session.

        param bank_payment_method_diff_pairs: list[(int, float)]
            Pairs of payment_method_id and diff_amount which will be used to post
            loss/profit when closing the session.

        If successful, it returns {'successful': True}
        Otherwise, it returns {'successful': False, 'message': str, 'redirect': bool}.
        'redirect' is a boolean used to know whether we redirect the user to the back end or not.
        When necessary, error (i.e. UserError, AccessError) is raised which should redirect the user to the back end.
        """
        bank_payment_method_diffs = dict(bank_payment_method_diff_pairs or [])
        self.ensure_one()
        # Even if this is called in `post_closing_cash_details`, we need to call this here too for case
        # where cash_control = False
        check_closing_session = self._cannot_close_session(
            bank_payment_method_diffs)
        if check_closing_session:
            return check_closing_session

        validate_result = self.action_pos_session_closing_control(
            bank_payment_method_diffs=bank_payment_method_diffs)

        # If an error is raised, the user will still be redirected to the back end to manually close the session.
        # If the return result is a dict, this means that normally we have a redirection or a wizard => we redirect the user
        if isinstance(validate_result, dict):
            # imbalance accounting entry
            return {
                'successful': False,
                'message': validate_result.get('name'),
                'redirect': True
            }

        self.message_post(body='Point of Sale Session ended')

        self.write({
            'eta_receipt_company_branch_id': self.config_id.eta_receipt_company_branch_id.id,
            'eta_receipt_serial_id': self.config_id.eta_receipt_serial_id.id,
            'eta_receipt_taxpayer_activity_code_id': self.config_id.eta_receipt_taxpayer_activity_code_id.id
        })

        if eta_receipt_last_uuid:
            self.config_id.eta_receipt_serial_id.write(
                {'eta_receipt_last_uuid': eta_receipt_last_uuid})

        return {'successful': True}

    def post_set_eta_receipt_last_uuid(self, eta_receipt_last_uuid=None):
        if eta_receipt_last_uuid:
            self.config_id.eta_receipt_serial_id.write(
                {'eta_receipt_last_uuid': eta_receipt_last_uuid})

    # Load Fields

    def _loader_params_product_product(self):
        result = super()._loader_params_product_product()
        result['search_params']['fields'].extend(
            ['detailed_type', 'default_code', 'eta_code_type', 'eta_item_code'])
        return result

    def _loader_params_res_company(self):
        result = super()._loader_params_res_company()
        result['search_params']['fields'].extend(
            ['eta_id', 'eta_name', 'eta_receipt_type_person_minimum', 'eta_receipt_version', 'eta_portal_url'])
        return result

    def _loader_params_res_partner(self):
        result = super()._loader_params_res_partner()
        result['search_params']['fields'].extend(
            ['eta_id', 'eta_type', 'eta_name'])
        return result

    def _loader_params_account_tax(self):
        result = super()._loader_params_account_tax()
        result['search_params']['fields'].extend(
            ['price_include', 'eta_code', 'eta_sub_code'])
        return result

    # Load Models
    @api.model
    def _pos_ui_models_to_load(self):
        models_to_load = super()._pos_ui_models_to_load()
        models_to_load += ['eta.activity.types',
                           'receipt.serial', 'eta.company.branch']
        return models_to_load

    def _loader_params_eta_activity_types(self):
        return {'search_params': {
            'domain': [('id', '=', self.config_id.eta_receipt_taxpayer_activity_code_id.id)],
            'fields': ['id', 'code']
        }
        }

    def _get_pos_ui_eta_activity_types(self, params):
        return self.env['eta.activity.types'].search_read(**params['search_params'])

    def _loader_params_receipt_serial(self):
        return {'search_params': {
            'domain': [('id', '=', self.config_id.eta_receipt_serial_id.id)],
            'fields': ['id', 'eta_receipt_serial', 'eta_receipt_last_uuid']
        }
        }

    def _get_pos_ui_receipt_serial(self, params):
        return self.env['receipt.serial'].search_read(**params['search_params'])

    def _loader_params_eta_company_branch(self):
        return {'search_params': {
            'domain': [('id', '=', self.config_id.eta_receipt_company_branch_id.id)],
            'fields': ['id', 'eta_branchId', 'eta_governate', 'eta_region_city', 'eta_street', 'eta_building_number']
        }
        }

    def _get_pos_ui_eta_company_branch(self, params):
        return self.env['eta.company.branch'].search_read(**params['search_params'])

    def _get_pos_ui_pos_config(self, params):
        config = super()._get_pos_ui_pos_config(params)

        params_eta_activity_types = self._loader_params_eta_activity_types()
        params_params_eta_company_branch = self._loader_params_eta_company_branch()
        params_params_receipt_serial = self._loader_params_receipt_serial()

        if config['eta_receipt_company_branch_id']:
            params_params_eta_company_branch['search_params']['domain'] = [
                ('id', '=', config['eta_receipt_company_branch_id'][0])]
            config['eta_receipt_company_branch_id'] = self.env['eta.company.branch'].search_read(
                **params_params_eta_company_branch['search_params'])[0]
        else:
            config['eta_receipt_company_branch_id'] = None

        if config['eta_receipt_serial_id']:
            params_params_receipt_serial['search_params']['domain'] = [
                ('id', '=', config['eta_receipt_serial_id'][0])]
            config['eta_receipt_serial_id'] = self.env['receipt.serial'].search_read(
                **params_params_receipt_serial['search_params'])[0]
        else:
            config['eta_receipt_serial_id'] = None

        if config['eta_receipt_taxpayer_activity_code_id']:
            params_eta_activity_types['search_params']['domain'] = [
                ('id', '=', config['eta_receipt_taxpayer_activity_code_id'][0])]
            config['eta_receipt_taxpayer_activity_code_id'] = self.env['eta.activity.types'].search_read(
                **params_eta_activity_types['search_params'])[0]
        else:
            config['eta_receipt_taxpayer_activity_code_id'] = None

        return config
