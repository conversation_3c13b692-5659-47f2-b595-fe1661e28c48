# -*- coding: utf-8 -*-

from datetime import datetime
from odoo import api, fields, models, _
from odoo.exceptions import UserError, ValidationError


class POSConfig(models.Model):
    _inherit = 'pos.config'

    eta_receipt_serial_id = fields.Many2one(
        string='Receipt Serial',
        comodel_name='receipt.serial',
        ondelete='restrict'
    )

    eta_receipt_company_branch_id = fields.Many2one(
        comodel_name='eta.company.branch',
        related='eta_receipt_serial_id.eta_receipt_company_branch_id',
        store=True
    )

    # As https://sdk.sit.invoicing.eta.gov.eg/codes/activity-types/
    eta_receipt_taxpayer_activity_code_id = fields.Many2one(
        string='Taxpayer Activity Code',
        comodel_name='eta.activity.types',
        ondelete='restrict',
        help='Tax activity code of the business issuing the document \
            representing the activity that caused it to be issued.[9478]'
    )

class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    eta_receipt_serial_id = fields.Many2one(
        comodel_name='receipt.serial',
        related="pos_config_id.eta_receipt_serial_id",
        readonly=False
    )

    eta_receipt_company_branch_id = fields.Many2one(
        comodel_name='eta.company.branch',
        related='eta_receipt_serial_id.eta_receipt_company_branch_id'
    )

    # As https://sdk.sit.invoicing.eta.gov.eg/codes/activity-types/
    eta_receipt_taxpayer_activity_code_id = fields.Many2one(
        comodel_name='eta.activity.types',
        related="pos_config_id.eta_receipt_taxpayer_activity_code_id",
        readonly=False
    )