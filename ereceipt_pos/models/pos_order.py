# -*- coding: utf-8 -*-

from datetime import datetime
from odoo import api, fields, models, _
from odoo.exceptions import UserError, ValidationError


class PosOrder(models.Model):
    _inherit = 'pos.order'

    eta_receipt_uuid = fields.Char(
        string='Receipt UUID',
        readonly=True,
        copy=False
    )

    eta_receipt_submission_uuid = fields.Char(
        string='Submission UUID',
        readonly=True,
        copy=False
    )
    
    eta_receipt_previous_uuid = fields.Char(
        string='Previous UUID',
        readonly=True,
        copy=False
    )

    #For return
    eta_receipt_reference_uuid = fields.Char(
        string='Reference UUID',
        copy=False
    )

    #For resend receipt
    eta_receipt_reference_old_uuid = fields.Char(
        string='Reference Old UUID',
        readonly=True,
        copy=False,
        tracking=True
    )
    
    eta_receipt_json_text = fields.Text(
        string='Json Text',
        readonly=True,
        copy=False
    )

    eta_receipt_state = fields.Selection(selection=[
        ('draft', 'Draft'),
        ('accepted', 'Accepted'),
        ('submitted', 'Submitted'),
        ('valid', 'Valid'),
        ('invalid', 'Invalid'),
        ('rejected', 'Rejected'),
        ('tocancel', 'tocancel'),
        ('canceled', 'Canceled')
    ], string='ETA Receipt Status', readonly=True, copy=False, tracking=True, default='draft')

    eta_receipt_reject_reason = fields.Text(
        string='Reject Reason',
        readonly=True,
        copy=False,
        tracking=True
    )

    eta_receipt_invalid_reason = fields.Text(
        string='Invalid Reason',
        readonly=True,
        copy=False,
        tracking=True
    )

    @api.depends('session_id','session_id.eta_receipt_serial_id','config_id')
    def _compute_eta_receipt_field(self):
        for record in self:
            order_session_id = record.session_id if record.session_id.eta_receipt_serial_id else record.config_id
            record.eta_receipt_company_branch_id = order_session_id.eta_receipt_company_branch_id
            record.eta_receipt_taxpayer_activity_code_id = order_session_id.eta_receipt_taxpayer_activity_code_id
            record.eta_receipt_serial_id = order_session_id.eta_receipt_serial_id
            
    eta_receipt_company_branch_id = fields.Many2one(
        string='Company Branch',
        comodel_name='eta.company.branch',
        compute='_compute_eta_receipt_field'
    )

    # As https://sdk.sit.invoicing.eta.gov.eg/codes/activity-types/
    eta_receipt_taxpayer_activity_code_id = fields.Many2one(
        string='Taxpayer Activity Code',
        comodel_name='eta.activity.types',
        compute='_compute_eta_receipt_field'
    )

    eta_receipt_serial_id = fields.Many2one(
        string='Receipt Serial',
        comodel_name='receipt.serial',
        compute='_compute_eta_receipt_field'
    )
    

    @api.model
    def _order_fields(self, ui_order):
        result = super()._order_fields(ui_order)

        result['eta_receipt_uuid'] = ui_order.get('eta_receipt_uuid', False)
        result['eta_receipt_previous_uuid'] = ui_order.get('eta_receipt_previous_uuid', False)
        result['eta_receipt_reference_uuid'] = ui_order.get('eta_receipt_reference_uuid', False)
        result['eta_receipt_json_text'] = ui_order.get('eta_receipt_json_text', False)

        return result

    def _export_for_ui(self, order):
        result = super()._export_for_ui(order)
        result.update({
            'eta_receipt_uuid': order.eta_receipt_uuid,
            'eta_receipt_previous_uuid': order.eta_receipt_previous_uuid,
            'eta_receipt_reference_uuid': order.eta_receipt_reference_uuid,
            'eta_receipt_json_text': order.eta_receipt_json_text,
        })
        return result

    def action_pos_regenerate_eta(self):
        return {
            'name': _('ETA Receipt Regenerate'),
            'res_model': 'pos.order.regenerate',
            'view_mode': 'form',
            'type': 'ir.actions.act_window',
            'context': {
                'active_model': 'pos.order',
                'active_ids': self.ids,
            },
            'target': 'new',
        }
        
    def action_pos_send_eta(self):
        self.env['eta.receipt.manage']._send_eta_receipt(self[:50])

    def action_pos_get_state(self):
        self.env['eta.receipt.manage'].get_eta_receipt_document_state(self)

    #Add Default Customer if empty
    @api.model
    def _order_fields(self, ui_order):
        result = super()._order_fields(ui_order)
        #TODO: make id in settings
        result['partner_id'] = ui_order['partner_id'] or 721103

        return result

class PosOrderLine(models.Model):
    _inherit = 'pos.order.line'

    ro_new_price = fields.Float(digits=0)

    def _export_for_ui(self, order_line):
        result = super()._export_for_ui(order_line)
        result.update({
            'ro_new_price': order_line.ro_new_price
        })
        return result