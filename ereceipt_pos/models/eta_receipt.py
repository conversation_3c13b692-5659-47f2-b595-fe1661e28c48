# -*- coding: utf-8 -*-

import json
import requests
import urllib3
import ssl
import re
import base64
from datetime import datetime, timedelta, date
import collections.abc
from hashlib import sha256

from odoo import fields, models, _, SUPERUSER_ID
from odoo.exceptions import UserError, ValidationError

# As https://sdk.sit.invoicing.eta.gov.eg/standard-error-response/
status_code_gn = {
    '200': 'Success',
    '400': 'NotReady or BadRequest or BadArgument',
    '401': 'Unauthorized',
    '403': 'Forbidden',
    '404': 'NotFound',
    '429': 'TooManyRequests',
    '500': 'InternalServerError',
    '501': 'NotImplemented',
    '503': 'ServiceUnavailable'
}

status_code_gn_pdf = {
    '200': 'Success',
    '400': 'NotReady',
    '404': 'NotFound',
}

status_code_gn_cancel = {
    '200': 'Success',
    '400': 'OperationPeriodOver or IncorrectState or ActiveReferencingDocuments',
    '403': 'Forbidden',
}


def round_to_decimal(num, rounding):

    if int(num) != num and len(str(num).split('.')[1]) > rounding:
        num = str(num)[:str(num).index('.')+rounding+2]
        if num[-1] >= '5':
            a = num[:-2-(not rounding)]       # integer part
            b = int(num[-2-(not rounding)])+1  # decimal part
            res = float(a)+b**(-rounding +
                               1) if a and b == 10 else float(a+str(b))
            return round(abs(res), rounding)

        return abs(float(num[:-1]))
    else:
        return abs(num)


class ROCustomHttpAdapter(requests.adapters.HTTPAdapter):
    # "Transport adapter" that allows us to use custom ssl_context.

    def __init__(self, ssl_context=None, **kwargs):
        self.ssl_context = ssl_context
        super().__init__(**kwargs)

    def init_poolmanager(self, connections, maxsize, block=False):
        self.poolmanager = urllib3.poolmanager.PoolManager(
            num_pools=connections, maxsize=maxsize,
            block=block, ssl_context=self.ssl_context)


def ro_get_legacy_session():
    ctx = ssl.create_default_context(ssl.Purpose.SERVER_AUTH)
    ctx.options |= 0x4  # OP_LEGACY_SERVER_CONNECT
    session = requests.session()
    session.mount('https://', ROCustomHttpAdapter(ctx))
    return session

class EtaReceiptManage(models.Model):
    _name = 'eta.receipt.manage'
    _description = 'ETA Receipt Manage'

    def _create_eta_token(self, company):

        token_url = '%s/connect/token' % company.eta_id_srv_base_url

        eta_client_id = company.eta_client_id
        eta_client_secret = company.eta_client_secret

        payload = 'grant_type=client_credentials&client_id=%s&client_secret=%s&scope=InvoicingAPI' % (
            eta_client_id, eta_client_secret)

        headers = {
            "content-type": "application/x-www-form-urlencoded"
        }

        token_response = ro_get_legacy_session().post(
            token_url, data=payload, headers=headers)

        token = token_response.json().get('access_token')
        expires_in = token_response.json().get('expires_in')

        if not token:
            error = token_response.json().get('error')
            error_description = token_response.json().get('error_description')
            error_uri = token_response.json().get('error_uri')

            raise UserError(_('Error %s description %s uri %s' %
                              (error, error_description, error_uri)))

        company.with_user(SUPERUSER_ID).write({
            'eta_generated_access_token': token,
            'eta_token_timeout': datetime.utcnow() + timedelta(seconds=expires_in)
        })

        return token

    def _send_eta_receipt(self, orders):

        current_company = orders.mapped('company_id')

        if len(current_company) > 1:
            raise UserError(_('Please choose only one company.'))

        url = '%s/api/v1/receiptsubmissions' % (
            current_company.eta_api_base_url)

        if not current_company.eta_token_timeout or datetime.utcnow() > current_company.eta_token_timeout:
            token = self._create_eta_token(current_company)
        else:
            token = current_company.eta_generated_access_token

        total_orders = []
        orders = orders[::-1]

        for order in orders:
            if order.state in ['draft', 'cancel']:
                continue

            if order.eta_receipt_state in ['accepted', 'submitted', 'valid']:
                continue

            if not order.eta_receipt_uuid:
                continue

            eta_receipt_json_text = order.eta_receipt_json_text
            eta_receipt_json_text = eta_receipt_json_text.replace("'", '"')
            order_receipt = json.loads(eta_receipt_json_text)

            total_orders.append(order_receipt)

        if not total_orders:
            return False

        payload = {
            "receipts":
            total_orders
        }

        payload = json.dumps(payload, ensure_ascii=False)

        headers = {
            'Accept-Language': 'en',
            'Authorization': 'Bearer %s' % (token),
            'Content-Type': 'application/json'
        }

        response = ro_get_legacy_session().post(
            url, headers=headers, data=payload.encode('utf-8'))

        if response.status_code in [202, 200]:
            accepted = response.json().get('acceptedDocuments')
            rejected = response.json().get('rejectedDocuments')

            if accepted:
                order = self.env['pos.order']

                for order_item in accepted:
                    order += orders.filtered(
                        lambda ord: ord.pos_reference == order_item['receiptNumber'])

                order.write({'eta_receipt_state': 'accepted',
                            'eta_receipt_reject_reason': False,
                             'eta_receipt_invalid_reason': False,
                             'eta_receipt_json_text': False,
                             'eta_receipt_submission_uuid': response.json().get('submissionId')})

            elif rejected:

                for order_item in rejected:
                    order = orders.filtered(
                        lambda ord: ord.pos_reference == order_item['receiptNumber'])
                    errors = order_item.get('error')['details']

                    err = ''

                    for error in errors:
                        err += '%s\n' % (error['message'])

                    order.write({'eta_receipt_state': 'rejected',
                                'eta_receipt_reject_reason': err})
            # On 202 response if validation not complete
            else:
                raise UserError(response)

        elif response.status_code == 401:
            raise ValidationError(_('Not Authorized'))
        else:
            error_description = status_code_gn.get(
                str(response.status_code)) or response.text
            raise UserError(_('Error %s %s' %
                              (response.status_code, error_description)))

        return True

    def get_eta_receipt_document_state(self, orders):
        current_company = orders.mapped('company_id')

        if len(current_company) > 1:
            raise UserError(_('Please choose only one company.'))

        if not current_company.eta_token_timeout or datetime.utcnow() > current_company.eta_token_timeout:
            token = self._create_eta_token(current_company)
        else:
            token = current_company.eta_generated_access_token

        orders = orders.filtered(lambda ord: ord.eta_receipt_state in [
                                 'accepted', 'submitted', 'valid'])

        receipt_submission_uuids = orders.mapped('eta_receipt_submission_uuid')

        for receipt_submission_uuid in receipt_submission_uuids:

            url = '%s/api/v1/receiptsubmissions/%s/details?PageNo=1&PageSize=100' % (
                current_company.eta_api_base_url, receipt_submission_uuid)

            headers = {
                'Accept-Language': 'en',
                'Authorization': 'Bearer %s' % (token),
                'Content-Type': 'application/json'
            }

            payload = {}

            response = ro_get_legacy_session().get(url, headers=headers, data=payload)

            if response.status_code in [202, 200]:

                status = response.json().get('status').lower()

                if status == 'valid':
                    orders.filtered(lambda ord: ord.eta_receipt_submission_uuid == response.json().get(
                        'submissionUuid')).write({'eta_receipt_state': 'valid'})

                elif status == 'invalid':
                    orders.filtered(lambda ord: ord.eta_receipt_submission_uuid == response.json().get('submissionUuid')).write(
                        {'eta_receipt_state': 'invalid'})

                elif status == 'submitted':
                    orders.filtered(lambda ord: ord.eta_receipt_submission_uuid == response.json().get('submissionUuid')).write({
                        'eta_receipt_state': 'submitted'
                    })

                else:
                    raise UserError(_('Check Later'))

            elif response.status_code == 401:
                raise ValidationError(_('Not Authorized'))

            elif response.status_code not in [202, 200]:
                error_description = status_code_gn.get(
                    str(response.status_code)) or response

                error = 'Error %s %s' % (
                    response.status_code, error_description)

                if response.status_code == 404:
                    error += ' or check later'
                raise UserError(_(error))

        return True

    def serializeUUID(self, documentStructure):

        if isinstance(documentStructure, (str, int, float, bool, date, datetime)):

            return '"' + str(documentStructure) + '"'

        serializedString = ""

        for key, value in documentStructure.items():

            if not isinstance(value, (list, tuple, set, dict)):
                serializedString += '"' + str(key).upper() + '"'
                serializedString += self.serializeUUID(value)

            if isinstance(value, (list, tuple, set)):

                serializedString += '"' + str(key).upper() + '"'

                for val in value:

                    serializedString += '"' + key.upper() + '"'

                    serializedString += self.serializeUUID(val)

            if isinstance(value, dict):

                serializedString += '"' + str(key).upper() + '"'

                for minkey, minval in value.items():

                    serializedString += '"' + minkey.upper() + '"'

                    serializedString += self.serializeUUID(minval)

        return serializedString

    def _send_regenerate_receipt(self, pos_order_ids, ro_regenerate_time):

        orders = self.env['pos.order'].search([('id', 'in', pos_order_ids)])

        orders = orders[::-1]

        rounding = self.env['decimal.precision'].precision_get(
            'Product Price')
        rounding_uom = self.env['decimal.precision'].precision_get(
            'Product Unit of Measure')

        rounding = rounding if rounding > rounding_uom else rounding_uom
        rounding = rounding if rounding <= 5 else 5

        for order in orders:

            if order.state not in ['paid', 'done', 'invoiced'] or order.eta_receipt_state not in ['draft', 'rejected', 'invalid']:
                continue

            order_lines_dict = []
            order_line_ids_all = order.lines

            order_line_ids = order_line_ids_all.filtered(
                lambda line: line.qty != 0 and line.price_unit >= 0)

            order_discount_lines = (order_line_ids_all - order_line_ids).filtered(
                lambda line: line.qty != 0 and line.price_unit < 0)

            if not order_line_ids:
                continue

            extraOrderDiscount = totalOrderDiscount = totalOrderSales = totalOrderItemDiscount = 0
            totalOrderAmount = netOrderAmount = 0

            orderTaxs = []
            errors = []

            extraOrderDiscount = sum(order_discount_lines.mapped('ro_new_price'))

            if order_line_ids[0].qty >= 0:
                is_line_positive = True
            else:
                is_line_positive = False

            for line in order_line_ids:

                if is_line_positive and line.qty < 0 or not is_line_positive and line.qty > 0:

                    raise ValidationError(
                        _("Receipt must be for delivery or return."))

                taxs = []

                TotalTaxableFees = totalLineTaxs = 0
                line_tax_ids = line.tax_ids

                # TODO:for forign currency need to work same
                ro_new_price = round_to_decimal(line.ro_new_price, rounding)

                amountEGP = round_to_decimal(ro_new_price, rounding)
                line_quantity = round_to_decimal(line.qty, rounding)

                if order.currency_id.name != 'EGP':

                    order_currency_rate = self.env['res.currency']._get_conversion_rate(
                        order.company_id.currency_id, order.currency_id, order.company_id, order.date or fields.Date.context_today(self))

                    if not order_currency_rate:
                        order_currency_rate = 1
                        errors.append(
                            'Line product [%s] Currency Rate' % line.product_id.name)
                    else:
                        order_currency_rate = round_to_decimal(
                            1/order_currency_rate, rounding)
                        amountEGP = round_to_decimal(
                            amountEGP * order_currency_rate, rounding)

                discountAmount = (line_quantity * amountEGP) * \
                    (line.discount / 100)

                netTotal = (line_quantity * amountEGP) - discountAmount

                valueDifference = totalLineDiscount = totalLineSales = 0.0

                tax_include_base_amount_ids = line_tax_ids.filtered(
                    lambda tax: tax.include_base_amount)
                tax_not_include_base_amount_ids = line_tax_ids.filtered(
                    lambda tax: not tax.include_base_amount)
                total_after_tax_include = netTotal

                for tax in tax_include_base_amount_ids:
                    # TODO: add other taxes
                    eta_tax_amount = 0

                    # T1/T2/T4 tax amount
                    if tax.eta_code in ['T1', 'T2', 'T4']:
                        eta_tax_amount = round_to_decimal(
                            (abs(tax.amount)/100)*total_after_tax_include, rounding)

                    if tax.amount == 8:
                        total_after_tax_include += eta_tax_amount

                    if tax.eta_code in ['T1', 'T2']:
                        totalLineTaxs += eta_tax_amount
                    elif tax.eta_code == 'T4':
                        totalLineTaxs -= eta_tax_amount

                    taxs.append({
                        "taxType": tax.eta_code if tax.eta_code else errors.append('Line product [%s] Tax Type' % line.product_id.name),
                        "amount": round_to_decimal(eta_tax_amount, rounding),
                        "subType": tax.eta_sub_code if tax.eta_sub_code else errors.append('Line product [%s] Tax Type' % line.product_id.name),
                        "rate": round_to_decimal(abs(tax.amount), rounding)
                    })

                    duplicateTax = False

                    for item in orderTaxs:
                        if item['taxType'] == tax.eta_code:
                            duplicateTax = item

                    if duplicateTax:

                        duplicateTax['amount'] += eta_tax_amount

                    else:
                        orderTaxs.append({
                            "taxType": tax.eta_code if tax.eta_code else errors.append('Line product [%s] Tax Type' % line.product_id.name),
                            "amount": eta_tax_amount,
                        })

                for tax in tax_not_include_base_amount_ids:
                    # TODO: add other taxes
                    eta_tax_amount = 0

                    # T1/T2/T4 tax amount
                    if tax.eta_code in ['T1', 'T2', 'T4']:
                        eta_tax_amount = round_to_decimal(
                            (abs(tax.amount)/100)*total_after_tax_include, rounding)

                    if tax.eta_code in ['T1', 'T2']:
                        totalLineTaxs += eta_tax_amount
                    elif tax.eta_code == 'T4':
                        totalLineTaxs -= eta_tax_amount

                    taxs.append({
                        "taxType": tax.eta_code if tax.eta_code else errors.append('Line product [%s] Tax Type' % line.product_id.name),
                        "amount": round_to_decimal(eta_tax_amount, rounding),
                        "subType": tax.eta_sub_code if tax.eta_sub_code else errors.append('Line product [%s] Tax Type' % line.product_id.name),
                        "rate": round_to_decimal(abs(tax.amount), rounding)
                    })

                    duplicateTax = False

                    for item in orderTaxs:
                        if item['taxType'] == tax.eta_code:
                            duplicateTax = item

                    if duplicateTax:

                        duplicateTax['amount'] += eta_tax_amount

                    else:
                        orderTaxs.append({
                            "taxType": tax.eta_code if tax.eta_code else errors.append('Line product [%s] Tax Type' % line.product_id.name),
                            "amount": eta_tax_amount,
                        })

                # TODO: item discount after tax
                itemsLineDiscount = 0
                # totalOrderItemDiscount += round_to_decimal(
                #    itemsLineDiscount, rounding)

                product_uom_eta_code = line.product_uom_id.eta_code if line.product_uom_id.eta_code else line.product_id.uom_id.eta_code
                if not product_uom_eta_code:
                    errors.append(
                        'Line product [%s] UOM Code' % line.product_id.name)

                if line.product_id.eta_code_type:
                    eta_code_type = line.product_id.eta_code_type.upper()
                else:
                    eta_code_type = False

                eta_item_code = line.product_id.eta_item_code

                if len(line.name) > 500:
                    errors.append(
                        'Line product [%s] Description must be less than 500 char.' % line.product_id.name)

                order_line_without_tax = {
                    'internalCode': str(line.product_id.default_code) if line.product_id.default_code else errors.append('Line product [%s] Internal Reference.' % line.product_id.name),
                    "description": line.full_product_name.replace('"', '|').replace("'", "|").replace('\xa0', ' ').replace('\n', ' ').replace('\\', ':').replace('/',':') if line.full_product_name else errors.append('Line product [%s] Description' % line.product_id.name),
                    "itemType": eta_code_type,
                    "unitType": product_uom_eta_code,
                    "unitPrice": ro_new_price,
                    "quantity": line_quantity,
                    "totalSale": round_to_decimal(line_quantity * amountEGP, rounding),
                    "netSale": round_to_decimal(netTotal, rounding)
                }

                if line.discount > 0:
                    order_line_without_tax["commercialDiscountData"] = [{
                        "amount": round_to_decimal(discountAmount, rounding),
                        'description': 'Discount'
                    }]

                if eta_code_type == 'EGS':
                    order_line_without_tax["itemCode"] = "EG-{}-{}".format(
                        order.company_id.eta_id, eta_item_code) if eta_item_code else errors.append('Line product [%s] Product Code' % line.product_id.name)
                elif eta_code_type == 'GS1':
                    order_line_without_tax["itemCode"] = eta_item_code if eta_item_code else errors.append(
                        'Product Code')
                else:
                    errors.append(
                        'Line product [%s] Code Type or Product Code' % line.product_id.name)

                totalOrderDiscount += round_to_decimal(
                    discountAmount, rounding)
                totalOrderSales += round_to_decimal(
                    line_quantity * amountEGP, rounding)

                totalLineDiscount += discountAmount
                totalLineSales += line_quantity * amountEGP

                netOrderAmount += round_to_decimal(
                    totalLineSales - totalLineDiscount, rounding)
                totalOrderAmount += round_to_decimal(
                    totalLineSales - totalLineDiscount + totalLineTaxs, rounding)

                order_line_without_tax['total'] = round_to_decimal(
                    netTotal + TotalTaxableFees + valueDifference + totalLineTaxs, rounding)

                order_line_without_tax['taxableItems'] = taxs

                order_lines_dict.append(order_line_without_tax)

            for orderTax in orderTaxs:
                orderTax['amount'] = round_to_decimal(
                    orderTax['amount'], rounding)
            order_session_id = order.session_id if order.session_id.eta_receipt_serial_id else order.config_id

            if ro_regenerate_time:
                date_time_issued = ro_regenerate_time.strftime(
                    "%Y-%m-%dT%H:%M:%SZ")
            elif order.date_order:
                date_time_issued = order.date_order.strftime(
                    "%Y-%m-%dT%H:%M:%SZ")
            else:
                date_time_issued = False

            current_order = {
                "header": {
                    'dateTimeIssued': date_time_issued if date_time_issued else errors.append('Date Time Issued'),
                    'receiptNumber': order.pos_reference,
                    'uuid': '',
                    'previousUUID': order_session_id.eta_receipt_serial_id.eta_receipt_last_uuid or '',
                    'currency': order.currency_id.name,
                    'orderdeliveryMode': 'FC'
                },
                "seller": {
                    "branchAddress": {
                        "country": order_session_id.eta_receipt_company_branch_id.eta_country_id.code if order_session_id.eta_receipt_company_branch_id.eta_country_id else errors.append('Company Country'),
                        "governate": order_session_id.eta_receipt_company_branch_id.eta_governate if
                        order_session_id.eta_receipt_company_branch_id.eta_governate else errors.append('Company Governate'),
                        "regionCity": order_session_id.eta_receipt_company_branch_id.eta_region_city if
                        order_session_id.eta_receipt_company_branch_id.eta_region_city else errors.append('Company City'),
                        "street": order_session_id.eta_receipt_company_branch_id.eta_street.replace(
                            '"', '|').replace("'", "|").replace('\xa0', ' ') if
                        order_session_id.eta_receipt_company_branch_id.eta_street else errors.append('Company Street'),
                        "buildingNumber": order_session_id.eta_receipt_company_branch_id.eta_building_number if order_session_id.eta_receipt_company_branch_id.eta_building_number else errors.append('Company Building Number')
                    },
                    "rin": order.company_id.eta_id if order.company_id.eta_id else errors.append('Company Id'),
                    "companyTradeName": order.company_id.eta_name.replace('"', '|').replace("'", "|").replace('\xa0', ' ') if
                    order.company_id.eta_name else errors.append('Name'),
                    "activityCode": str(order_session_id.eta_receipt_taxpayer_activity_code_id.code) if order_session_id.eta_receipt_taxpayer_activity_code_id else errors.append('Taxpayer Activity Code'),
                    "branchCode": order_session_id.eta_receipt_company_branch_id.eta_branchId if order_session_id.eta_receipt_company_branch_id.eta_branchId else errors.append('Company branchId'),
                    "deviceSerialNumber": order_session_id.eta_receipt_serial_id.eta_receipt_serial if order_session_id.eta_receipt_serial_id.eta_receipt_serial else errors.append('Company deviceSerialNumber'),

                },
                "buyer": {
                    "type": order.partner_id.eta_type if order.partner_id.eta_type else 'P'
                },
                "documentType": {
                    'receiptType': 'SR',
                    'typeVersion': order.company_id.eta_receipt_version
                },
                "itemData":
                order_lines_dict,
                "totalSales": round_to_decimal(totalOrderSales, rounding),
                "netAmount": round_to_decimal(netOrderAmount, rounding),
                "taxTotals":
                orderTaxs,
                "totalAmount": round_to_decimal(totalOrderAmount+extraOrderDiscount, rounding)

            }

            if totalOrderDiscount > 0:
                current_order["totalCommercialDiscount"] = round_to_decimal(
                    totalOrderDiscount, rounding)
            if extraOrderDiscount != 0:
                current_order["extraReceiptDiscountData"] = [{
                    "amount":round_to_decimal(
                    extraOrderDiscount, rounding),
                    "description": "Extra Discount"
                    }]
                
            if order.payment_ids:
                if order.payment_ids[0].payment_method_id.journal_id.type == 'cash':
                    payment_method = 'C'
                elif order.payment_ids[0].payment_method_id.journal_id.type == 'bank':
                    payment_method = 'V'
                else:
                    payment_method = 'O'
            else:
                payment_method = 'O'

            current_order['paymentMethod'] = payment_method

            if order.amount_total < 0:
                current_order['documentType']['receiptType'] = 'RR'
                #get return right uuid
                eta_receipt_reference_uuid = order.eta_receipt_reference_uuid
                if eta_receipt_reference_uuid not in order.refunded_order_ids.mapped('eta_receipt_uuid'):
                    eta_receipt_reference_uuid = order.refunded_order_ids[:1].eta_receipt_uuid
                    order.eta_receipt_reference_uuid = eta_receipt_reference_uuid

                current_order['header']['referenceUUID'] = eta_receipt_reference_uuid
                
            elif order.amount_total < 0:
                raise ValidationError(
                    _("Receipt Reference required."))

            if order.partner_id.eta_type:
                current_order["buyer"]["type"] = order.partner_id.eta_type
            elif order.partner_id.eta_type == "B" or totalOrderAmount >= order.company_id.eta_receipt_type_person_minimum:
                errors.append('Partner Type')

            if order.partner_id.eta_name:
                current_order["buyer"]["name"] = order.partner_id.eta_name.replace(
                    '"', '|').replace("'", "|").replace('\xa0', ' ').replace('\\', ':').replace('/',':')
            elif order.partner_id.eta_type == "B" or totalOrderAmount >= order.company_id.eta_receipt_type_person_minimum:
                errors.append('Partner Name')

            if order.partner_id.eta_id:
                current_order['buyer']['id'] = order.partner_id.eta_id
            elif order.partner_id.eta_type == "B" or totalOrderAmount >= order.company_id.eta_receipt_type_person_minimum:
                errors.append('Partner Id')

            if len(errors) > 0:
                continue
                #raise ValidationError(_('Fields are required {}'.format(errors)))

            # date can't be in future and utc time
            now = datetime.utcnow()
            then = datetime.utcfromtimestamp(
                int(datetime.timestamp(order.date_order)))
            diff = (now - then) / timedelta(seconds=1)

            if diff < 0:
                raise ValidationError(_("Time should be in UTC and not future."))

            # Keep first uuid
            to_write = {}
            """if order.eta_receipt_state != 'draft' and not order.eta_receipt_reference_old_uuid:
                current_order['header']['referenceOldUUID'] = order.eta_receipt_uuid
                to_write['eta_receipt_reference_old_uuid'] = order.eta_receipt_uuid
            elif order.eta_receipt_state != 'draft' and order.eta_receipt_reference_old_uuid:
                current_order['header']['referenceOldUUID'] = order.eta_receipt_reference_old_uuid"""

            # generate uuid
            serializeUUID = self.serializeUUID(current_order)
            uuid = sha256(serializeUUID.encode('utf-8')).hexdigest()

            current_order['header']['uuid'] = uuid

            to_write['eta_receipt_json_text'] = current_order
            to_write['eta_receipt_uuid'] = uuid
            order_session_id.eta_receipt_serial_id.eta_receipt_last_uuid = uuid
            to_write['eta_receipt_state'] = 'draft'

            order.write(to_write)

        return True
