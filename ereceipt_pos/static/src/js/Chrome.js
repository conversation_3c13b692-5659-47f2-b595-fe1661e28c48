odoo.define('ereceipt_pos.chrome', function (require) {
    'use strict';

    const Chrome = require('point_of_sale.Chrome');
    const Registries = require('point_of_sale.Registries');

    const PosResChrome = (Chrome) =>
        class extends Chrome {
            /**
             * @override
             */
            async _closePos() {
                await this.rpc({
					model: 'pos.session',
					method: 'post_set_eta_receipt_last_uuid',
					args: [this.env.pos.pos_session.id, this.env.pos.eta_receipt_last_uuid]
				})
                await super._closePos();
            }
        };

    Registries.Component.extend(Chrome, PosResChrome);

    return Chrome;
});
