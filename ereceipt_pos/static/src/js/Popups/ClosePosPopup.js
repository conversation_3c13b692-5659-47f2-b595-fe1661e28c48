odoo.define('ereceipt_pos.ClosePosPopup', function (require) {
    'use strict';

    const ClosePosPopup = require('point_of_sale.ClosePosPopup');
    const Registries = require('point_of_sale.Registries');

    const saveClosePosPopup = (ClosePosPopup) =>
        class extends ClosePosPopup {
            async closeSession() {
                if (!this.closeSessionClicked) {
                    this.closeSessionClicked = true;
                    let response;
                    if (this.cashControl) {
                        response = await this.rpc({
                            model: 'pos.session',
                            method: 'post_closing_cash_details',
                            args: [this.env.pos.pos_session.id],
                            kwargs: {
                                counted_cash: this.state.payments[this.defaultCashDetails.id].counted,
                            }
                        })
                        if (!response.successful) {
                            return this.handleClosingError(response);
                        }
                    }
                    await this.rpc({
                        model: 'pos.session',
                        method: 'update_closing_control_state_session',
                        args: [this.env.pos.pos_session.id, this.state.notes]
                    })
                    try {
                        const bankPaymentMethodDiffPairs = this.otherPaymentMethods
                            .filter((pm) => pm.type == 'bank')
                            .map((pm) => [pm.id, this.state.payments[pm.id].difference]);
                        response = await this.rpc({
                            model: 'pos.session',
                            method: 'close_session_from_ui',
                            args: [this.env.pos.pos_session.id, bankPaymentMethodDiffPairs, this.env.pos.eta_receipt_last_uuid],
                            context: this.env.session.user_context,
                        });
                        if (!response.successful) {
                            return this.handleClosingError(response);
                        }
                        window.location = '/web#action=point_of_sale.action_client_pos_menu';
                    } catch (error) {
                        const iError = identifyError(error);
                        if (iError instanceof ConnectionLostError || iError instanceof ConnectionAbortedError) {
                            await this.showPopup('ErrorPopup', {
                                title: this.env._t('Network Error'),
                                body: this.env._t('Cannot close the session when offline.'),
                            });
                        } else {
                            await this.showPopup('ErrorPopup', {
                                title: this.env._t('Closing session error'),
                                body: this.env._t(
                                    'An error has occurred when trying to close the session.\n' +
                                    'You will be redirected to the back-end to manually close the session.')
                            })
                            window.location = '/web#action=point_of_sale.action_client_pos_menu';
                        }
                    }
                    this.closeSessionClicked = false;
                }
            }
        };

    Registries.Component.extend(ClosePosPopup, saveClosePosPopup);

    return ClosePosPopup;
});
