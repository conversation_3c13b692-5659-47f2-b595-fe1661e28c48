odoo.define('ereceipt_pos.TicketScreen', function (require) {
    'use strict';

    const TicketScreen = require('point_of_sale.TicketScreen');
    const Registries = require('point_of_sale.Registries');

    const saveTicketScreen = (TicketScreen) =>
        class extends TicketScreen {
            async _onDoRefund() {
                const order = this.getSelectedSyncedOrder();
    
                if (!order) {
                    this._state.ui.highlightHeaderNote = !this._state.ui.highlightHeaderNote;
                    return;
                }
                
                if (this._doesOrderHaveSoleItem(order)) {
                    if (!this._prepareAutoRefundOnOrder(order)) {
                        // Don't proceed on refund if preparation returned false.
                        return;
                    }
                }
    
                const partner = order.get_partner();
    
                const allToRefundDetails = this._getRefundableDetails(partner);
                if (allToRefundDetails.length == 0) {
                    this._state.ui.highlightHeaderNote = !this._state.ui.highlightHeaderNote;
                    return;
                }
    
                // The order that will contain the refund orderlines.
                // Use the destinationOrder from props if the order to refund has the same
                // partner as the destinationOrder.
                const destinationOrder =
                    this.props.destinationOrder &&
                    partner === this.props.destinationOrder.get_partner() &&
                    !this.env.pos.doNotAllowRefundAndSales()
                        ? this.props.destinationOrder
                        : this._getEmptyOrder(partner);
    
                //Add a check too see if the fiscal position exist in the pos
                if (order.fiscal_position_not_found) {
                    this.showPopup('ErrorPopup', {
                        title: this.env._t('Fiscal Position not found'),
                        body: this.env._t('The fiscal position used in the original order is not loaded. Make sure it is loaded by adding it in the pos configuration.')
                    });
                    return;
                }
                
                //For ETA
                if (order.eta_receipt_uuid)
                    destinationOrder.set_eta_receipt_reference_uuid(order.eta_receipt_uuid)
                
                // Add orderline for each toRefundDetail to the destinationOrder.
                for (const refundDetail of allToRefundDetails) {
                    const product = this.env.pos.db.get_product_by_id(refundDetail.orderline.productId);
                    const options = this._prepareRefundOrderlineOptions(refundDetail);
                    await destinationOrder.add_product(product, options);
                    refundDetail.destinationOrderUid = destinationOrder.uid;
                }
                destinationOrder.fiscal_position = order.fiscal_position;
    
                // Set the partner to the destinationOrder.
                if (partner && !destinationOrder.get_partner()) {
                    destinationOrder.set_partner(partner);
                    destinationOrder.updatePricelist(partner);
                }
    
                if (this.env.pos.get_order().cid !== destinationOrder.cid) {
                    this.env.pos.set_order(destinationOrder);
                }
    
                this._onCloseScreen();
            }
        };
    Registries.Component.extend(TicketScreen, saveTicketScreen);

    return TicketScreen;
});
