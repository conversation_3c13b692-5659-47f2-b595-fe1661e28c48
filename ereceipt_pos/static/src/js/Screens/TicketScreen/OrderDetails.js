odoo.define('ereceipt_pos.OrderDetails', function (require) {
    'use strict';

    const OrderDetails = require('point_of_sale.OrderDetails');
    const Registries = require('point_of_sale.Registries');

    const saveOrderDetails = (OrderDetails) =>
    class extends OrderDetails {
        get eta_receipt_reference_uuid() {
            return this.order.get_eta_receipt_reference_uuid();
        }
    };
    Registries.Component.extend(OrderDetails, saveOrderDetails);

    return OrderDetails;
});
