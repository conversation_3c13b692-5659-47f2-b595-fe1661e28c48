odoo.define('ereceipt_pos.OrderRUuidButton', function (require) {
    'use strict';

    const PosComponent = require('point_of_sale.PosComponent');
    const ProductScreen = require('point_of_sale.ProductScreen');
    const { useListener } = require("@web/core/utils/hooks");
    const Registries = require('point_of_sale.Registries');

    class OrderRUuidButton extends PosComponent {
        setup() {
            super.setup();
            useListener('click', this.onClick);
        }
        async onClick() {
            const selectedOrder = this.env.pos.get_order();
            if (!selectedOrder) return;

            const { confirmed, payload: inputRUUID } = await this.showPopup('TextAreaPopup', {
                startingValue: selectedOrder.get_eta_receipt_reference_uuid(),
                title: this.env._t('Add Return UUID'),
            });

            if (confirmed) {
                selectedOrder.set_eta_receipt_reference_uuid(inputRUUID);
            }
        }
    }
    OrderRUuidButton.template = 'OrderRUuidButton';

    ProductScreen.addControlButton({
        component: OrderRUuidButton
    });

    Registries.Component.add(OrderRUuidButton);

    return OrderRUuidButton;
});
