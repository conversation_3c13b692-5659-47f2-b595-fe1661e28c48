odoo.define('ereceipt_pos.OrderWidget', function(require) {
    'use strict';

    const OrderWidget = require('point_of_sale.OrderWidget');
    const Registries = require('point_of_sale.Registries');

    const saveOrderWidget = (OrderWidget) =>
    class extends OrderWidget {
        get eta_receipt_reference_uuid() {
            return this.order.get_eta_receipt_reference_uuid();
        }
    };
    Registries.Component.extend(OrderWidget, saveOrderWidget);

    return OrderWidget;
});
