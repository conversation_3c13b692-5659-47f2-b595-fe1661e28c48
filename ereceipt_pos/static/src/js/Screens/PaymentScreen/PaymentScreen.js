odoo.define("ereceipt_pos.PaymentScreen", function (require) {
    "use strict";

    const PaymentScreen = require('point_of_sale.PaymentScreen');
    const Registries = require('point_of_sale.Registries');


    const PosUUIDPaymentScreen = PaymentScreen => class extends PaymentScreen {
        //@Override
        async validateOrder(isForceValidate) {
            var self = this.currentOrder;
            var company = self.pos.company;

            if (await this._isOrderValid(isForceValidate)) {
                //try {

                if (company.country && company.country.code === 'EG' && !self.is_settlement()) {



                    var min_total_req_id = company.eta_receipt_type_person_minimum
                    const client = self.get_partner() || self.pos.db.get_partner_by_id(721103)

                    if (client) {
                        if (!client.eta_id && (Math.abs(self.get_total_with_tax()) >= min_total_req_id || client.eta_type == 'B')) {

                            return this.showPopup('ErrorPopup', {
                                title: this.env._t('Required Fields!'),
                                body: this.env._t("Client Tax/National Id required."),
                            });
                        }
                    }
                    else {
                        if (Math.abs(self.get_total_with_tax()) >= min_total_req_id) {
                            return this.showPopup('ErrorPopup', {
                                title: this.env._t('Required Fields!'),
                                body: this.env._t("Client required."),
                            });
                        }
                    }

                    var order = {};
                    const rounding = 4;

                    var validation_date = new Date().toISOString();
                    const receiptDateAndTime = validation_date.split('.')[0] + 'Z';

                    order.header = {
                        'dateTimeIssued': receiptDateAndTime,
                        'receiptNumber': self.get_name(),
                        'uuid': '',
                        'previousUUID': self.pos.eta_receipt_last_uuid || '',
                        'currency': self.pos.currency.name,
                        'orderdeliveryMode': 'FC'
                    }

                    order.documentType = {
                        'receiptType': 'SR',
                        'typeVersion': company.eta_receipt_version
                    }

                    //For return
                    if (self.eta_receipt_reference_uuid && self.get_total_with_tax() < 0) {
                        order.documentType.receiptType = 'RR'
                        order.header.referenceUUID = self.eta_receipt_reference_uuid
                    }
                    /*else if (self.get_total_with_tax() < 0) {
                        return this.showPopup('ErrorPopup', {
                            title: this.env._t('Required Fields!'),
                            body: this.env._t("Receipt Reference required."),
                        });
                    }*/

                    if (self.pos.config.eta_receipt_company_branch_id && self.pos.config.eta_receipt_serial_id && self.pos.config.eta_receipt_taxpayer_activity_code_id) {

                        order.seller = {
                            'rin': company.eta_id,
                            'companyTradeName': (company.eta_name || '').replace('"', '|').replace("'", "|").replace('\xa0', ' '),
                            'branchCode': self.pos.config.eta_receipt_company_branch_id.eta_branchId,
                            'branchAddress': {
                                'country': 'EG',
                                'governate': self.pos.config.eta_receipt_company_branch_id.eta_governate,
                                'regionCity': self.pos.config.eta_receipt_company_branch_id.eta_region_city,
                                'street': (self.pos.config.eta_receipt_company_branch_id.eta_street || '').replace('"', '|').replace("'", "|").replace('\xa0', ' '),
                                'buildingNumber': self.pos.config.eta_receipt_company_branch_id.eta_building_number
                            },
                            'deviceSerialNumber': self.pos.config.eta_receipt_serial_id.eta_receipt_serial,
                            'activityCode': self.pos.config.eta_receipt_taxpayer_activity_code_id.code
                        }
                    }

                    order.buyer = {
                        'type': 'P'
                    }

                    //TODO: add mandatory when > min_total_req_id
                    if (client) {
                        if (client.eta_type) order.buyer.type = client.eta_type
                        if (client.eta_id) order.buyer.id = client.eta_id
                        if (client.eta_name) order.buyer.name = (client.eta_name || '').replace('"', '|').replace("'", "|").replace('\xa0', ' ').replace('\\', ':').replace('/',':')
                    }

                    var itemData = []
                    var totalCommercialDiscount = 0
                    var totalExtraDiscount = 0
                    var totalSales = 0
                    var totalAmount = 0
                    //var totalNetSale = 0
                    var errorExist = 0


                    var order_lines = self.get_orderlines()

                    //get price if tax include
                    var new_order_lines = []

                    var is_line_positive, should_stop = false;

                    if (order_lines.length > 0) {
                        if (order_lines[0].quantity >= 0)
                            is_line_positive = true
                        else
                            is_line_positive = false;
                    }

                    order_lines.forEach(order_line => {
                        //check to make all lines delivery or return

                        if (is_line_positive && order_line.quantity < 0 || !is_line_positive && order_line.quantity > 0) {

                            should_stop = true;
                        }

                        var taxes_ids = order_line.tax_ids || order_line.product.taxes_id;

                        if (taxes_ids) {
                            taxes_ids = _.filter(taxes_ids, t => t in self.pos.taxes_by_id);
                            order_line.ro_new_price = order_line.price


                            var all_taxes = order_line.compute_all(order_line.pos.get_taxes_after_fp(taxes_ids), order_line.ro_new_price, order_line.get_quantity(), self.pos.currency.rounding);

                            _(all_taxes.taxes).each(function (tax) {
                                if (tax.price_include) {
                                    order_line.ro_new_price = self.roundNumberAndFill(
                                        order_line.ro_new_price / (1 + (Math.abs(tax.tax_rate) / 100)), rounding)
                                }
                            });

                        }
                        if (order_line.ro_new_price < 0)
                            totalExtraDiscount += order_line.ro_new_price
                        else if(order_line.quantity != 0)
                            new_order_lines.push(order_line)
                    })

                    if (should_stop) {
                        return this.showPopup('ErrorPopup', {
                            title: this.env._t('Wrong Receipt!'),
                            body: this.env._t("Receipt must be for delivery or return."),
                        });
                    }

                    order_lines = new_order_lines

                    if (order_lines.length > 0) {

                        order_lines.forEach(order_line => {

                            var line_total_discount = 0;
                            var line_total_tax = 0;

                            var total_line = self.roundNumberAndFill(order_line.quantity * order_line.ro_new_price, rounding);
                            totalSales += total_line;
                            var eta_item_code;

                            if (order_line.product.eta_code_type == 'egs') eta_item_code = 'EG-' + company.eta_id + '-' + order_line.product.eta_item_code
                            else eta_item_code = order_line.product.eta_item_code

                            var eta_code_type;

                            if (order_line.product.eta_code_type) eta_code_type = order_line.product.eta_code_type.toUpperCase()
                            else {eta_code_type = ''; errorExist=1}

                            const price_without_tax = order_line.get_price_without_tax()

                            const line_net_sale = self.roundNumberAndFill(price_without_tax, rounding);

                            //totalNetSale += line_net_sale

                            var data_line = {
                                'internalCode': order_line.product.default_code || '',
                                'description': (order_line.product.display_name || '').replace('"', '|').replace("'", "|").replace('\xa0', ' ').replace('\n', ' ').replace('\\', ':').replace('/',':'),
                                'itemType': eta_code_type,
                                'itemCode': eta_item_code || '',
                                'unitType': order_line.get_unit().eta_code,
                                'quantity': self.roundNumberAndFill(order_line.quantity, rounding),
                                'unitPrice': self.roundNumberAndFill(order_line.ro_new_price, rounding),
                                //total without tax after applying discount
                                'netSale': total_line,
                                //only qty * price
                                'totalSale': total_line,
                                //total here as total shift to next record this is wrong behavior
                                'total': total_line
                            }

                            //Discount per order_line
                            if (order_line.discount > 0) {
                                line_total_discount = self.roundNumberAndFill(total_line - price_without_tax, rounding);
                                line_total_discount = line_total_discount
                                totalCommercialDiscount += line_total_discount
                                data_line.commercialDiscountData = [{
                                    'amount': self.roundNumberAndFill(line_total_discount, rounding),
                                    'description': 'Discount'
                                }]
                            }

                            //Taxes per order_line
                            var taxes_ids = order_line.tax_ids || order_line.product.taxes_id;

                            if (taxes_ids) {
                                var taxableItems = []
                                taxes_ids = _.filter(taxes_ids, t => t in self.pos.taxes_by_id);

                                var all_taxes = order_line.compute_all(order_line.pos.get_taxes_after_fp(taxes_ids), order_line.ro_new_price, order_line.get_quantity(), self.pos.currency.rounding);

                                _(all_taxes.taxes).each(function (tax) {
                                    var tax_round_amount = self.roundNumberAndFill((tax.tax_rate / 100) * data_line.netSale, rounding)
                                    taxableItems.push({
                                        'taxType': tax.eta_code,
                                        'amount': tax_round_amount,
                                        'subType': tax.eta_sub_code,
                                        'rate': self.roundNumberAndFill(tax.tax_rate, rounding),
                                    })

                                    line_total_tax += tax_round_amount
                                });

                                data_line.taxableItems = taxableItems
                            }


                            var price_with_tax_before_discount = total_line + line_total_tax - line_total_discount


                            totalAmount += price_with_tax_before_discount;

                            //total with tax removing discounts
                            //data_line.total = self.roundNumberAndFill(price_with_tax_before_discount, rounding)



                            itemData.push(data_line)
                        });

                        order.itemData = itemData;

                        order.totalSales = self.roundNumberAndFill(totalSales, rounding);

                        order.netAmount = self.roundNumberAndFill(totalSales, rounding);
                        order.totalAmount = self.roundNumberAndFill(totalAmount+totalExtraDiscount, rounding);

                        if (self.get_tax_details()) {
                            var taxableTotals = []
                            _(self.get_tax_details()).each(function (totalTax) {
                                taxableTotals.push({
                                    'taxType': totalTax.eta_code,
                                    'amount': self.roundNumberAndFill(totalTax.amount, rounding)
                                })
                            });

                            order.taxTotals = taxableTotals
                        }

                        if (totalCommercialDiscount > 0) order.totalCommercialDiscount = totalCommercialDiscount

                        if (totalExtraDiscount != 0) {
                            order.extraReceiptDiscountData = [{
                                'amount': self.roundNumberAndFill(totalExtraDiscount, rounding),
                                'description': "Extra Discount",
                            }]
                        }

                        var paymentMethod;
                        var paymentlines = self.paymentlines
                            .filter(function (paymentline) {
                                return !paymentline.is_change;
                            })
                            .map(function (paymentline) {
                                return paymentline.export_for_printing();
                            });

                        if (paymentlines.length <= 0) paymentMethod = 'O';
                        else if (paymentlines[0].name.search(/cash/i) != -1) paymentMethod = 'C';
                        else if (paymentlines[0].name.search(/bank/i) != -1 || paymentlines[0].name.search(/visa/i) != -1) paymentMethod = 'V';
                        else paymentMethod = 'O';

                        order.paymentMethod = paymentMethod


                        //console.log(order)
                        //console.log(ad111as)

                        //generate uuid
                        var serializeUUID = self.serialize(order)
                        var uuid = CryptoJS.SHA256(serializeUUID).toString()


                        if (errorExist == 0)
                            self.eta_receipt_uuid = uuid

                        self.eta_receipt_previous_uuid = self.pos.eta_receipt_last_uuid || ''

                        order.header.uuid = uuid

                        
                        self.eta_receipt_json_text = order

                        self.totalAmount = totalAmount

                        self.pos.eta_receipt_last_uuid = uuid

                    }
                }

                /*} catch (e) {
                    // do nothing with the error
                }*/
            } else {
                return; // do nothing if the order is not valid
            }


            ///JUSTFORTEST
            /*return this.showPopup('ErrorPopup', {
                title: this.env._t('Select CustomeropDA'),
                body: this.env._t("Please Select a customerooAD"),
            });*/
            ///////////

            await super.validateOrder(...arguments);
        }

    };

    Registries.Component.extend(PaymentScreen, PosUUIDPaymentScreen);

    return PosUUIDPaymentScreen;

});
