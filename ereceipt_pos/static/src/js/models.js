odoo.define('ereceipt_pos.pos', function (require) {
	"use strict";

	var { PosGlobalState, Orderline, Order } = require('point_of_sale.models');
	const Registries = require('point_of_sale.Registries');
	var utils = require('web.utils');
	var round_pr = utils.round_precision;

	const ETAPosGlobalState = (PosGlobalState) => class ETAPosGlobalState extends PosGlobalState {

		async after_load_server_data() {
			await super.after_load_server_data(...arguments);
			if (this.config.eta_receipt_serial_id)
				this.eta_receipt_last_uuid = this.config.eta_receipt_serial_id.eta_receipt_last_uuid || ''
			else
				this.eta_receipt_last_uuid = ''
		}
		/**
		 * Mirror JS method of:
		 * compute_all in addons/account/models/account.py
		 *
		 * Read comments in the python side method for more details about each sub-methods.
		 */
		compute_all(taxes, price_unit, quantity, currency_rounding, handle_price_include = true) {
			var self = this;

			// 1) Flatten the taxes.

			var _collect_taxes = function (taxes, all_taxes) {
				taxes = [...taxes].sort(function (tax1, tax2) {
					return tax1.sequence - tax2.sequence;
				});
				_(taxes).each(function (tax) {
					if (tax.amount_type === 'group')
						all_taxes = _collect_taxes(tax.children_tax_ids, all_taxes);
					else
						all_taxes.push(tax);
				});
				return all_taxes;
			}
			var collect_taxes = function (taxes) {
				return _collect_taxes(taxes, []);
			}

			taxes = collect_taxes(taxes);

			// 2) Deal with the rounding methods

			var round_tax = this.company.tax_calculation_rounding_method != 'round_globally';

			var initial_currency_rounding = currency_rounding;
			if (!round_tax)
				currency_rounding = currency_rounding * 0.00001;

			// 3) Iterate the taxes in the reversed sequence order to retrieve the initial base of the computation.
			var recompute_base = function (base_amount, fixed_amount, percent_amount, division_amount) {
				return (base_amount - fixed_amount) / (1.0 + percent_amount / 100.0) * (100 - division_amount) / 100;
			}

			var base = round_pr(price_unit * quantity, initial_currency_rounding);

			var sign = 1;
			if (base < 0) {
				base = -base;
				sign = -1;
			}

			var total_included_checkpoints = {};
			var i = taxes.length - 1;
			var store_included_tax_total = true;

			var incl_fixed_amount = 0.0;
			var incl_percent_amount = 0.0;
			var incl_division_amount = 0.0;

			var cached_tax_amounts = {};
			if (handle_price_include) {
				_(taxes.reverse()).each(function (tax) {
					if (tax.include_base_amount) {
						base = recompute_base(base, incl_fixed_amount, incl_percent_amount, incl_division_amount);
						incl_fixed_amount = 0.0;
						incl_percent_amount = 0.0;
						incl_division_amount = 0.0;
						store_included_tax_total = true;
					}
					if (tax.price_include) {
						if (tax.amount_type === 'percent')
							incl_percent_amount += tax.amount;
						else if (tax.amount_type === 'division')
							incl_division_amount += tax.amount;
						else if (tax.amount_type === 'fixed')
							incl_fixed_amount += Math.abs(quantity) * tax.amount
						else {
							var tax_amount = self._compute_all(tax, base, quantity);
							incl_fixed_amount += tax_amount;
							cached_tax_amounts[i] = tax_amount;
						}
						if (store_included_tax_total) {
							total_included_checkpoints[i] = base;
							store_included_tax_total = false;
						}
					}
					i -= 1;
				});
			}

			var total_excluded = round_pr(recompute_base(base, incl_fixed_amount, incl_percent_amount, incl_division_amount), initial_currency_rounding);
			var total_included = total_excluded;

			// 4) Iterate the taxes in the sequence order to fill missing base/amount values.

			base = total_excluded;

			var skip_checkpoint = false;

			var taxes_vals = [];
			i = 0;
			var cumulated_tax_included_amount = 0;
			_(taxes.reverse()).each(function (tax) {
				if (tax.price_include || tax.is_base_affected)
					var tax_base_amount = base;
				else
					var tax_base_amount = total_excluded;

				if (!skip_checkpoint && tax.price_include && total_included_checkpoints[i] !== undefined) {
					var tax_amount = total_included_checkpoints[i] - (base + cumulated_tax_included_amount);
					cumulated_tax_included_amount = 0;
				} else
					var tax_amount = self._compute_all(tax, tax_base_amount, quantity, true);

				tax_amount = round_pr(tax_amount, currency_rounding);

				if (tax.price_include && total_included_checkpoints[i] === undefined)
					cumulated_tax_included_amount += tax_amount;

				taxes_vals.push({
					'id': tax.id,
					'name': tax.name,
					'amount': sign * tax_amount,
					'base': sign * round_pr(tax_base_amount, currency_rounding),
					'tax_rate': tax.amount,
					'eta_code': tax.eta_code,
					'eta_sub_code': tax.eta_sub_code,
					'price_include': tax.price_include
				});

				if (tax.include_base_amount) {
					base += tax_amount;
					if (!tax.price_include)
						skip_checkpoint = true;
				}

				total_included += tax_amount;
				i += 1;
			});

			return {
				'taxes': taxes_vals,
				'total_excluded': sign * round_pr(total_excluded, this.currency.rounding),
				'total_included': sign * round_pr(total_included, this.currency.rounding),
			};
		}
	}
	Registries.Model.extend(PosGlobalState, ETAPosGlobalState);

	const ETAOrderline = (Orderline) => class ETAOrderline extends Orderline {
		init_from_JSON(json) {
			super.init_from_JSON(...arguments);
			this.ro_new_price = json.ro_new_price;
		}
		export_as_JSON() {
			const json = super.export_as_JSON(...arguments);

			json.ro_new_price = this.ro_new_price

			return json
		}
	}

	Registries.Model.extend(Orderline, ETAOrderline);

	const ETAOrder = (Order) => class ETAOrder extends Order {
		//@override
		init_from_JSON(json) {
			super.init_from_JSON(...arguments);
			this.eta_receipt_uuid = json.eta_receipt_uuid;
		}
		export_as_JSON() {
			const json = super.export_as_JSON(...arguments);

			json.eta_receipt_uuid = this.eta_receipt_uuid
			json.eta_receipt_previous_uuid = this.eta_receipt_previous_uuid || ''
			json.eta_receipt_reference_uuid = this.get_eta_receipt_reference_uuid() || ''
			json.eta_receipt_json_text = this.eta_receipt_json_text || ''

			return json
		}
		export_for_printing() {
			const result = super.export_for_printing(...arguments);
			if (this.eta_receipt_uuid) {
				const receiptDateAndTime = result.date.isostring.split('.')[0] + 'Z';
				const company = this.pos.company;
				const eInvoicingPortalURL = company.eta_portal_url;
				result.is_settlement = this.is_settlement();
				result.eta_receipt_uuid = this.eta_receipt_uuid

				//QR
				const codeWriter = new window.ZXing.BrowserQRCodeSvgWriter()
				let qr_code_svg = new XMLSerializer().serializeToString(codeWriter.write(eInvoicingPortalURL + '/receipts/search/' + this.eta_receipt_uuid + '/share/' + receiptDateAndTime + '#Total:' + this.totalAmount + ',IssuerRIN:' + company.eta_id, 150, 150));
				result.qr_code = "data:image/svg+xml;base64," + window.btoa(qr_code_svg);
			}
			return result;
		}
		get_tax_details() {
			var details = {};
			var fulldetails = [];

			this.orderlines.forEach(function (line) {
				var ldetails = line.get_tax_details();
				for (var id in ldetails) {
					if (ldetails.hasOwnProperty(id)) {
						let amount = (details[id] && details[id].amount) ? details[id].amount : 0;
						let base = (details[id] && details[id].base) ? details[id].base : 0;
						details[id] = {
							amount: amount + ldetails[id].amount,
							base: base + ldetails[id].base,
						};
					}
				}
			});

			for (var id in details) {
				if (details.hasOwnProperty(id)) {
					fulldetails.push({ amount: details[id].amount, base: details[id].base, tax: this.pos.taxes_by_id[id], name: this.pos.taxes_by_id[id].name, eta_code: this.pos.taxes_by_id[id].eta_code });
				}
			}

			return fulldetails;
		}
		/* Rounding the number and filling the number with zeros. */
		roundNumberAndFill(givenNumber, rounding) {
			const roundLength = rounding;
			const roundingDecimals = 10 ** rounding;
			return Math.abs(parseFloat((Math.round((givenNumber + Number.EPSILON) * roundingDecimals) / roundingDecimals).toFixed(roundLength)))
		}
		serialize(documentStructure) {

			var self = this;


			var normalTypes = ['number', 'bigint', 'string', 'boolean'];
			var typeOfDocumentStructure = typeof (documentStructure);

			if (normalTypes.find((str) => str === typeOfDocumentStructure)) {

				return '"' + documentStructure.toString() + '"';
			}

			var serializedString = "";

			for (const [key, value] of Object.entries(documentStructure)) {

				if (typeof (value) != 'object') {
					serializedString += '"' + key.toUpperCase() + '"';
					serializedString += self.serialize(value);
				}

				if (typeof (value) == 'object') {

					serializedString += '"' + key.toUpperCase() + '"'

					for (const [minKey, minValue] of Object.entries(value)) {

						var keyName;

						if (isNaN(parseInt(minKey.toUpperCase()))) keyName = minKey.toUpperCase();
						else keyName = key.toUpperCase();

						serializedString += '"' + keyName + '"'

						serializedString += self.serialize(minValue);
					};
				}
			};

			return serializedString
		}
		/**
		 * If the order is empty (there are no products)
		 * and all "pay_later" payments are negative,
		 * we are settling a customer's account.
		 * If the module pos_settle_due is not installed,
		 * the function always returns false (since "pay_later" doesn't exist)
		 * @returns {boolean} true if the current order is a settlement, else false
		 */
		is_settlement() {
			return this.is_empty() &&
				!!this.paymentlines.filter(paymentline => paymentline.payment_method.type === "pay_later" && paymentline.amount < 0).length;
		}
		set_eta_receipt_reference_uuid(ruuid) {
			this.eta_receipt_reference_uuid = ruuid;
		}
		get_eta_receipt_reference_uuid() {
			return this.eta_receipt_reference_uuid;
		}
	}
	Registries.Model.extend(Order, ETAOrder);

});
