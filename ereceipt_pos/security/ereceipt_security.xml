<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Multi - Company Rules -->
        <record model="ir.rule" id="comp_branch_comp_rule">
            <field name="name">Company Branch multi-company</field>
            <field name="model_id" ref="model_eta_company_branch"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
        </record>
        <record model="ir.rule" id="active_types_comp_rule">
            <field name="name">Active Types multi-company</field>
            <field name="model_id" ref="model_eta_activity_types"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
        </record>
        <record model="ir.rule" id="receipt_serial_comp_rule">
            <field name="name">Receipt Serial multi-company</field>
            <field name="model_id" ref="model_receipt_serial"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
        </record>
    </data>
</odoo>