# -*- coding: utf-8 -*-

{
    'name': 'Egypt Electronic Receipt',
    'version': '14.1',
    'summary': 'ETA Electronic Receipt For Retail',
    'category': 'Account',
    'author': 'Roaya',
    'website': 'https://www.roayadm.com',
    'depends': ['point_of_sale'],
    'data': [
        'security/ereceipt_security.xml',
        'security/ir.model.access.csv',
        'wizard/pos_order_regenerate.xml',
        'views/settings.xml',
        'views/receipt_serial.xml',
        'views/res_config_settings_views.xml',
        'views/pos_order.xml',
        #Exist in einvoice
        'views/partner.xml',
        'views/company.xml',
        'views/uom.xml',
        'views/tax.xml',
        'views/product.xml',
        'views/eta_activity_types.xml',
        'views/eta_branch.xml',
    ],
    'assets': {
        'point_of_sale.assets': [
            'ereceipt_pos/static/src/js/crypto-js.min.js',
            'web/static/lib/zxing-library/zxing-library.js',
            'ereceipt_pos/static/src/js/models.js',
            'ereceipt_pos/static/src/js/Chrome.js',
            'ereceipt_pos/static/src/js/Screens/TicketScreen/OrderDetails.js',
            'ereceipt_pos/static/src/js/Screens/TicketScreen/TicketScreen.js',
            'ereceipt_pos/static/src/js/Screens/ProductScreen/OrderWidget.js',
            'ereceipt_pos/static/src/js/Screens/PaymentScreen/PaymentScreen.js',
            'ereceipt_pos/static/src/js/Screens/ProductScreen/ControlButtons/OrderRUuidButton.js',
            'ereceipt_pos/static/src/js/Popups/ClosePosPopup.js',
            'ereceipt_pos/static/src/xml/**/*'
        ]
    },
    'installable': True,
    'license': 'OPL-1'
}
