# -*- coding: utf-8 -*-
from odoo import fields, models, _


class PosOrderRegenerate(models.TransientModel):
    _name = 'pos.order.regenerate'

    ro_regenerate_time = fields.Datetime(string="Regenerate Time")

    def action_pos_regenerate_eta(self):
        pos_order_ids = self.env.context.get('active_ids')
        self.env['eta.receipt.manage']._send_regenerate_receipt(pos_order_ids, self.ro_regenerate_time)
        