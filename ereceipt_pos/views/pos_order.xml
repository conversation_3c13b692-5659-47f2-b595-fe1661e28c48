<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_order_eta_receipt_form" model="ir.ui.view">
        <field name="model">pos.order</field>
        <field name="inherit_id" ref="point_of_sale.view_pos_pos_form"/>
        <field name="arch" type="xml">
            <xpath expr="//header" position="inside">
                <field name="eta_receipt_state" widget="statusbar" statusbar_visible="accepted,rejected,valid,invalid"/>
            </xpath>
            <xpath expr="//button[@name='refund']" position="after">
                <button name="action_pos_send_eta" style="background-color: #5c5963;color: white;" 
                type="object" string="ETA Receipt Send" attrs="{'invisible':['|',('eta_receipt_state', '!=', 'draft'),('eta_receipt_uuid', '=', False)]}" groups="account.group_account_invoice"/>
                
                <button name="action_pos_get_state" style="background-color: #5c5963;color: white;" 
                            type="object" 
                            string="Get Receipt State" attrs="{'invisible':[
                            ('eta_receipt_state', 'not in', ('accepted','submitted'))]}" 
                            groups="account.group_account_invoice" />
            </xpath>
            <xpath expr="//notebook" position="inside">
                <page string="ETA Receipt">
                    <group>
                        <group>
                            <field name="eta_receipt_serial_id" 
                            options='{"no_open": True, "no_create": True}' 
                            />
                            <field name="eta_receipt_company_branch_id" 
                            options='{"no_open": True, "no_create": True}' 
                            />
                            <field name="eta_receipt_taxpayer_activity_code_id" 
                            options='{"no_open": True, "no_create": True}' 
                            />
                        </group>
                        <group>
                            <field name="eta_receipt_reference_uuid" attrs="{'invisible': [('eta_receipt_reference_uuid','=',False)]}"/>
                            <field name="eta_receipt_uuid"
                        attrs="{'invisible': [('eta_receipt_uuid','=',False)]}"
                        />
                        </group>
                        <group>
                            <field name="eta_receipt_reject_reason" 
                        attrs="{'invisible': [('eta_receipt_state','!=','rejected')]}"
                        />
                            <field name="eta_receipt_invalid_reason" 
                        attrs="{'invisible': [('eta_receipt_state','!=','invalid')]}"
                        />
                        </group>
                    </group>
                </page>
            </xpath>
        </field>
    </record>
    <record id="view_order_eta_receipt_tree" model="ir.ui.view">
        <field name="model">pos.order</field>
        <field name="inherit_id" ref="point_of_sale.view_pos_order_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='state']" position="after">
                <field name="eta_receipt_state" widget="badge" decoration-success="eta_receipt_state == 'valid'" 
                decoration-info="eta_receipt_state in ('accepted','submitted')" decoration-warning="eta_receipt_state == 'invalid'" 
                decoration-danger="eta_receipt_state == 'rejected'" optional="show"/>
            </xpath>
        </field>
    </record>
    <record id="view_order_eta_receipt_search" model="ir.ui.view">
        <field name="model">pos.order</field>
        <field name="inherit_id" ref="point_of_sale.view_pos_order_filter"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='session_id']" position="after">
                <field name="eta_receipt_uuid"/>
            </xpath>
            <xpath expr="//filter[@name='order_date']" position="after">
                <separator/>
                <filter name="valid" string="Valid" domain="[('eta_receipt_state','=','valid')]"/>
                <filter name="invalid" string="Invalid" domain="[('eta_receipt_state','=','invalid')]"/>
                <filter name="rejected" string="Rejected" domain="[('eta_receipt_state','=','rejected')]"/>
            </xpath>
            <xpath expr="//filter[@name='order_month']" position="after">
                <separator/>
                <filter string="ETA Receipt Status" name="eta_receipt_state" context="{'group_by': 'eta_receipt_state'}"/>
            </xpath>
        </field>
    </record>
    <record id="eta_send_eta_recipt" model="ir.actions.server">
        <field name="name">ETA Recipt Send</field>
        <field name="model_id" ref="point_of_sale.model_pos_order"/>
        <field name="binding_model_id" ref="point_of_sale.model_pos_order"/>
        <field name="groups_id" eval="[(4, ref('account.group_account_invoice'))]"/>
        <field name="state">code</field>
        <field name="code">
        if records:
            action = records.action_pos_send_eta()
        </field>
    </record>
    <record id="eta_get_eta_recipt_state" model="ir.actions.server">
        <field name="name">ETA Recipt Get State</field>
        <field name="model_id" ref="point_of_sale.model_pos_order"/>
        <field name="binding_model_id" ref="point_of_sale.model_pos_order"/>
        <field name="groups_id" eval="[(4, ref('account.group_account_invoice'))]"/>
        <field name="state">code</field>
        <field name="code">
        if records:
            action = records.action_pos_get_state()
        </field>
    </record>
    <record id="eta_regenerate_eta_recipt" model="ir.actions.server">
        <field name="name">ETA Recipt Regenerate</field>
        <field name="model_id" ref="point_of_sale.model_pos_order"/>
        <field name="binding_model_id" ref="point_of_sale.model_pos_order"/>
        <field name="groups_id" eval="[(4, ref('account.group_account_invoice'))]"/>
        <field name="state">code</field>
        <field name="code">
        if records:
            action = records.action_pos_regenerate_eta()
        </field>
    </record>
</odoo>