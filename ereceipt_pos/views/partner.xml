<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_partner_form_eta" model="ir.ui.view">
        <field name="name">view.partner.form.inherit.eta</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='street']" position="before">
                <field name="eta_building_number" placeholder="Building Number..." class="o_address_city" attrs="{'readonly': [('type', '=', 'contact'),('parent_id', '!=', False)]}"/>
            </xpath>
            <xpath expr="//notebook" position="inside">
                <page string="ETA">
                    <group>
                        <group>
                            <h3>Address</h3>
                            <field name="eta_country_id" options="{'no_open': True}"/>
                            <field name="eta_governate" placeholder="Giza Governorate"/>
                            <field name="eta_region_city" placeholder="Dokki"/>
                            <field name="eta_street" placeholder="17 Nabil Al Wakad"/>
                            <field name="eta_building_number" placeholder="17"/>
                            <h4 style="color: blue;">Optional Fields</h4>
                            <field name="eta_postalCode"/>
                            <field name="eta_floor"/>
                            <field name="eta_room"/>
                            <field name="eta_landmark"/>
                            <field name="eta_additionalInformation"/>
                        </group>
                        <group>
                            <field name="eta_name"/>
                            <field name="eta_id"/>
                            <field name="eta_type"/>
                        </group>
                    </group>
                </page>
            </xpath>
            <xpath expr="//field[@name='vat']" position="replace">
                <field name="vat" placeholder="e.g. BE0477472701" attrs="{'readonly': [('company_type','=','person')]}"/>
                <field name="eta_passport_national_id" placeholder="e.g. 92017641235002" attrs="{'readonly': [('company_type','=','company')]}"/>
            </xpath>
        </field>
    </record>
</odoo>