# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError

class SaleOrderLinePrice(models.Model):
    _inherit = 'sale.order.line'

    is_sale_manager = fields.Boolean(
                    string = u'Sale Manger',
                    store=False,
                    compute='_compute_field_product_id_sale_price',
                )    

    def _compute_field_product_id_sale_price(self):
        for record in self:
            record.is_sale_manager = False

            if self.env.user.has_group('hide_sale_price.group_manager_sales_price'):
                record.is_sale_manager = True

    @api.onchange('product_id')
    def _onchange_product_id_sale_price(self):
        self.is_sale_manager = False
        if self.env.user.has_group('hide_sale_price.group_manager_sales_price'):
            self.is_sale_manager = True

class InvoiceOrderLinePrice(models.Model):
    _inherit = 'account.move.line'

    is_manager_user = fields.Boolean(
                    string = u'Manager User',
                    store=False,
                    compute='_compute_field_product_id_unit_price',
                )    

    @api.depends('move_id')
    def _compute_field_product_id_unit_price(self):
        for record in self:
            record.is_manager_user = False
            if record.move_id.move_type == 'out_invoice': 
                if self.env.user.has_group('account.group_account_user') or self.env.user.has_group('account.group_account_manager'):
                    record.is_manager_user = True
            else:
                record.is_manager_user = True
    
    @api.onchange('product_id')
    def _onchange_product_id_bill_price(self):
        self.is_manager_user = False
        if self.move_id.move_type == 'out_invoice': 
            if self.env.user.has_group('account.group_account_user') or self.env.user.has_group('account.group_account_manager'):
                self.is_manager_user = True
        else:
            self.is_manager_user = True