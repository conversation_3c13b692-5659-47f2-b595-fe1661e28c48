from odoo import models, fields


class PosConfig(models.Model):
    _name = 'pos.config'
    _inherit = ['pos.config', 'analytic.mixin']

    analytic_distribution = fields.Json(
        'Analytic',
        compute="_compute_analytic_distribution", store=True, copy=True, readonly=False,
        precompute=True
    )
    def _compute_analytic_distribution(self):
        pass     

    # analytic_account_id = fields.Many2one(comodel_name="account.analytic.account", string="Analytic Account")
