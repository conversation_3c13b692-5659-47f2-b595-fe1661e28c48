from odoo import models, fields, api

class SaleOrder(models.Model):
    _inherit = 'sale.order'

    @api.onchange('team_id','order_line')
    def _onchange_team_id(self):
        if self.team_id and self.team_id.analytic_distribution:
            for line in self.order_line:
                line.analytic_distribution = self.team_id.analytic_distribution

    def action_confirm(self):
        for rec in self:
            for line in rec.order_line:
                line.analytic_distribution = rec.team_id.analytic_distribution
        return super(Sale<PERSON>rder, self).action_confirm()
    
class ChooseDeliveryCarrier(models.TransientModel):
    _inherit = 'choose.delivery.carrier'
   
    def button_confirm(self):
        res = super(<PERSON><PERSON><PERSON>eliveryCarrier, self).button_confirm()
        self.order_id._onchange_team_id()
        return res