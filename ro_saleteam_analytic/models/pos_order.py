from odoo import models


class PosOrder(models.Model):
    _inherit = "pos.order"

    def _prepare_invoice_line(self, order_line):
        res = super(<PERSON>s<PERSON>rde<PERSON>, self)._prepare_invoice_line(order_line)
        if order_line.order_id.config_id.analytic_distribution:
            res.update({"analytic_distribution": order_line.order_id.config_id.analytic_distribution})
        return res
