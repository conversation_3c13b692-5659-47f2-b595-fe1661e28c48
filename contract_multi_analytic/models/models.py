# -*- coding: utf-8 -*-

from odoo import models, fields, api

from odoo.addons.hr_payroll_account.models.hr_payroll_account import HrPayslip

class ContactInherited(models.Model):
    _inherit = 'hr.contract'

    analytic_account_ids = fields.Many2many('account.analytic.account', string='Analytic Accounts', domain="['|', ('company_id', '=', False), ('company_id', '=', company_id)]")    

class HrPayslip(models.Model):
    _inherit = 'hr.payslip'

    def _prepare_line_values(self, line, account_id, date, debit, credit):
        res = super(HrPayslip, self)._prepare_line_values(line, account_id, date, debit, credit)
        distr = {}
        for analytic in line.slip_id.contract_id.analytic_account_ids:
            distr[analytic.id] = 100
        res['analytic_distribution'] =  distr if len(distr)>0 else False
        return res
    
def _get_existing_lines(self, line_ids, line, account_id, debit, credit):
    existing_lines = (
        line_id for line_id in line_ids if
        line_id['name'] == line.name
        and line_id['account_id'] == account_id
        and ((line_id['debit'] > 0 and credit <= 0) or (line_id['credit'] > 0 and debit <= 0))
        and (
                (
                    not line_id['analytic_distribution'] and
                    not line.salary_rule_id.analytic_account_id.id and
                    not line.slip_id.contract_id.analytic_account_id.id
                )
                # or line_id['analytic_distribution'] and line.salary_rule_id.analytic_account_id.id in line_id['analytic_distribution']
                or line_id['analytic_distribution'] and all([analytic.id in line_id['analytic_distribution'] for analytic in line.slip_id.contract_id.analytic_account_ids])
            )
    )
    return next(existing_lines, False)

HrPayslip._get_existing_lines = _get_existing_lines