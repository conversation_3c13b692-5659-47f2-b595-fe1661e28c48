import logging

from odoo import models, fields, api,_
from odoo import tools
from datetime import datetime
from num2words import num2words


from odoo.exceptions import UserError, ValidationError

class MrpProduction(models.Model):
    _inherit = 'mrp.production'

    
    @api.depends('company_id', 'bom_id')
    def _compute_picking_type_id(self):
        super(MrpProduction, self)._compute_picking_type_id()
        for rec in self:
            crm = self.env['crm.team'].search([('member_ids','=',self.env.user.id)])
            if len(crm)>0 :
                warehouse_id = self.env['stock.warehouse'].search([('crm_team_id','=',crm[0].id)])
                if len(warehouse_id)>0 :    
                    rec.picking_type_id = warehouse_id[0].manu_type_id
