<?xml version="1.0" encoding="UTF-8"?>
<templates id="ro_pos_sale_return_amount.template" xml:space="preserve">
    <t t-name="ClosePosPopup" t-inherit="point_of_sale.ClosePosPopup" t-inherit-mode="extension" owl="1">
        <xpath expr="//tbody[hasclass('cash-overview')]//tr[@t-if='defaultCashDetails.payment_amount']" position="replace">

            <tr t-if="defaultCashDetails.sale_payment_amount">
                <td>
                    <div class="flex">
                        <div class="cash-sign" t-esc="defaultCashDetails.sale_payment_amount &lt; 0 ? '-' : '+'"/>
                        Payments in <t t-esc="defaultCashDetails.name"/>
                    </div>
                </td>
                <td t-esc="env.pos.format_currency(Math.abs(defaultCashDetails.sale_payment_amount))"/>
            </tr>

            <tr t-if="defaultCashDetails.return_payment_amount">
                <td>
                    <div class="flex">
                        <div class="cash-sign" t-esc="defaultCashDetails.return_payment_amount &lt; 0 ? '-' : '+'"/>
                        Return From <t t-esc="defaultCashDetails.name"/>
                    </div>
                </td>
                <td t-esc="env.pos.format_currency(Math.abs(defaultCashDetails.return_payment_amount))"/>
            </tr>

        </xpath>
    </t>
</templates>