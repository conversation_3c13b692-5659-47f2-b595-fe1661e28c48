# -*- coding: utf-8 -*-

from odoo import models, fields, api, _ 
from odoo.exceptions import ValidationError

class ResPartner(models.Model):
    _inherit = 'res.partner'

    phone = fields.Char( index=True, copy=False)
    # required=True,
    @api.model
    def _name_search(self, name, args=None, operator='ilike', limit=100, name_get_uid=None):
        result = super()._name_search(name=name, args=args, operator=operator, limit=limit, name_get_uid=name_get_uid)
        if not result and name and operator in ('=', 'ilike', '=ilike', 'like', '=like'):
            args = args or []
            args = [('phone', operator, name)] + args
            return self._search(args, limit=limit, access_rights_uid=name_get_uid)
        return result

    @api.constrains('phone')
    def _check_phone_number(self):

        for record in self:
            if record.phone:
                phone = record.phone.replace('+2', '').replace(' ', '')
                
                #for landline also start with governorate code
                if len(phone) not in [11,7,9,10] or not record.phone.isdigit():
                    raise ValidationError(_('please check phone number again.'))

            else:
                raise ValidationError(_('Phone Number is Required.'))
                    
    @api.model
    def create(self, values):
        
        for val in values:
            if values.get('phone'):
                values['phone'] = values['phone'].replace('+2','').replace(' ','')

        result = super().create(values)
    
        return result
    
    def write(self, values):

        for val in values:
            if values.get('phone'):
                values['phone'] = values['phone'].replace('+2','').replace(' ','')
                
        result = super().write(values)
    
        return result
        

    # _sql_constraints = [
    # ('phoneUniq', 'unique(phone)', 'Phone Number already exists!'),
    # ('phone_required_field',
    #         "Check(1=1)",
    #         "Missing required phone number.")
    # ]
