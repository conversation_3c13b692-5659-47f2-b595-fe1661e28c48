# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError

class PurchaseOrder(models.Model):
    _inherit = 'purchase.order'

    edit_lines = fields.Boolean(
                    string = u'Edit Lines',
                    default = False,
                    compute='_compute_edite_lines',
                )   
    @api.depends('partner_id')
    def _compute_edite_lines(self):
        for record in self:
            record.edit_lines = False
            
            if self.env.user.has_group('purchase_order_readonly_line.group_allow_edit_po') :
               record.edit_lines = True
             