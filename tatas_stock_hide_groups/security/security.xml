<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <data noupdate="0">
        <record id="group_stock_adj" model="res.groups">
            <field name="name">Inventory Adjustment Group [Alasdeka]</field>
            <field name="category_id" ref="base.module_category_hidden"/>
        </record>
        
        <record id="group_stock_adj_add_qty" model="res.groups">
            <field name="name">Inventory Adjustment Qty Group [Alasdeka]</field>
            <field name="category_id" ref="base.module_category_hidden"/>
        </record>

        <record id="group_stock_scrap" model="res.groups">
            <field name="name">Scrap Group [Alasdeka]</field>
            <field name="category_id" ref="base.module_category_hidden"/>
        </record>

        <record id="group_stock_scrap_validate" model="res.groups">
            <field name="name">Scrap Group Validate[Alasdeka]</field>
            <field name="category_id" ref="base.module_category_hidden"/>
        </record>
    </data>
</odoo>
