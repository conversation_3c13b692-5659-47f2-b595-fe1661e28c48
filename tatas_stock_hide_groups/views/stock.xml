<odoo>
    <data>

        <record model="ir.ui.menu" id="stock.menu_action_inventory_tree">
            <field name="groups_id" eval="[(4, ref('tatas_stock_hide_groups.group_stock_adj')),(4, ref('tatas_stock_hide_groups.group_stock_adj_add_qty'))]"/>
        </record>
        <record model="ir.ui.menu" id="stock.menu_stock_scrap">
            <field name="groups_id" eval="[(4, ref('tatas_stock_hide_groups.group_stock_scrap')),(4, ref('tatas_stock_hide_groups.group_stock_scrap_validate'))]"/>
        </record>
        <record id="stock_scrap_form_view_validate_button_inherit" model="ir.ui.view">
            <field name="model">stock.scrap</field>
            <field name="inherit_id" ref="stock.stock_scrap_form_view"/>
            <field name="arch" type="xml">
                <xpath expr="//button[@name='action_validate']" position="attributes">
                    <attribute name="groups">tatas_stock_hide_groups.group_stock_scrap_validate</attribute>
                </xpath>
            </field>
        </record>
        <record id="stock_scrap_form_wizard_validate_button_inherit" model="ir.ui.view">
            <field name="model">stock.scrap</field>
            <field name="inherit_id" ref="stock.stock_scrap_form_view2"/>
            <field name="arch" type="xml">
                <xpath expr="//button[@name='action_validate']" position="replace">
                    <button string="Confirm" class="btn-primary" special="save" data-hotkey="v"/>
                </xpath>
            </field>
        </record>

        <record id="product_form_view_procurement_button_inherit" model="ir.ui.view">
            <field name="model">product.product</field>
            <field name="inherit_id" ref="stock.product_form_view_procurement_button"/>
            <field name="arch" type="xml">
                <xpath expr="//button[@name='action_update_quantity_on_hand']" position="attributes">
                    <attribute name="groups">tatas_stock_hide_groups.group_stock_adj,tatas_stock_hide_groups.group_stock_adj_add_qty</attribute>
                </xpath>
            </field>
        </record>

        <record id="product_product_view_form_easy_inherit_stock_inherit" model="ir.ui.view">
            <field name="model">product.product</field>
            <field name="inherit_id" ref="stock.product_product_view_form_easy_inherit_stock"/>
            <field name="arch" type="xml">
                <xpath expr="//button[@name='action_update_quantity_on_hand']" position="attributes">
                    <attribute name="groups">tatas_stock_hide_groups.group_stock_adj,tatas_stock_hide_groups.group_stock_adj_add_qty</attribute>
                </xpath>
            </field>
        </record>


        <record id="product_template_form_view_procurement_button_inherit" model="ir.ui.view">
            <field name="model">product.template</field>
            <field name="inherit_id" ref="stock.product_template_form_view_procurement_button"/>
            <field name="arch" type="xml">
                <xpath expr="//button[@name='action_update_quantity_on_hand']" position="attributes">
                    <attribute name="groups">tatas_stock_hide_groups.group_stock_adj,tatas_stock_hide_groups.group_stock_adj_add_qty</attribute>
                </xpath>
            </field>
        </record>


        <record id="stock_quant_form_view_tree_editable_inventory_inherit" model="ir.ui.view">
            <field name="model">stock.quant</field>
            <field name="inherit_id" ref="stock.view_stock_quant_tree_editable"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='inventory_quantity_auto_apply']" position="attributes">
                    <attribute name="groups">tatas_stock_hide_groups.group_stock_adj,tatas_stock_hide_groups.group_stock_adj_add_qty</attribute>
                </xpath>
                <xpath expr="//button[@name='%(stock.action_view_inventory_tree)d']" position="attributes">
                    <attribute name="groups">tatas_stock_hide_groups.group_stock_adj</attribute>
                </xpath>
            </field>
        </record>

        <record id="stock_inventory_adjustment_popup_editable_inventory_inherit" model="ir.ui.view">
            <field name="model">stock.inventory.adjustment.name</field>
            <field name="inherit_id" ref="stock.stock_inventory_adjustment_name_form_view"/>
            <field name="arch" type="xml">
                <xpath expr="//div" position="attributes">
                    <attribute name="groups">tatas_stock_hide_groups.group_stock_adj</attribute>
                </xpath>
                <xpath expr="//footer" position="attributes">
                    <attribute name="groups">tatas_stock_hide_groups.group_stock_adj</attribute>
                </xpath>
            </field>
        </record>

        <record id="stock_quant_form_view_tree_inventory_inherit" model="ir.ui.view">
            <field name="model">stock.quant</field>
            <field name="inherit_id" ref="stock.view_stock_quant_tree_inventory_editable"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='inventory_quantity']" position="attributes">
                    <attribute name="groups">tatas_stock_hide_groups.group_stock_adj,tatas_stock_hide_groups.group_stock_adj_add_qty</attribute>
                </xpath> 

                <xpath expr="//button[@name='stock.action_stock_inventory_adjustement_name']" position="attributes">
                    <attribute name="groups">tatas_stock_hide_groups.group_stock_adj</attribute>
                </xpath>
                <xpath expr="//button[@name='action_reset']" position="attributes">
                    <attribute name="groups">tatas_stock_hide_groups.group_stock_adj</attribute>
                </xpath>
                <xpath expr="//button[@name='stock.action_stock_request_count']" position="attributes">
                    <attribute name="groups">tatas_stock_hide_groups.group_stock_adj</attribute>
                </xpath>

                <xpath expr="//button[@name='action_apply_inventory']" position="attributes">
                    <attribute name="groups">tatas_stock_hide_groups.group_stock_adj</attribute>
                </xpath>
                <xpath expr="//button[@name='action_set_inventory_quantity']" position="attributes">
                    <attribute name="groups">tatas_stock_hide_groups.group_stock_adj</attribute>
                </xpath>
                <xpath expr="//button[@name='action_set_inventory_quantity_to_zero']" position="attributes">
                    <attribute name="groups">tatas_stock_hide_groups.group_stock_adj</attribute>
                </xpath>
            </field>
        </record>


    </data>
</odoo>