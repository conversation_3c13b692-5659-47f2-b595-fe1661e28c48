# -*- coding: utf-8 -*-

from odoo import models, fields, api, _, exceptions
from odoo.exceptions import UserError
from datetime import datetime, time



class WizardProductMoves(models.TransientModel):
    _name = 'wizard.product.stock.moves'
    _description = 'wizard Product Stock report'
  
    product_ids = fields.Many2many('product.product')

    # location_ids = fields.Many2many('stock.location',domain="[('usage', '=', 'internal')]")

    date_from = fields.Datetime(required=True)


    def action_get_data(self):

        view_id = self.env.ref('product_stock_move_qty_date_filter.stock_move_location_filter_view_tree').id
        domain = []

        if self.product_ids:
            domain.append(('product_id','in',self.product_ids.ids))
        # if self.location_ids:
        #     domain.append(('location_id','in',self.location_ids.ids))
        # if self.date_from:
            # domain.append(('date','>=',datetime.combine(self.date_from, time.min)))
        if len(domain) > 0 or self.date_from:
            self.env['stock.move.location.filter'].with_context({'date_filter':self.date_from}).init()
            return {
                    'name': _('Product Moves'),
                    'view_mode': 'tree',
                    'res_model': 'stock.move.location.filter',
                    'domain': domain,
                    # 'warehouse_id',
                    'context': {'date_filter':self.date_from, 'group_by':['product_id']},
                    # ,'warehouse_id'
                    'view_id': view_id,
                    'type': 'ir.actions.act_window'
                }
        else:
            return {
                    'name': _('Product Moves'),
                    'view_mode': 'tree',
                    'res_model': 'stock.move.location.filter',
                    'context': {'group_by':['product_id']},
                    'view_id': view_id,
                    'type': 'ir.actions.act_window'
                }
