<?xml version='1.0' encoding='utf-8'?>
<odoo>

    <function name = "write" model = "ir.model.data">
        <function name = "search" model = "ir.model.data">
            <value eval = "[('module', '=', 'sale'), ('name','=','account_invoice_rule_see_personal')] "/>
        </function>
        <value eval =" {'noupdate': False} "/>
    </function>
    <record id="sale.account_invoice_rule_see_personal" model="ir.rule">
        <field name="domain_force">[('move_type', 'in', ('out_invoice', 'out_refund')), ('invoice_user_id', '=', user.id)]</field>
    </record>

</odoo>


