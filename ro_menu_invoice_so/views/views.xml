<odoo>
  <data>
    <record id="inherit_account_invoice_tree" model="ir.ui.view">
      <field name="model">account.move</field>
      <field name="inherit_id" ref="account.view_invoice_tree"/>
      <field name="mode">primary</field>
      <field name="arch" type="xml">

        <xpath expr="//field[@name='state']" position="after">
          <field name="delivery_employee_id" string="Delivery Employee" />

        </xpath>


      </field>
    </record>
    

    <record id="ro_invoice_not_paid_action" model="ir.actions.act_window">
      <field name="name">Invoices Not Paid</field>
      <field name="type">ir.actions.act_window</field>
      <field name="res_model">account.move</field>
      <field name="view_mode">tree,form</field>
      <field name="view_id" ref="inherit_account_invoice_tree"/>
      <field name="domain">[('payment_state', '!=', 'paid'),('move_type','in',('out_invoice','out_refund')),('invoice_user_id','=',uid)]</field>
      <field name="context">{'create': False}</field>
    </record>

    <menuitem action="ro_invoice_not_paid_action" id="ro_invoice_not_paid" parent="sale.sale_menu_root" sequence="20"/>



  </data>
</odoo>