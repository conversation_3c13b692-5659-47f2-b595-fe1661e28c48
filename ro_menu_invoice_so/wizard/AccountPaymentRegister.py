from odoo import models, fields, api

class AccountPaymentRegister(models.TransientModel):
    _inherit = 'account.payment.register'

    #invoice_ids = fields.Many2many('account.move', string='Invoices')
    total_balance = fields.Monetary(string='Total Balance', readonly=True)

    def _compute_total_balance(self):
        
        for payment in self:
            payment.total_balance = sum(payment.invoice_ids.mapped('amount_residual'))

    @api.model
    def default_get(self, fields_list):
        # OVERRIDE
        res = super().default_get(fields_list)
        
        if self._context.get('active_model') == 'account.move':
            moves = self.env['account.move'].browse(self._context.get('active_ids', []))
            res['total_balance'] = sum(moves.mapped('amount_residual'))
        
        return res