<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="view_sale_payment_register_form" model="ir.ui.view">
            <field name="name">sale.payment.register.form</field>
            <field name="model">sale.payment.register</field>
            <field name="arch" type="xml">
                <form string="Register Payment">
                    <group>
                        <group name="group1">
                            <field name="company_id" invisible="1"/>
                            <field name="journal_id" options="{'no_open': True, 'no_create': True}" required="1"/>
                        </group>
                        <group name="group2">
                            <label for="amount"/>
                            <div name="amount_div" class="o_row">
                                <field name="amount"/>
                                <field name="currency_id"
                                       options="{'no_create': True, 'no_open': True}"
                                       groups="base.group_multi_currency"/>
                            </div>
                            <field name="amount_due"/>
                            <field name="amount_diff"/>
                            <field name="payment_date"/>
                        </group>
                    </group>
                    <footer>
                        <button string="Create Payment" name="action_create_payments" type="object" class="oe_highlight" data-hotkey="q"/>
                        <button string="Cancel" class="btn btn-secondary" special="cancel" data-hotkey="z"/>
                    </footer>
                </form>
            </field>
        </record>
    </data>
</odoo>