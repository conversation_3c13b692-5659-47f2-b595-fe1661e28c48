<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="view_order_form_register_payment" model="ir.ui.view">
            <field name="name">view.order.form.register.payment</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale.view_order_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='state']" position="before">
                    <field name="payment_state" invisible="1"/>
                    <button name="action_register_payment"
                                type="object" class="oe_highlight"
                                attrs="{'invisible': ['|', ('state', 'not in', ('sale','done')), ('payment_state', 'not in', ('not_paid', 'partial'))]}"
                                string="Register Payment" data-hotkey="r"
                                groups="sales_team.group_sale_salesman"/>
                </xpath>
                <div name="button_box" position="inside">
                    <button class="oe_stat_button" type="object" name="action_sale_open_payments" context="{'default_sale_order_id': active_id}" icon="fa-money">
                        <div class="o_stat_info">
                            <field name="payment_count" class="o_stat_value"/>
                            <span class="o_stat_text">Payments</span>
                        </div>
                    </button>
                </div>
                <div name="button_box" position="after">
                    <widget name="web_ribbon" title="Paid" attrs="{'invisible': [('payment_state', '!=', 'paid')]}"/>
                    <widget name="web_ribbon" title="Partial" attrs="{'invisible': [('payment_state', '!=', 'partial')]}"/>
                </div>
                <xpath expr="//group[@name='sale_total']" position="inside">
                    <field name="amount_paid_text" colspan="2" nolabel="1"/>
                    <div class="oe_subtotal_footer_separator oe_inline o_td_label">
                        <label for="amount_paid"/>
                    </div>
                    <field name="amount_paid" nolabel="1" class="oe_subtotal_footer_separator" widget='monetary'
                            options="{'currency_field': 'currency_id'}"/>
                    <div class="oe_subtotal_footer_separator oe_inline o_td_label">
                        <label for="amount_due"/>
                    </div>
                    <field name="amount_due" nolabel="1" class="oe_subtotal_footer_separator" widget='monetary'
                            options="{'currency_field': 'currency_id'}"/>
                </xpath>
            </field>
        </record>
        <record id="view_order_tree_paid" model="ir.ui.view">
            <field name="name">view.order.tree.paid</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale.view_order_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='amount_total']" position="after">
                    <field name="amount_paid" sum="Total Paid" widget="monetary" decoration-bf="1" optional="show"/>
                    <field name="payment_state" decoration-success="payment_state == 'paid'" decoration-info="payment_state == 'partial'" widget="badge" optional="show"/>
                </xpath>
            </field>
        </record>
        <record id="view_quotation_tree_paid" model="ir.ui.view">
            <field name="name">view.quotation.tree.paid</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale.view_quotation_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='amount_total']" position="after">
                    <field name="amount_paid" sum="Total Paid" widget="monetary" decoration-bf="1" optional="show"/>
                    <field name="payment_state" decoration-success="payment_state == 'paid'" decoration-info="payment_state == 'partial'" widget="badge" optional="show"/>
                </xpath>
            </field>
        </record>
    </data>
</odoo>