<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="hr_public_holiday_view_form" model="ir.ui.view">
        <field name="name">hr.public.holiday.view.form</field>
        <field name="model">hr.public.holiday</field>
        <field name="arch" type="xml">
            <form string="Public Holidays">
                <header>
                    <field name="state" widget="statusbar" clickable="1"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <label for="name" class="oe_edit_only"/>
                        <h1>
                            <field name="name"
                                   placeholder="Holiday Reason"/>
                        </h1>
                    </div>
                    <group>
                        <label for="date_from" string="Period"/>
                        <div>
                            <field name="date_from" class="oe_inline"/>
                            -
                            <field name="date_to" class="oe_inline"/>
                        </div>
                    </group>
                    <group>
                        <label for="type_select" string="Apply On"/>
                        <field name="type_select" widget="radio"
                               nolabel="1"/>
                        <field name="emp_ids" widget="many2many_tags"
                               attrs="{'invisible': [('type_select','!=','emp')]}"/>
                        <field name="dep_ids" widget="many2many_tags"
                               attrs="{'invisible': [('type_select','!=','dep')]}"/>
                        <field name="cat_ids" widget="many2many_tags"
                               attrs="{'invisible': [('type_select','!=','tag')]}"/>
                    </group>
                    <label for="note"/>
                    <field name="note" placeholder="Notes"/>
                </sheet>
                 <div class="oe_chatter">
                <field name="message_follower_ids" widget="mail_followers"/>
                <field name="message_ids" widget="mail_thread"/>
            </div>
            </form>

        </field>
    </record>


    <record id="hr_public_holiday_view_tree" model="ir.ui.view">
        <field name="name">hr.public.holiday.view.tree</field>
        <field name="model">hr.public.holiday</field>
        <field name="arch" type="xml">
            <tree string="Public Holidays">
                <field name="name"/>
                <field name="date_from"/>
                <field name="date_to"/>
            </tree>
        </field>
    </record>


    <record id="action_hr_public_holiday" model="ir.actions.act_window">
        <field name="name">Public Holidays</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">hr.public.holiday</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new public holiday
            </p>
        </field>
    </record>


    <menuitem
            id="menu_hr_public_holiday"
            name="Public Holidays"
            parent="hr_holidays.menu_hr_holidays_root"
            sequence="4"
            groups="hr_holidays.group_hr_holidays_manager,hr_holidays.group_hr_holidays_user"/>

    <menuitem id="menu_action_public_holiday" name="Public Holidays"
              parent="menu_hr_public_holiday"
              action="action_hr_public_holiday"
              sequence="96"/>


</odoo>