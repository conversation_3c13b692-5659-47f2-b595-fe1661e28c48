<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record model="ir.module.category"
                id="module_category_human_resources_attendances_sheet">
            <field name="name">Attendance Sheet</field>
            <field name="description">Helps you manage Attendance Sheets.
            </field>
            <field name="sequence">15</field>
        </record>


        <record id="group_attendance_sheet_user" model="res.groups">
            <field name="name">User</field>
            <field name="implied_ids" eval="[(4, ref('hr.group_hr_user'))]"/>
            <field name="category_id"
                   ref="module_category_human_resources_attendances_sheet"/>
        </record>


        <record id="group_attendance_sheet_manager" model="res.groups">
            <field name="name">Manager</field>
            <field name="category_id"
                   ref="module_category_human_resources_attendances_sheet"/>
            <field name="implied_ids"
                   eval="[(4, ref('group_attendance_sheet_user'))]"/>

        </record>


        <record id="base.user_admin" model="res.users">
            <field name="groups_id"
                   eval="[(4, ref('rm_hr_attendance_sheet.group_attendance_sheet_manager'))]"/>
        </record>


    </data>
</odoo>
