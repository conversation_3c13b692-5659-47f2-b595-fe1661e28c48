odoo.define('ro_pos_employee_access.RefundPermission', function (require) {
    'use strict';
    const TicketScreen = require('point_of_sale.TicketScreen');
    const Registries = require('point_of_sale.Registries');

    const PosResTicketScreen = (TicketScreen) =>
        class extends TicketScreen {
            async _onDoRefund() {
                const employee = this.env.pos.get_cashier(); 

                if (!employee.ro_access_refund) {
                    this.showPopup('ErrorPopup', {
                        title: this.env._t('Access Denied'),
                        body: this.env._t('You do not have permission to perform refunds.'),
                    });
                    return; 
                }

                super._onDoRefund();
            }
        };

    Registries.Component.extend(TicketScreen, PosResTicketScreen);
});
