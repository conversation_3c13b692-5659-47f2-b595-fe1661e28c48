odoo.define('ro_pos_employee_access.models', function (require) {
    'use strict';

    var { PosGlobalState } = require('point_of_sale.models');
    const Registries = require('point_of_sale.Registries');

    const PosHrPosGlobalStateAccess = (PosGlobalState) => class PosHrPosGlobalState extends PosGlobalState {
        /**
         * @override
         */
        reset_cashier() {
            this.cashier = {name: null, id: null, barcode: null, user_id: null, pin: null, role: null, ro_close_session: null, ro_cash_in_out: null, ro_access_refund: null};
        }
    }

    Registries.Model.extend(PosGlobalState, PosHrPosGlobalStateAccess);

});
