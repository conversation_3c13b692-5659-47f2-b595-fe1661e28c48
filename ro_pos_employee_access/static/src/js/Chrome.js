odoo.define('ro_pos_employee_access.chrome', function (require) {
    'use strict';

    const Chrome = require('point_of_sale.Chrome');
    const Registries = require('point_of_sale.Registries');

    const PosResChrome = (Chrome) =>
        class extends Chrome {
            /**
             * @override
             */
            get headerButtonIsShown() {
                //close session button
                return !this.env.pos.config.module_pos_hr || this.env.pos.get_cashier().ro_close_session;
            }
            /**
             * @override
             */
            showCashMoveButton() {
                //cash in/out button
                return !this.env.pos.cashier || this.env.pos.cashier.ro_cash_in_out;
            }
        };

    Registries.Component.extend(Chrome, PosResChrome);

    return Chrome;
});
