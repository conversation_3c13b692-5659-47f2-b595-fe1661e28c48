# -*- coding: utf-8 -*-

from odoo.addons.pos_hr.models.pos_session import PosSession

def _loader_params_hr_employee(self):
    if len(self.config_id.employee_ids) > 0:
        domain = ['&', ('company_id', '=', self.config_id.company_id.id), '|', ('user_id', '=', self.user_id.id), ('id', 'in', self.config_id.employee_ids.ids)]
    else:
        domain = [('company_id', '=', self.config_id.company_id.id)]
    return {'search_params': {'domain': domain, 'fields': ['name', 'id', 'user_id', 'ro_close_session', 'ro_cash_in_out', 'ro_access_refund'], 'load': False}}


PosSession._loader_params_hr_employee = _loader_params_hr_employee
