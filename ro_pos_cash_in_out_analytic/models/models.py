# -*- coding: utf-8 -*-
from odoo import models,fields,api

class AccountBankStatementLine(models.Model):
    _inherit = 'account.bank.statement.line'


    def _prepare_move_line_default_vals(self, counterpart_account_id=None):
        res = super(AccountBankStatementLine, self)._prepare_move_line_default_vals(counterpart_account_id)

        if self.pos_session_id.config_id:
            # liquidity_line_vals
            res[0]['analytic_distribution'] = self.pos_session_id.config_id.analytic_distribution
            # counterpart line values
            res[1]['analytic_distribution'] = self.pos_session_id.config_id.analytic_distribution
        
        return res