<odoo>
    <data>
        <!-- Billing Access Rights -->
        <record id="ro_pos_rule_user_allowed_pm" model="ir.rule">
            <field name="name">User POS Alowed Method</field>
            <field name="model_id" ref="point_of_sale.model_pos_payment_method" />
            <field name="domain_force">[('journal_id', 'in', user.journal_ids.ids)]</field>
            <field name="groups" eval='[(4, ref("account.group_account_invoice"))]' />
        </record>


        <!-- Advisor Access Rights -->
        <record id="ro_pos_rule_admin_allowed_pm" model="ir.rule">
            <field name="name">Admin POS Alowed Method</field>
            <field name="model_id" ref="point_of_sale.model_pos_payment_method" />
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval='[(4, ref("account.group_account_manager"))]' />
        </record>
    </data>


</odoo>