<odoo>
    <data>


        <record id="inventory_report_view_inherited" model="ir.ui.view">
            <field name="name">view.inventory_report.inherit</field>
            <field name="model">stock.quant</field>
            <field name="inherit_id" ref="stock.view_stock_quant_tree_editable"/>
            <field name="mode">primary</field>
            <field name="arch" type="xml">

              <xpath expr="//tree" position="attributes">
                <!-- <attribute name="create">0</attribute> -->
                <!-- <attribute name="edit">0</attribute> -->
                <attribute name="js_class">0</attribute>
              </xpath>
              <xpath expr="//field[@name='location_id']" position="attributes">
                <attribute name="readonly">1</attribute>
                <attribute name="force_save">1</attribute>
              </xpath>  
              <xpath expr="//field[@name='product_id']" position="attributes">
                <attribute name="readonly">1</attribute>
                <attribute name="force_save">1</attribute>
              </xpath>  
              <xpath expr="//field[@name='inventory_quantity_auto_apply']" position="attributes">
                <attribute name="readonly">1</attribute>
                <attribute name="force_save">1</attribute>
              </xpath>              
              <xpath expr="//field[@name='inventory_quantity_auto_apply']" position="after">
                <field name="inventory_quantity" optional="show"/>
                <field name="available_quantity" />
              </xpath>
              
            </field>
        </record>

        <record id="action_inventory_report_view" model="ir.actions.act_window">
            <field name="name">Branch Inventory Report</field>
            <field name="res_model">stock.quant</field>
            <field name="view_mode">tree</field>
            <field name="context">{'group_by':['location_id']}</field>
            <field name="domain">[('location_id.warehouse_id.crm_team_id.member_ids','=',uid),('on_hand','=',True)]</field>
            <field name="view_id" ref="inventory_report_view_inherited"/>
            <field name="search_view_id" ref="stock.quant_search_view"/>
        </record>


        <record model="ir.actions.server" id="action_inventory_report_view_ir_srver">
            <field name="name">Branch Inventory Report</field>
            <field name="model_id" ref="stock.model_stock_quant"/>
            <field name="state">code</field>
            <field name="code">action = model.action_inventory_report_domain()</field>
        </record>
 
        <menuitem id="menu_action_inventory_report" parent="whole_transfer.whole_transfer_menu_root"
            action="action_inventory_report_view_ir_srver" sequence="165"/>

    </data>
</odoo>
