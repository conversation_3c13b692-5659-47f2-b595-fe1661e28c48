from odoo import fields, models, api


class PosConfig(models.Model):
    _inherit = "pos.config"

    default_partner_id = fields.Many2one(
        comodel_name="res.partner", string="Default Customer", required=False
    )


    def get_limited_partners_loading(self):
        self.env.cr.execute("""
            WITH pm AS
            (
                     SELECT   partner_id,
                              Count(partner_id) order_count
                     FROM     pos_order
                     GROUP BY partner_id)
            SELECT    id
            FROM      res_partner AS partner
            LEFT JOIN pm
            ON        (
                                partner.id = pm.partner_id)
            WHERE (
                partner.company_id=%s OR partner.company_id IS NULL
            )
            ORDER BY  COALESCE(pm.order_count, 0) DESC,
                      NAME limit %s;
        """, [self.company_id.id, str(self.limited_partners_amount)])
        result = self.env.cr.fetchall()
        # if result:
        #     result = result + [(self.default_partner_id.id,)]
        # return result
        partner_ids = [row[0] for row in result]

        # Add the default partner ID if it exists
        if self.default_partner_id:
            partner_ids.append(self.default_partner_id.id)

        # Convert to a list of tuples as the original result format
        return [(partner_id,) for partner_id in partner_ids]

