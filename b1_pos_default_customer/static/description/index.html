<head>
  <meta name="description" content="POS Default Customer" />
  <meta
    name="keywords"
    content="odoo, odooapps, pos, customer, default customer"
  />
  <meta name="author" content="Brent137" />
</head>
<section class="oe_container lead">
  <div class="oe_row oe_spaced">
    <div class="oe_span12">
      <h2 class="oe_slogan">POS Default Customer</h2>
      <h3 class="oe_slogan">Set Default Customer in POS</h3>
    </div>
  </div>
</section>

<section class="oe_container lead">
  <div class="alert text-center mt32">
    <b>1. Set the default customer on POS Config on the Bills & Receipts section </b>
  </div>
  <a href="pos_config_screenshot.png">
    <img
      class="oe_picture oe_screenshot"
      src="pos_config_screenshot.png"
      style="width: 100%; margin: auto"
    />
  </a>
  <div class="alert text-center mt32">
    <b>2. The customer will be selected automatically when the POS starts or a new order is created</b>
  </div>
  <a href="pos_screenshot.png">
    <img
      class="oe_picture oe_screenshot"
      src="pos_screenshot.png"
      style="width: 100%; margin: auto"
    />
  </a>
</section>

<section class="oe_container lead">
  <div class="alert text-center mt32">
    <a
      rel="nofollow"
      href="https://www.github.com/brent137/odoo"
      target="_blank"
    >
      <i class="fa fa-globe"></i> Github
    </a>
  </div>
</section>
