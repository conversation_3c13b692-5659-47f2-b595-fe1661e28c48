odoo.define("b1_pos_default_customer.models", function (require) {
  "use strict";

  const { PosGlobalState } = require("point_of_sale.models");
  const Registries = require("point_of_sale.Registries");

  const B1PosGlobalState = (PosGlobalState) =>
    class extends PosGlobalState {
      //@override
      createReactiveOrder(json) {
        let reactiveOrder = super.createReactiveOrder(...arguments);
        if (reactiveOrder.partner == null && this.config.default_partner_id && (!json || !json.refund) ) {
          let partner = this.db.get_partner_by_id(
            this.config.default_partner_id[0]
          );
          if (partner) reactiveOrder.set_partner(partner);
        }
        return reactiveOrder;
      }
    };

  Registries.Model.extend(PosGlobalState, B1PosGlobalState);
});
