# -*- coding: utf-8 -*-

from odoo import api, models, fields, _
from odoo.exceptions import UserError
from num2words import num2words
import json
from datetime import datetime
from odoo.addons.account.models.account_move import AccountMove
from odoo.addons.account.wizard.account_payment_register import AccountPaymentRegister

class ResUsers(models.Model):
    _inherit="res.users"

    journal_ids = fields.Many2many('account.journal')

class AccountMove(models.Model):
    _inherit="account.move"

    allowed_journal_ids = fields.Many2many('account.journal', compute='_compute_allowed_journal_ids', precompute=True)

    @api.depends('company_id')
    def _compute_allowed_journal_ids(self):
        for m in self:
            domain = [('id', 'in', self.env.user.journal_ids.ids)]
            m.allowed_journal_ids = self.env['account.journal'].search(domain)



@api.depends('move_type')
def _compute_journal_id(self):
    for record in self:
        record.journal_id = record.with_user(self.env.user.id)._search_default_journal()
    self._conditional_add_to_compute('company_id', lambda m: (
        not m.company_id
        or m.company_id != m.journal_id.company_id
    ))
    self._conditional_add_to_compute('currency_id', lambda m: (
        not m.currency_id
        or m.journal_id.currency_id and m.currency_id != m.journal_id.currency_id
    ))
AccountMove._compute_journal_id = _compute_journal_id



@api.depends('available_journal_ids')
def _compute_journal_id(self):
    for wizard in self:
        if wizard.can_edit_wizard:
            batch = wizard._get_batches()[0]
            wizard.journal_id = wizard.with_user(self.env.user.id)._get_batch_journal(batch)
        else:
            wizard.journal_id = self.env['account.journal'].with_user(self.env.user.id).search([
                ('type', 'in', ('bank', 'cash')),
                ('company_id', '=', wizard.company_id.id),
                ('id', 'in', self.available_journal_ids.ids)
            ], limit=1)

AccountPaymentRegister._compute_journal_id = _compute_journal_id
