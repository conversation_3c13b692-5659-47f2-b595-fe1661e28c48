<odoo>
    <!-- Billing Access Rights -->
    <record id="rule_user_allowed_journals" model="ir.rule">
        <field name="name">account allowed journals</field>
        <field name="model_id" ref="account.model_account_journal"/>
        <field name="domain_force">[('id', 'in', user.journal_ids.ids)]</field>
        <field name="groups" eval='[(4, ref("account.group_account_invoice"))]' />
    </record>

    <record id="rule_user_allowed_moves" model="ir.rule">
        <field name="name">account allowed moves</field>
        <field name="model_id" ref="account.model_account_move"/>
        <field name="domain_force">[('journal_id', 'in', user.journal_ids.ids)]</field>
        <field name="groups" eval='[(4, ref("account.group_account_invoice"))]' />
    </record>

    <record id="rule_user_allowed_journals_payment" model="ir.rule">
        <field name="name">account allowed journals for accountant</field>
        <field name="model_id" ref="account.model_account_payment"/>
        <field name="domain_force">[('journal_id', 'in', user.journal_ids.ids)]</field>
        <field name="groups" eval='[(4, ref("account.group_account_invoice"))]' />
    </record>


    <!-- Advisor Access Rights -->
    <record id="rule_user_allowed_journals_admin" model="ir.rule">
        <field name="name">account allowed journals Admin</field>
        <field name="model_id" ref="account.model_account_journal"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval='[(4, ref("account.group_account_manager"))]' />
    </record>

    <record id="rule_user_allowed_moves_admin" model="ir.rule">
        <field name="name">account allowed moves Admin</field>
        <field name="model_id" ref="account.model_account_move"/>
        <field name="domain_force">[(1, '=', 1)]</field>
        <field name="groups" eval='[(4, ref("account.group_account_manager"))]' />
    </record>

    <record id="rule_user_allowed_journals_for_admin_payment" model="ir.rule">
        <field name="name">account allowed journals for admin</field>
        <field name="model_id" ref="account.model_account_payment"/>
        <field name="domain_force">[(1,'=',1)]</field>
        <field name="groups" eval='[(4, ref("account.group_account_manager"))]' />
    </record>


    

    
</odoo>