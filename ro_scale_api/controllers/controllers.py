import json

from odoo import http, SUPERUSER_ID
from odoo.http import request


class ETAController(http.Controller):

    @http.route('/getproducts/<int:company_id>', type='http', auth="api_key", methods=['GET'])
    def getdocs(self, company_id=1, **kwargs):

        kg_uom =  request.env.ref('uom.product_uom_kgm')
        domain = [('default_code', '=like', '99%'), ('uom_id', '=', kg_uom.id),'|',('company_id', '=', company_id),('company_id', '=', False)]

        product_ids = request.env['product.template'].search(domain)

        products = []
        for product in product_ids:
            try:
                products.append({"Code":int(product.default_code[2:]),"ItemCode":product.default_code,"Name":product.name,"Price":product.list_price,"Type":"KGM"})
            except:
                pass

        data = json.dumps(products, ensure_ascii=False).encode('utf8')

        return data