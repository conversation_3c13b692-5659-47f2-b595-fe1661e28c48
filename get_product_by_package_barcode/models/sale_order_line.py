from odoo import models,fields,api


class SaleOrderLine(models.Model):
    
    _inherit = "sale.order.line"
    
    
    product_packaging_id = fields.Many2one(
        comodel_name='product.packaging',
        string="Packaging",
        compute='_compute_product_packaging_id',
        store=True, readonly=False, precompute=True,
        # inverse='_inverse_product_packaging_id',
        check_company=True)

    @api.onchange('product_packaging_id')
    def _inverse_product_packaging_id(self):
        for line in self:
            print("in the inverse function")
            if not line.product_id:
                line.product_id = self.product_packaging_id.product_id.id
    
    @api.onchange('product_id')
    def _get_packaging_domain(self):
        product =  self.product_id
        # print('-'*20)
        # print(product)
        
        if product:
            return {'domain':{'product_packaging_id':[('sales', '=', True), ('product_id','=',product.id)]}}
        else:
            return {'domain':{'product_packaging_id':[('sales','=',True)]}}
        
    # @api.onchange('product_packaging_id')
    # def _onchange_packaging(self):
    #     if self.product_packaging_id:
    #         if not self.product_id:
    #             packaging = self.env['product.packaging'].search([('barcode','=',self.product_packaging_id)])
    #             if packaging:
    #                 self.product_id = packaging.product_id.id
    #                 self.product_packaging_id = packaging.id
        
    