from odoo import models,fields,api


class PurchaseOrderLine(models.Model):
    _inherit = "purchase.order.line"
    
    
    product_packaging_id = fields.Many2one(
        comodel_name='product.packaging',
        string="Packaging",
        compute='_compute_product_packaging_id',
        store=True, readonly=False, precompute=True,
        # inverse='_inverse_product_packaging_id',
        check_company=True)

    @api.onchange('product_packaging_id')
    def _inverse_product_packaging_id(self):
        for line in self:
            print("in the inverse function")
            if not line.product_id:
                line.product_id = line.product_packaging_id.product_id.id
                line.product_uom = line.product_id.uom_id.id
    
    @api.onchange('product_id')
    def _get_packaging_domain(self):
        product =  self.product_id
        
        if product:
            return {'domain':{'product_packaging_id':[('purchase', '=', True), ('product_id','=',product.id)]}}
        else:
            return {'domain':{'product_packaging_id':[('purchase','=',True)]}}
        