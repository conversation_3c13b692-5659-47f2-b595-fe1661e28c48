from odoo import models, fields, api


class ProductPackaging(models.Model):
    _inherit = "product.packaging"
    # def name_search(self, name='', args=None, operator='ilike', limit=100):
    #     return super().name_search(name, args, operator, limit)
    
    @api.model
    def name_search(self, name, args=None, operator='ilike', limit=100):
        res = self.env['product.packaging'].search(['|',('name',operator,name),('barcode',operator,name)])
        return res.name_get()