# -*- coding: utf-8 -*-

from odoo import models, fields

class BoxWeight(models.Model):
    _name = 'product.box'
    
    ro_main_product_id = fields.Many2one('product.template', string='Main Product')
    ro_product_id = fields.Many2one('product.product', string='Product')
    ro_product_weight = fields.Float(related="ro_product_id.weight", readonly=False)
    ro_range_from = fields.Float('Range From', digits='Stock Weight')
    ro_range_to = fields.Float('Range To', digits='Stock Weight')

    