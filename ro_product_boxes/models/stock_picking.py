# -*- coding: utf-8 -*-

from odoo import models, fields,api


class StockPicking(models.Model):
    _inherit = 'stock.picking'
    
    def _action_done(self):
        ro_pos_order = self.env.context.get('ro_pos_order') or self.pos_order_id
        
        self.env.context = dict(self.env.context)
        self.env.context.update({'cancel_backorder': True})

        
        if  ro_pos_order and self.picking_type_id.code == 'outgoing':
            for move in self.move_ids_without_package.filtered(lambda x:not x.description_bom_line):
                boxs_weight = 0
                chosen_box = False
                # Logic specific to POS orders
                for line in ro_pos_order.lines.filtered(lambda l: l.product_id == move.product_id):
                    product = line.product_id
                    demand = line.qty
                    chosen_box = product.ro_box_weight_ids.filtered(
                        lambda box: box.ro_range_from < demand and demand <= box.ro_range_to)[:1]

                    if chosen_box:
                        boxs_weight += demand - chosen_box.ro_product_id.weight
                        self._create_box_move(chosen_box)
                        
                if chosen_box:    
                    move.write({'quantity_done': boxs_weight})
                    
        elif self.picking_type_id.code == 'outgoing' and self.sale_id:
            for move in self.move_ids_without_package.filtered(lambda x:not x.description_bom_line):
                # Logic specific to Sale orders
                    demand = move.product_uom_qty
                    chosen_box = move.product_id.ro_box_weight_ids.filtered(
                        lambda box: box.ro_range_from < demand and demand <= box.ro_range_to)[:1]

                    if chosen_box:
                        done = demand - chosen_box.ro_product_id.weight
                        move.write({'quantity_done': done})
                        self._create_box_move(chosen_box)
                        
        return super(StockPicking, self)._action_done()

    def _create_box_move(self, chosen_box):
        box_move_vals = {
            'name': chosen_box.ro_product_id.name,
            'product_uom': chosen_box.ro_product_id.uom_id.id,
            'picking_id': self.id,
            'picking_type_id': self.picking_type_id.id,
            'product_id': chosen_box.ro_product_id.id,
            'product_uom_qty': 1,
            'location_id': self.location_id.id,
            'location_dest_id': self.location_dest_id.id,
            'company_id': self.company_id.id,
            'quantity_done': 1,
        }
        box_move = self.env['stock.move'].create(box_move_vals)
        box_move._action_confirm()

