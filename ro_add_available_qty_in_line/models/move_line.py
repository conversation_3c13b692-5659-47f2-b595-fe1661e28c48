from odoo import models, fields, api

class MoveLineInherited(models.Model):
    _inherit = 'stock.move'

    ro_available_qty = fields.Float(
        string='Qty Available',
        compute='_compute_available_quantity'
    )

    @api.depends('product_id', 'location_id', 'product_uom_qty')
    def _compute_available_quantity(self):
        for record in self:
            product = record.product_id
            location = record.location_id

            # Assuming 'free_qty' is a field on the product model
            available_quantity = product.with_context({'location': location.id}).free_qty

            # Adjust the available quantity based on the product's UOM
            # if product.uom_id and record.product_uom:
            #     available_quantity = product.uom_id._compute_quantity(available_quantity, record.product_uom)

            record.ro_available_qty = available_quantity




