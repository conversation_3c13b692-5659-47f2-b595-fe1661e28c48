# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
import json


class SaleOrder(models.Model):
    _inherit = 'sale.order'

    def action_confirm(self):
        super().action_confirm()
        for rec in self:
            if rec.partner_id.property_delivery_carrier_id:
                rec.set_delivery_line(rec.partner_id.property_delivery_carrier_id, rec.partner_id.property_delivery_carrier_id.fixed_price)
                rec.write({
                    'recompute_delivery_price': False,
                    'delivery_message': "",
                })