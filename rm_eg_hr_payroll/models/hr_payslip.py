# -*- coding: utf-8 -*-

##############################################################################
#
#
#    Copyright (C) 2020-TODAY .
#    Author: <PERSON>g<PERSON> (<<EMAIL>>)
#
#    It is forbidden to publish, distribute, sublicense, or sell copies
#    of the Software or modified copies of the Software.
#
##############################################################################


from odoo import models, fields, api
from datetime import date, datetime
from dateutil.relativedelta import relativedelta


class HrPayslip(models.Model):
    _inherit = "hr.payslip"

    payment_type = fields.Selection([('cash','Cash'),('bank','Bank')], related='employee_id.payment_type', store=True)


    tax_amount = fields.Float(compute="get_tax_amount")

    @api.depends('line_ids')
    def get_tax_amount(self):
        for rec in self:
            rec.tax_amount = 0
            tax_line = rec.line_ids.filtered(lambda x:x.code == 'TXDED')
            if tax_line:
                rec.tax_amount = tax_line[-1].amount