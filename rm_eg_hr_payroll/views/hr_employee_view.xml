<?xml version="1.0" encoding="utf-8"?>

<odoo>
    <data>
        <record id="view_employee_form_inherit" model="ir.ui.view">
            <field name="name">view.employee.form.inherit</field>
            <field name="model">hr.employee</field>
            <field name="inherit_id" ref="hr.view_employee_form"/>
            <field name="arch" type="xml">

                <xpath expr="//field[@name='birthday']" position="after">
                    <label for="age"/>
                    <div class="o_row">
                        <field name="age"/>Years
                    </div>
                </xpath>
                <xpath expr="//field[@name='identification_id']" position="after">
                    <field name="religion"/>
                </xpath>
                <xpath expr="//field[@name='gender']" position="after">

                    <field name="military_status" attrs="{'invisible':[('gender','!=', 'male')]}"/>

                </xpath>

                <xpath expr="//group[@name='active_group']" position="after">
                    <group string="Experience">
                        <field name="start_date"/>
                        <label for="experience_y"/>
                        <div class="o_row">
                            <field name="experience_y"/>Years-<field name="experience_m"/>Months-<field
                                name="experience_d"/>Days
                        </div>
                    </group>
                </xpath>
                
                <xpath expr="//field[@name='bank_account_id']" position="after">
                    <field name="payment_type"/>
                </xpath>

                <xpath expr="//notebook/page[@name='personal_information']" position="after">
                    <page name="insurance_info" string="Insurance Information">
                        <group>
                            <group>
                                <field name="is_retirment" />
                                <field name="is_five_perc" />
                                
                                <field name="insurance_job_position" />
                                
                                <field name="card_type" />
                                <field name="has_insurance_booklet" />
                                <field name="no_booklet_reason" attrs="{'invisible':[('has_insurance_booklet','!=', 'no_booklet')]}"/>
                                
                                <field name="sin_exist"/>
                                <field name="sin_no" attrs="{'invisible':[('sin_exist','=', False)]}"/>
                                <field name="sin_date" attrs="{'invisible':[('sin_exist','=', False)]}"/>
                                <field name="sin_end_date"
                                       attrs="{'invisible':[('sin_exist','=', False)]}"/>
                            </group>
                            <group>
                                <!--<field name="experience"/>-->
                                <field name="mi_exist"/>
                                <field name="mi_no" attrs="{'invisible':[('mi_exist','=', False)]}"/>
                                <field name="mi_date" attrs="{'invisible':[('mi_exist','=', False)]}"/>
                            </group>
                        </group>
                    </page>
                </xpath>

            </field>
        </record>
    </data>
</odoo>