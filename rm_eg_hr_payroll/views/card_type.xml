<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="view_card_type_form" model="ir.ui.view">
            <field name="name">view.card.type.form</field>
            <field name="model">card.type</field>
            <field name="arch" type="xml">
                <form string="">
                    <sheet>
                        <group>
                            <field name="name"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <record id="view_card_type_tree" model="ir.ui.view">
            <field name="name">view.card.type.tree</field>
            <field name="model">card.type</field>
            <field name="arch" type="xml">
                <tree string="">
                    <field name="name"/>
                </tree>
            </field>
        </record>

        <record id="view_action_card_type" model="ir.actions.act_window">
            <field name="name">Card Type</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">card.type</field>
            <field name="view_mode">tree,form</field>
        </record>


        <menuitem
                action="view_action_card_type"
                id="menu_card_type"
                parent="hr_work_entry_contract_enterprise.menu_hr_payroll_configuration"
                sequence="101"/>
    </data>
</odoo>