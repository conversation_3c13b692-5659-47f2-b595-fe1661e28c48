# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* eg_hr_payroll
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-07-07 12:52+0000\n"
"PO-Revision-Date: 2017-07-07 14:53+0200\n"
"Last-Translator: <>\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: \n"
"Language: ar\n"
"X-Generator: Poedit 2.0.2\n"

#. module: eg_hr_payroll
#: model:hr.alw,name:eg_hr_payroll.other_alw_an17
msgid "2017 Annual Raise"
msgstr "علاوة اجتماعية 2017"

#. module: eg_hr_payroll
#: model:hr.salary.rule,name:eg_hr_payroll.hr_payroll_rule_raise2017
msgid "2017 annual Raise"
msgstr "علاوة اجتماعية 2017"

#. module: eg_hr_payroll
#: model:ir.ui.view,arch_db:eg_hr_payroll.report_payslip_eg
msgid "<strong>Age</strong>"
msgstr "<strong>العمر</strong>"

#. module: eg_hr_payroll
#: model:ir.ui.view,arch_db:eg_hr_payroll.report_payslip_eg
msgid "<strong>Authorized signature</strong>"
msgstr "<strong>توقيع معتمد</strong>"

#. module: eg_hr_payroll
#: model:ir.ui.view,arch_db:eg_hr_payroll.report_payslip_eg
msgid "<strong>Birth Date</strong>"
msgstr "<strong>تاريخ الميلاد</strong>"

#. module: eg_hr_payroll
#: model:ir.ui.view,arch_db:eg_hr_payroll.report_payslip_eg
msgid "<strong>Department</strong>"
msgstr "<strong>القسم</strong>"

#. module: eg_hr_payroll
#: model:ir.ui.view,arch_db:eg_hr_payroll.report_payslip_eg
msgid "<strong>Education</strong>"
msgstr "<strong>المؤهل</strong>"

#. module: eg_hr_payroll
#: model:ir.ui.view,arch_db:eg_hr_payroll.report_payslip_eg
msgid "<strong>Marital Status</strong>"
msgstr "<strong>الحالة الاجتماعية</strong>"

#. module: eg_hr_payroll
#: model:ir.ui.view,arch_db:eg_hr_payroll.report_payslip_eg
msgid "<strong>Name</strong>"
msgstr "<strong>اسم</strong>"

#. module: eg_hr_payroll
#: model:ir.ui.view,arch_db:eg_hr_payroll.report_payslip_eg
msgid "<strong>Start Working At</strong>"
msgstr "<strong>بداية التعيين</strong>"

#. module: eg_hr_payroll
#: model:hr.salary.rule,name:eg_hr_payroll.hr_salary_rule_absence
msgid "Absence"
msgstr "غياب"

#. module: eg_hr_payroll
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_employee_age
msgid "Age"
msgstr "العـمــر"

#. module: eg_hr_payroll
#: model:hr.salary.rule,name:eg_hr_payroll.hr_payroll_rule_allowances
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_employee_allowances
#: model:ir.ui.view,arch_db:eg_hr_payroll.view_employee_form_inherit
msgid "Allowances"
msgstr "البدلات"

#. module: eg_hr_payroll
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_other_alw_amount
msgid "Amount"
msgstr "المبلغ"

#. module: eg_hr_payroll
#: model:hr.salary.rule,name:eg_hr_payroll.hr_salary_rule_attdelay
msgid "Attendance Delay"
msgstr "تأخير"

#. module: eg_hr_payroll
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_employee_basic_salary
msgid "Basic Salary"
msgstr "أساسي التأمينات"

#. module: eg_hr_payroll
#: model:hr.rule.input,name:eg_hr_payroll.hr_rule_input_penalties
msgid "Cash Penalties In Days"
msgstr "عدد ايام الجزاءات"

#. module: eg_hr_payroll
#: selection:hr.employee,religion:0
msgid "Christian"
msgstr "مسيحي"

#. module: eg_hr_payroll
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_other_alw_code
msgid "Code"
msgstr "الكود"

#. module: eg_hr_payroll
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_eg_education_create_uid
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_eg_school_create_uid
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_other_alw_create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: eg_hr_payroll
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_eg_education_create_date
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_eg_school_create_date
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_other_alw_create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: eg_hr_payroll
#: selection:hr.employee,military_status:0
msgid "Currently serving "
msgstr "مازال في الخدمة"

#. module: eg_hr_payroll
#: model:ir.ui.view,arch_db:eg_hr_payroll.view_employee_form_inherit
msgid "Days"
msgstr "يوم"

#. module: eg_hr_payroll
#: model:ir.ui.view,arch_db:eg_hr_payroll.report_payslip_eg
msgid "Deduction"
msgstr "الإستقطاعات"

#. module: eg_hr_payroll
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_eg_education_display_name
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_eg_school_display_name
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_other_alw_display_name
msgid "Display Name"
msgstr "اسم العرض"

#. module: eg_hr_payroll
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_eg_education_name
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_employee_edu_phase
#: model:ir.ui.view,arch_db:eg_hr_payroll.view_employee_form_inherit
msgid "Education"
msgstr "المؤهل"

#. module: eg_hr_payroll
#: model:ir.ui.view,arch_db:eg_hr_payroll.view_employee_form_inherit
msgid "Education Info"
msgstr "بيانات التعليم"

#. module: eg_hr_payroll
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_employee_edu_note
msgid "Education Notes"
msgstr "ملاحظات"

#. module: eg_hr_payroll
#: model:ir.ui.view,arch_db:eg_hr_payroll.view_employee_form_inherit
msgid "Education Notes ..........."
msgstr "ملاحظات"

#. module: eg_hr_payroll
#: model:ir.model,name:eg_hr_payroll.model_hr_employee
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_other_alw_employee_id
msgid "Employee"
msgstr "الموظف"

#. module: eg_hr_payroll
#: model:hr.rule.input,name:eg_hr_payroll.hr_rule_input_absence
msgid "Employee Absence In Days"
msgstr "عدد أيام الغياب"

#. module: eg_hr_payroll
#: model:hr.rule.input,name:eg_hr_payroll.hr_rule_input_delay
msgid "Employee Attendance Delay In Minutes"
msgstr "عدد دقائق التاخير"

#. module: eg_hr_payroll
#: selection:hr.employee,edu_grad:0
msgid "Excellent"
msgstr "ممتاز"

#. module: eg_hr_payroll
#: selection:hr.employee,military_status:0
msgid "Exemption"
msgstr "اعفاء"

#. module: eg_hr_payroll
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_employee_experience_y
#: model:ir.ui.view,arch_db:eg_hr_payroll.view_employee_form_inherit
msgid "Experience"
msgstr "الخبرة"

#. module: eg_hr_payroll
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_employee_experience_d
msgid "Experience dayes"
msgstr "Experience dayes"

#. module: eg_hr_payroll
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_employee_experience_m
msgid "Experience monthes"
msgstr "Experience monthes"

#. module: eg_hr_payroll
#: model:hr.eg.school,name:eg_hr_payroll.hr_school_eng
msgid "Faculity of Electronic Engineering"
msgstr "كلية الهندسة الالكترونية"

#. module: eg_hr_payroll
#: selection:hr.employee,edu_grad:0
msgid "Good"
msgstr "جيد"

#. module: eg_hr_payroll
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_employee_edu_grad
msgid "Grad"
msgstr "التقدير"

#. module: eg_hr_payroll
#: model:hr.salary.rule,name:eg_hr_payroll.hr_rule_gross
#: model:ir.ui.view,arch_db:eg_hr_payroll.report_payslip_eg
msgid "Gross"
msgstr "ألاستحقاقات"

#. module: eg_hr_payroll
#: model:ir.ui.view,arch_db:eg_hr_payroll.report_payslip_eg
msgid "Gross Value"
msgstr "إجمالي ألاستحقاقات"

#. module: eg_hr_payroll
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_employee_mi_exist
msgid "Has Medical Insurance"
msgstr "يوجد تامين صحي"

#. module: eg_hr_payroll
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_employee_sin_exist
msgid "Has Social Insurance"
msgstr "يوجد تامين اجتماعي"

#. module: eg_hr_payroll
#: model:ir.model,name:eg_hr_payroll.model_hr_department
msgid "Hr Department"
msgstr "القسم"

#. module: eg_hr_payroll
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_eg_education_id
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_eg_school_id
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_other_alw_id
msgid "ID"
msgstr "المعرف"

#. module: eg_hr_payroll
#: model:hr.salary.rule,name:eg_hr_payroll.hr_salary_rule_tax_deduction
msgid "Income tax Deduction"
msgstr "خصم ضريبة الدخل"

#. module: eg_hr_payroll
#: model:ir.ui.view,arch_db:eg_hr_payroll.view_employee_form_inherit
msgid "Insurance Information"
msgstr "بيانات التامينات "

#. module: eg_hr_payroll
#: model:ir.ui.view,arch_db:eg_hr_payroll.view_employee_form_inherit
msgid "Insurance Items"
msgstr "بنود التامينات"

#. module: eg_hr_payroll
#: model:hr.salary.rule,name:eg_hr_payroll.hr_salary_rule_variable
msgid "Insurance Variable"
msgstr "متغير التامين"

#. module: eg_hr_payroll
#: model:hr.salary.rule,name:eg_hr_payroll.hr_salary_rule_basic
msgid "Insurance basic"
msgstr "اساسي التامين"

#. module: eg_hr_payroll
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_eg_education___last_update
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_eg_school___last_update
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_other_alw___last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: eg_hr_payroll
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_eg_education_write_uid
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_eg_school_write_uid
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_other_alw_write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: eg_hr_payroll
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_eg_education_write_date
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_eg_school_write_date
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_other_alw_write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: eg_hr_payroll
#: model:ir.model.fields,help:eg_hr_payroll.field_hr_employee_mi_date
msgid "Medical  Insurance Date"
msgstr "تاريخ التامين الصحي"

#. module: eg_hr_payroll
#: model:ir.model.fields,help:eg_hr_payroll.field_hr_employee_mi_no
msgid "Medical  Insurance No"
msgstr "رقم التامين الصحي"

#. module: eg_hr_payroll
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_employee_mi_date
msgid "Medical Insurance Date"
msgstr "تاريخ التامين الصحي"

#. module: eg_hr_payroll
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_employee_mi_no
msgid "Medical Insurance NO"
msgstr "رقم التامين الصحي"

#. module: eg_hr_payroll
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_employee_military_status
msgid "Military Status"
msgstr "الموقف من التجنيد"

#. module: eg_hr_payroll
#: model:ir.ui.view,arch_db:eg_hr_payroll.view_employee_form_inherit
msgid "Months-"
msgstr "شهر"

#. module: eg_hr_payroll
#: selection:hr.employee,religion:0
msgid "Muslem"
msgstr "مسلم"

#. module: eg_hr_payroll
#: selection:hr.employee,military_status:0
msgid "Not Required"
msgstr "غير مطلوب "

#. module: eg_hr_payroll
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_eg_education_note
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_eg_school_note
msgid "Note"
msgstr "الملاحظة"

#. module: eg_hr_payroll
#: model:ir.ui.view,arch_db:eg_hr_payroll.view_employee_form_inherit
msgid "Ohter allowances"
msgstr "بدلات اخري"

#. module: eg_hr_payroll
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_employee_other_alw_ids
#: model:ir.ui.view,arch_db:eg_hr_payroll.view_employee_form_inherit
msgid "Other Allowances"
msgstr "بدلات اخري"

#. module: eg_hr_payroll
#: selection:hr.employee,religion:0
msgid "Others"
msgstr "أخري"

#. module: eg_hr_payroll
#: selection:hr.employee,edu_grad:0
msgid "Pass"
msgstr "مقبول"

#. module: eg_hr_payroll
#: model:ir.model,name:eg_hr_payroll.model_hr_payslip
msgid "Pay Slip"
msgstr "ظرف المرتب"

#. module: eg_hr_payroll
#: model:ir.actions.report.xml,name:eg_hr_payroll.action_report_eg_payslip
msgid "Payslip Report"
msgstr "تقرير استمارة المرتب"

#. module: eg_hr_payroll
#: model:hr.salary.rule,name:eg_hr_payroll.hr_salary_rule_penalties
msgid "Penalties"
msgstr "الجزاءات"

#. module: eg_hr_payroll
#: selection:hr.employee,military_status:0
msgid "Postponed"
msgstr "مؤجل"

#. module: eg_hr_payroll
#: model:hr.employee,name:eg_hr_payroll.employee_rk
#: model:hr.employee,name_related:eg_hr_payroll.employee_rk
#: model:resource.resource,name:eg_hr_payroll.employee_rk_resource_resource
msgid "Ramadan Khalil"
msgstr "رمضان خليل"

#. module: eg_hr_payroll
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_employee_religion
msgid "Religion"
msgstr "الديانة"

#. module: eg_hr_payroll
#: model:ir.model,name:eg_hr_payroll.model_resource_resource
msgid "Resource Detail"
msgstr "تفاصيل المصدر"

#. module: eg_hr_payroll
#: model:ir.ui.view,arch_db:eg_hr_payroll.view_employee_form_inherit
msgid "Salary Items"
msgstr "بنود المرتب"

#. module: eg_hr_payroll
#: model:ir.model,name:eg_hr_payroll.model_hr_salary_rule_category
msgid "Salary Rule Category"
msgstr "فئة قاعدة المرتب"

#. module: eg_hr_payroll
#: model:ir.model,name:eg_hr_payroll.model_hr_rule_input
msgid "Salary Rule Input"
msgstr "مدخلات قاعده المرتب "

#. module: eg_hr_payroll
#: code:addons/eg_hr_payroll/models/eg_hr_payroll.py:176
#, python-format
msgid "Salary Slip for %s"
msgstr "استمارة مرتب لشهر  %s"

#. module: eg_hr_payroll
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_eg_school_name
msgid "School name"
msgstr "اسم المدرسة /الكلية/المعهد"

#. module: eg_hr_payroll
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_employee_edu_school
msgid "School/University/Institute"
msgstr "اسم المدرسة /الكلية/المعهد"

#. module: eg_hr_payroll
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_employee_sin_end_date
msgid "Social Insurance  end Date"
msgstr "تاريخ نهاية التامينات"

#. module: eg_hr_payroll
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_employee_sin_date
msgid "Social Insurance Date"
msgstr "تاريخ بداية التامين الاجتماعي"

#. module: eg_hr_payroll
#: model:hr.salary.rule,name:eg_hr_payroll.hr_salary_rule_insurance_deduction
msgid "Social Insurance Deduction"
msgstr "خصم التامينات الاجتماعية"

#. module: eg_hr_payroll
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_employee_sin_no
msgid "Social Insurance No"
msgstr "رقم التامين الاجتماعي"

#. module: eg_hr_payroll
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_employee_start_date
msgid "Start Working At"
msgstr "تاريخ بداية العمل "

#. module: eg_hr_payroll
#: model:hr.salary.rule,name:eg_hr_payroll.hr_rule_taxable
msgid "Taxable Amount"
msgstr "المبلغ الخاضع للضريبة"

#. module: eg_hr_payroll
#: model:ir.ui.view,arch_db:eg_hr_payroll.report_payslip_eg
msgid "Total"
msgstr "الإجمالي "

#. module: eg_hr_payroll
#: model:ir.ui.view,arch_db:eg_hr_payroll.report_payslip_eg
msgid "Total Deduction Value"
msgstr "إجمالي الاستقطاعات"

#. module: eg_hr_payroll
#: model:ir.ui.view,arch_db:eg_hr_payroll.report_payslip_eg
msgid "Total Net"
msgstr "الصافي"

#. module: eg_hr_payroll
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_employee_variable_salary
msgid "Variable Salary"
msgstr "متغير التامينات"

#. module: eg_hr_payroll
#: selection:hr.employee,edu_grad:0
msgid "Very Good"
msgstr "جيد جدا"

#. module: eg_hr_payroll
#: model:ir.ui.view,arch_db:eg_hr_payroll.view_employee_form_inherit
msgid "Years"
msgstr "سنة"

#. module: eg_hr_payroll
#: model:ir.ui.view,arch_db:eg_hr_payroll.view_employee_form_inherit
msgid "Years-"
msgstr "سنة"

#. module: eg_hr_payroll
#: model:hr.eg.education,name:eg_hr_payroll.hr_education_bacelor
msgid "bachelor of Engineering"
msgstr "بكالوريوس"

#. module: eg_hr_payroll
#: selection:hr.employee,military_status:0
msgid "complete"
msgstr "انهي الخدمة"

#. module: eg_hr_payroll
#: model:ir.model.fields,help:eg_hr_payroll.field_hr_employee_experience_y
msgid "experience in our company"
msgstr "سنوات الخبرة داخل الشركة"

#. module: eg_hr_payroll
#: model:ir.model,name:eg_hr_payroll.model_hr_eg_education
msgid "hr.eg.education"
msgstr "hr.eg.education"

#. module: eg_hr_payroll
#: model:ir.model,name:eg_hr_payroll.model_hr_eg_school
msgid "hr.eg.school"
msgstr "hr.eg.school"

#. module: eg_hr_payroll
#: model:ir.model,name:eg_hr_payroll.model_hr_other_alw
msgid "hr.alw"
msgstr "hr.alw"

#. module: eg_hr_payroll
#: model:ir.model,name:eg_hr_payroll.model_hr_salary_rule
msgid "hr.salary.rule"
msgstr "hr.salary.rule"

#. module: eg_hr_payroll
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_employee_edu_major
msgid "major"
msgstr "التخصص"

#. module: eg_hr_payroll
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_other_alw_name
msgid "name"
msgstr "الاسم"

#. module: eg_hr_payroll
#: model:hr.salary.rule,name:eg_hr_payroll.hr_payroll_rule_raise
#: model:ir.model.fields,field_description:eg_hr_payroll.field_hr_employee_prev_raise
msgid "previous Annual Raises"
msgstr "علاوات اجتماعية سابقة"
