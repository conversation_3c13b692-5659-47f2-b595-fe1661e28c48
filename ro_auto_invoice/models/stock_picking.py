from odoo import api, fields, models

class StockPicking(models.Model):
    _inherit = 'stock.picking'

    def _action_done(self):
        res = super(StockPicking, self)._action_done()
        if res:
            sale_pickings = self.filtered(lambda picking: picking.picking_type_code in ('outgoing','incoming') and picking.sale_id)
            sale_pickings.sale_id.create_invoice_on_delivery_validation()
        return res
