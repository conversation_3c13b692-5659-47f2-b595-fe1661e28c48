# -*- coding: utf-8 -*-

from odoo import api,fields,models,_
from odoo.addons.sale_stock.models.stock import StockPicking
from odoo.addons.stock_account.models.stock_valuation_layer import StockValuationLayer 
from odoo.exceptions import UserError

# def _validate_accounting_entries(self):
#     am_vals = []
#     for svl in self:
#         if not svl.with_company(svl.company_id).product_id.valuation == 'real_time':
#             continue
#         if svl.currency_id.is_zero(svl.value):
#             continue
#         move = svl.stock_move_id
#         if not move:
#             move = svl.stock_valuation_layer_id.stock_move_id
#         am_vals += move.with_company(svl.company_id)._account_entry_move(svl.quantity, svl.description, svl.id, svl.value)
#     if am_vals:
#         account_moves = self.env['account.move'].sudo().create(am_vals)
#         account_moves.action_post()
#         # for line in account_moves.line_ids.analytic_line_ids:
#         #     line.on_change_unit_amount()
#     for svl in self:
#         # Eventually reconcile together the invoice and valuation accounting entries on the stock interim accounts
#         if svl.company_id.anglo_saxon_accounting:
#             svl.stock_move_id._get_related_invoices()._stock_account_anglo_saxon_reconcile_valuation(product=svl.product_id)
# StockValuationLayer._validate_accounting_entries = _validate_accounting_entries

# class StockPickingInh(models.Model):
#     _name = 'stock.picking'
#     _inherit = ['stock.picking', 'analytic.mixin']
    
#     manual_sale_id = fields.Many2one('sale.order')


#     # plan_id = fields.Many2one('account.analytic.plan',string='Project')
#     analytic_account_id = fields.Many2one('account.analytic.account',
#         string="Analytic Account",
#         copy=False, check_company=True,  # Unrequired company
#         domain="['|', ('company_id', '=', False), ('company_id', '=', company_id)]")
#     # ('plan_id','=',plan_id), 
    
#     # === Analytic fields === #
#     analytic_line_ids = fields.One2many(
#         comodel_name='account.analytic.line', inverse_name='picking_id',
#         string='Analytic lines',
#     )


#     def _create_backorder(self):
#         res = super(StockPicking, self)._create_backorder()
#         for backorder in res:
#             backorder.analytic_account_id = backorder.backorder_id.analytic_account_id
#         return res

# class StockRule(models.Model):
#     _inherit = 'stock.rule'
  
#     def _get_custom_move_fields(self):
#         fields = super(StockRule, self)._get_custom_move_fields()
#         fields += ['analytic_account_id']
#         return fields


class StockMove(models.Model):
    _inherit = "stock.move"

    # analytic_account_id = fields.Many2one('account.analytic.account',
    #     string="Analytic Account",
    #     copy=False, check_company=True,  # Unrequired company
    #     domain="['|', ('company_id', '=', False), ('company_id', '=', company_id)]")
    # account_id = fields.Many2one('account.account', domain=[('is_delivery_account','=',True)])
    
    # analytic_account_id_2 = fields.Many2one('account.analytic.account',
    #     string="Analytic Account 2",
    #     copy=False, check_company=True,  # Unrequired company
    #     domain="['|', ('company_id', '=', False), ('company_id', '=', company_id)]")


    def _generate_valuation_lines_data(self, partner_id, qty, debit_value, credit_value, debit_account_id, credit_account_id, svl_id, description):
        result = super(StockMove, self)._generate_valuation_lines_data(partner_id, qty, debit_value, credit_value, debit_account_id, credit_account_id, svl_id, description)

       
        sale_analytic_account = False
        if self.picking_id.sale_id:
            sale_analytic_account = self.picking_id.sale_id.team_id.analytic_distribution
        if self.picking_id.pos_order_id or  self.env.context.get('ro_pos_order'):
            ro_pos_order = self.env.context.get('ro_pos_order') or self.picking_id.pos_order_id

            sale_analytic_account = ro_pos_order.config_id.analytic_distribution
        
        if sale_analytic_account:
            # result['debit_line_vals']['account_id'] = account_id
            result['debit_line_vals']['analytic_distribution'] = sale_analytic_account
            result['credit_line_vals']['analytic_distribution'] = sale_analytic_account

        # if self.picking_id.picking_type_code == 'incoming' and sale_analytic_account:
        #     # result['credit_line_vals']['account_id'] = account_id
        

        return result

# class StockReturnPicking(models.TransientModel):
#     _inherit = 'stock.return.picking'

#     def _create_returns(self):
        
#         new_picking, pick_type_id = super(StockReturnPicking, self)._create_returns()

        
#         picking = self.env['stock.picking'].browse(new_picking)
#         origin_picking = self.env['stock.picking'].search([('name','=',picking.origin.replace('Return of ',''))])
#         picking.write({
#             # 'plan_id': origin_picking.plan_id,
#             'analytic_account_id': origin_picking.analytic_account_id,
#             # 'account_id': origin_picking.account_id,
#             })
#         return new_picking, pick_type_id
    

# def _action_done(self):
#     res = super(StockPicking,self)._action_done()
#     # sale_order_lines_vals = []
#     # for move in self.move_ids:
#     #     sale_order = move.picking_id.sale_id
#     #     if sale_order:
#     #         group_id = self.env['procurement.group'].create({
#     #             'name': sale_order.name,
#     #             'move_type': sale_order.picking_policy,
#     #             'sale_id': sale_order.id,
#     #             'partner_id': sale_order.partner_shipping_id.id,
#     #         })
#     #         self.group_id = group_id

#         # Creates new SO line only when pickings linked to a sale order and
#         # for moves with qty. done and not already linked to a SO line.

#         #################33
#         #Override To stop create sol for new line in picking
#         #################33
#         # if not sale_order or move.location_dest_id.usage != 'customer' or move.sale_line_id or not move.quantity_done:
#         #     continue
#         # product = move.product_id
#         # so_line_vals = {
#         #     'move_ids': [(4, move.id, 0)],
#         #     'name': product.display_name,
#         #     'order_id': sale_order.id,
#         #     'product_id': product.id,
#         #     'product_uom_qty': 0,
#         #     'qty_delivered': move.quantity_done,
#         #     'product_uom': move.product_uom.id,
#         # }
#         # if product.invoice_policy == 'delivery':
#         #     # Check if there is already a SO line for this product to get
#         #     # back its unit price (in case it was manually updated).
#         #     so_line = sale_order.order_line.filtered(lambda sol: sol.product_id == product)
#         #     if so_line:
#         #         so_line_vals['price_unit'] = so_line[0].price_unit
#         # elif product.invoice_policy == 'order':
#         #     # No unit price if the product is invoiced on the ordered qty.
#         #     so_line_vals['price_unit'] = 0
#         # sale_order_lines_vals.append(so_line_vals)

#     # if sale_order_lines_vals:
#     #     self.env['sale.order.line'].with_context(skip_procurement=True).create(sale_order_lines_vals)
#     return res
# StockPicking._action_done = _action_done