# -*- coding: utf-8 -*-

from odoo import api,fields,models,_
from odoo.addons.sale_stock.models.sale_order import SaleOrder
# class SaleOrder(models.Model):
#     _inherit = 'sale.order'
    
#     manual_picking_ids = fields.One2many('stock.picking', 'manual_sale_id', string='Sale Manual Transfers')


# class SaleOrderLine(models.Model):
#     _inherit = 'sale.order.line'
        
#     def _prepare_procurement_values(self,group_id):
#         res = super(SaleOrderLine,self)._prepare_procurement_values(group_id)
#         res['analytic_account_id'] = self.order_id.analytic_account_id.id
#         return res
        
