<odoo>
  <data>
    <!-- <record id="view_sale_order_form_analytic_account" model="ir.ui.view">
      <field name="model">sale.order</field>
      <field name="inherit_id" ref="sale_stock.view_order_form_inherit_sale_stock"/>
      <field name="arch" type="xml">
          <xpath expr="//button[@name='action_view_delivery']" position="attributes">
            <attribute name="attrs">{'invisible': [('state','not in', ['sale','done']), ('delivery_count', '=', 0)]}</attribute>
          </xpath>   
      </field>
    </record> -->


    <record id="view_picking_form_analytic_account" model="ir.ui.view">
      <field name="model">stock.picking</field>
      <field name="inherit_id" ref="stock.view_picking_form"/>
      <field name="arch" type="xml">
          <xpath expr="//field[@name='origin']" position="after">
            <!-- groups="analytic.group_analytic_accounting" -->
            <field name="analytic_account_id" attrs="{'readonly': [('state','=','done')], 'invisible': [('picking_type_code', 'not in', ('outgoing','incoming'))],}"  force_save="1"/>
            <!-- <field name="analytic_distribution" widget="analytic_distribution"
                    options="{'force_applicability': 'optional', 'disable_save': true}" attrs="{'invisible': [('picking_type_code', 'not in', ('outgoing','incoming'))], 'required': [('picking_type_code', 'in', ('outgoing')),('sale_id','=',False)]}"/> -->
            <field name="sale_id" invisible="1"/>  
          </xpath>   

          <xpath expr="//field[@name='move_ids_without_package']/tree/field[@name='product_uom_qty']" position="before">
            <field name="analytic_account_id" attrs="{'readonly': ['|',('product_id', '=', False),('parent.state','=','done')], 'column_invisible':['|',('parent.picking_type_code', 'not in', ('outgoing','incoming')) ,('parent.state', '=', 'draft'), ('parent.immediate_transfer', '=', False)]}" force_save="1"/>
            <field name="account_id" options='{"no_open": True, "no_create": True}' attrs="{'readonly': ['|',('product_id', '=', False),('parent.state','=','done')], 'column_invisible':['|',('parent.picking_type_code', 'not in', ('outgoing','incoming')) ,('parent.state', '=', 'draft'), ('parent.immediate_transfer', '=', False)]}" force_save="1"/>
            <!-- <field name="analytic_account_id_2" attrs="{'readonly': ['|',('product_id', '=', False),('parent.state','=','done')], 'column_invisible':['|',('parent.picking_type_code', 'not in', ('outgoing','incoming')) ,('parent.state', '=', 'draft'), ('parent.immediate_transfer', '=', False)]}" force_save="1"/> -->
          </xpath>   
          <!-- attrs="{'readonly': [('state','=','done')], 'invisible': [('picking_type_code', 'not in', ('outgoing','incoming'))], 'required': [('picking_type_code', 'in', ('outgoing')),('sale_id','=',False)]}"  force_save="1"/> -->
          
      </field>
    </record>
  </data>
</odoo>