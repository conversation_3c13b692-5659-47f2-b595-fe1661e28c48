from odoo import models, api, _
from odoo.exceptions import ValidationError

class StockPicking(models.Model):
    _inherit = 'stock.picking'

    def button_validate(self):
        for picking in self:
            if picking.picking_type_code == 'incoming':
                for move in picking.move_ids:
                    if move.product_id and move.quantity_done > move.product_uom_qty:
                        raise ValidationError(_('Received quantity cannot be greater than ordered quantity for product %s' % move.product_id.display_name))

        result = super().button_validate()
        return result
