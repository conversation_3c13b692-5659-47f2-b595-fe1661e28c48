<?xml version="1.0" encoding="UTF-8"?>
<odoo>

  <record id="hr_contact_tag_form_inherited" model="ir.ui.view">
    <field name="name">hr.contact.tag.form.inherited</field>
    <field name="model">hr.contract</field>
    <field name="inherit_id" ref="hr_contract.hr_contract_view_form"/>
    <field name="arch" type="xml">
        <xpath expr="//field[@name='hr_responsible_id']" position="after">
          <field name="tag_id" />
        </xpath>
    </field>
  </record>

  <record id="hr_payslip_employees_customization" model="ir.ui.view">
    <field name="model">hr.payslip.employees</field>
    <field name="inherit_id" ref="hr_payroll.view_hr_payslip_by_employees"/>
    <field name="arch" type="xml">
        <xpath expr="//div[2]" position="after">
          <separator string="Emplyee Tag" colspan="2"/>
          <div class="o_row">
              <p class="text-muted w-50">Set a specific Emplyee Tag if you wish to select all the employees from this Category at once.</p>
              <field name="tag_id" nolabel="1"/>
          </div>
        </xpath>
    </field>
  </record>

</odoo>
