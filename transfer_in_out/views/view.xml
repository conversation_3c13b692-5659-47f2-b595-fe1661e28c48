<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="action_transfer_out_picking" model="ir.actions.act_window">
        <field name="name">Deliveries</field>
        <field name="res_model">stock.picking</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'default_is_transfer_out': True, 'default_company_id': allowed_company_ids[0], 'contact_display': 'partner_address'}
        </field>
        <field name="domain">[('picking_type_code','in',('internal','outgoing')),('picking_type_id.is_return','=',False),('warehouse_team_from_id.member_ids','=', uid)]</field>
    </record>
    <!-- <record id="action_transfer_return_picking" model="ir.actions.act_window">
        <field name="name">Returns</field>
        <field name="res_model">stock.picking</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'default_company_id': allowed_company_ids[0], 'contact_display': 'partner_address'}
        </field>
        <field name="domain">['|', '&amp;', ('picking_type_id.is_return','=',True), '&amp;', ('warehouse_team_id.member_ids','=', uid),('state','not in',('done','cancel')), '&amp;', ('picking_type_code','=','outgoing'), '&amp;', ('state','not in',('done','cancel')), '&amp;', ('purchase_id','!=',False), ('warehouse_team_id.member_ids','=', uid)]</field>
    </record> -->
    <record id="action_transfer_in_picking" model="ir.actions.act_window">
        <field name="name">Receives</field>
        <field name="res_model">stock.picking</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'default_is_transfer_in': True, 'default_company_id': allowed_company_ids[0], 'contact_display': 'partner_address'}
        </field>
        <field name="domain">[('picking_type_code','in',['internal','incoming']),('picking_type_id.is_return','=',False),('warehouse_team_to_id.member_ids','=', uid)]</field>
    </record>
    <record model="ir.ui.view" id="stock_picking_document_out_inherited_view">
        <field name="name">stock.picking.document.inherited.view</field>
        <field name="model">stock.picking</field>
        <field name="inherit_id" ref="stock.view_picking_form"/>
        <field name="mode">primary</field>
        <field name="priority">30</field>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='backorder_id']" position="after">
                <field name="is_transfer_out" invisible="1"/>
            </xpath>
            <xpath expr="//field[@name='picking_type_id']" position="attributes">
                <attribute name="readonly">1</attribute>
                <attribute name="force_save">1</attribute>
            </xpath>
            <xpath expr="//field[@name='location_id'][2]" position="attributes">
                <attribute name="readonly">1</attribute>
                <attribute name="force_save">1</attribute>
                <attribute name="domain">[('usage','=','internal')]</attribute>
            </xpath>
            <xpath expr="//field[@name='location_dest_id'][2]" position="attributes">
                <attribute name="domain">[('usage','=','internal')]</attribute>
            </xpath>

        </field>
    </record>
    <record model="ir.ui.view" id="stock_picking_document_in_inherited_view">
        <field name="name">stock.picking.document.in.inherited.view</field>
        <field name="model">stock.picking</field>
        <field name="inherit_id" ref="stock.view_picking_form"/>
        <field name="mode">primary</field>
        <field name="priority">30</field>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='backorder_id']" position="after">
                <field name="is_transfer_in" invisible="1"/>
            </xpath>
            <xpath expr="//field[@name='picking_type_id']" position="attributes">
                <attribute name="readonly">1</attribute>
                <attribute name="force_save">1</attribute>
            </xpath>
            <xpath expr="//field[@name='location_id'][2]" position="attributes">
                <attribute name="domain">[('usage','=','internal')]</attribute>
            </xpath>
            <xpath expr="//field[@name='location_dest_id'][2]" position="attributes">
                <attribute name="readonly">1</attribute>
                <attribute name="force_save">1</attribute>
                <attribute name="domain">[('usage','=','internal')]</attribute>
            </xpath>
        </field>
    </record>
    <record model="ir.actions.act_window.view" id="action_document_out_picking_kanban">
        <field name="sequence" eval="2"/>
        <field name="view_mode">kanban</field>
        <field name="view_id" ref="stock.stock_picking_kanban"/>
        <field name="act_window_id" ref="action_transfer_out_picking"/>
    </record>
    <!-- <record model="ir.actions.act_window.view" id="action_document_return_picking_kanban">
        <field name="sequence" eval="2"/>
        <field name="view_mode">kanban</field>
        <field name="view_id" ref="stock.stock_picking_kanban"/>
        <field name="act_window_id" ref="action_transfer_return_picking"/>
    </record> -->
    <record model="ir.actions.act_window.view" id="action_document_in_picking_kanban">
        <field name="sequence" eval="2"/>
        <field name="view_mode">kanban</field>
        <field name="view_id" ref="stock.stock_picking_kanban"/>
        <field name="act_window_id" ref="action_transfer_in_picking"/>
    </record>
    <record model="ir.actions.act_window.view" id="action_document_out_picking_tree">
        <field name="sequence" eval="1"/>
        <field name="view_mode">tree</field>
        <field name="view_id" ref="stock.vpicktree"/>
        <field name="act_window_id" ref="action_transfer_out_picking"/>
    </record>
    <!-- <record model="ir.actions.act_window.view" id="action_document_return_picking_tree">
        <field name="sequence" eval="1"/>
        <field name="view_mode">tree</field>
        <field name="view_id" ref="stock.vpicktree"/>
        <field name="act_window_id" ref="action_transfer_return_picking"/>
    </record> -->
    <record model="ir.actions.act_window.view" id="action_document_in_picking_tree">
        <field name="sequence" eval="1"/>
        <field name="view_mode">tree</field>
        <field name="view_id" ref="stock.vpicktree"/>
        <field name="act_window_id" ref="action_transfer_in_picking"/>
    </record>

    <record id="view_stock_move_line_tree_own" model="ir.ui.view">
        <field name="name">view.stock.picking.form.own.sale</field>
        <field name="model">stock.move.line</field>
        <field name="inherit_id" ref="stock.view_stock_move_line_detailed_operation_tree"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//tree" position="attributes">
                <attribute name="delete">0</attribute>
            </xpath>
        </field>
    </record>
    <record id="view_stock_picking_form_own" model="ir.ui.view">
        <field name="name">view.stock.picking.form.own.sale</field>
        <field name="model">stock.picking</field>
        <field name="inherit_id" ref="stock.view_picking_form"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">

            <xpath expr="//field[@name='move_line_ids_without_package']" position="attributes">
                <attribute name="context">{'tree_view_ref': 'transfer_in_out.view_stock_move_line_tree_own', 'default_picking_id': id, 'default_location_id': location_id, 'default_location_dest_id': location_dest_id, 'default_company_id': company_id}</attribute>
            </xpath>

        </field>
    </record>
    <record model="ir.actions.act_window.view" id="action_document_out_picking_form">
        <field name="sequence" eval="2"/>
        <field name="view_mode">form</field>
        <field name="view_id" ref="transfer_in_out.stock_picking_document_out_inherited_view"/>
        <field name="act_window_id" ref="action_transfer_out_picking"/>
    </record>
    <!-- <record model="ir.actions.act_window.view" id="action_document_return_picking_form">
        <field name="sequence" eval="2"/>
        <field name="view_mode">form</field>
        <field name="view_id" ref="transfer_in_out.view_stock_picking_form_own"/>
        <field name="act_window_id" ref="action_transfer_return_picking"/>
    </record> -->
    <record model="ir.actions.act_window.view" id="action_document_in_picking_form">
        <field name="sequence" eval="2"/>
        <field name="view_mode">form</field>
        <field name="view_id" ref="transfer_in_out.stock_picking_document_in_inherited_view"/>
        <field name="act_window_id" ref="action_transfer_in_picking"/>
    </record>
    <menuitem
            id="menu_action_transfers"
            name="Transfers"
            sequence="2"
            parent="whole_transfer.whole_transfer_menu_root"
            groups="whole_transfer.group_whole_transfer_user"/>
    <menuitem
            id="menu_action_transfer_out_picking"
            action="action_transfer_out_picking"
            sequence="2"
            parent="menu_action_transfers"/>
    <menuitem
            id="menu_action_transfer_in_picking"
            action="action_transfer_in_picking"
            sequence="3"
            parent="menu_action_transfers"/>
    <!-- <menuitem
            id="menu_action_transfer_return_picking"
            action="action_transfer_return_picking"
            sequence="4"
            parent="menu_action_transfers"/> -->
</odoo>