<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">

    <!-- multi -->
        <record model="ir.rule" id="employee_division_rule">
            <field name="name">Employee Division multi-company</field>
            <field name="model_id" search="[('model','=','employee.division')]" model="ir.model"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
        </record>

    </data>
</odoo>
