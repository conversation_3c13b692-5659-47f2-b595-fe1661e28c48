from odoo import models, fields

class HrDepartureWizard(models.TransientModel):
    _inherit = 'hr.departure.wizard'

    departure_description = fields.Text(string="Additional Information")


class Employee(models.Model):
    _inherit = 'hr.employee'

    # ro_depart = fields.Char('القسم')
    ro_division_id = fields.Many2one('employee.division', 'القسم')

    departure_description = fields.Text(string="Additional Information", groups="hr.group_hr_user", copy=False, tracking=True)

    ro_origin_certificate = fields.<PERSON><PERSON><PERSON>("أصل المؤهل")
    ro_army_certificate = fields.<PERSON><PERSON><PERSON>("أصل شهادة الجيش")
    ro_birth_certificate = fields.<PERSON><PERSON><PERSON>("أصل شهادة الميلاد")
    ro_personal_photos = fields.<PERSON><PERSON>an("8 صور شخصية")
    ro_personal_id_card = fields.<PERSON><PERSON>an("البطاقة الشخصية")
    ro_criminal_record = fields.<PERSON><PERSON><PERSON>("أصل الفيش")
    ro_work_stub = fields.<PERSON><PERSON>an("كعب عمل")
    ro_insurance_printout = fields.<PERSON><PERSON><PERSON>("برينت تأميني")
    ro_driving_license = fields.<PERSON>olean("رخصة قيادة")
    ro_skill_measurement = fields.Boolean("قياس مهارة")
    ro_qualification_certificate = fields.Boolean("شهادة تأهيل")
    ro_union_card = fields.Boolean("كارنية النقابة")
    ro_form_111 = fields.Boolean("نموذج 111")
    ro_health_certificate = fields.Boolean("الشهادة الصحية")
    ro_form_s6 = fields.Boolean("س6")
    ro_form_s1 = fields.Boolean("س1")
    
    
    
    ro_origin_certificate_note=fields.Char(string="ملاحظة أصل المؤهل")
    ro_army_certificate_note=fields.Char(string="ملاحظة أصل شهادة الجيش")
    ro_birth_certificate_note=fields.Char(string="ملاحظة أصل شهادة الميلاد")
    ro_personal_photos_note=fields.Char(string="ملاحظة 8 صور شخصية")
    ro_personal_id_card_note=fields.Char(string="ملاحظة البطاقة الشخصية")
    ro_criminal_record_note=fields.Char(string="ملاحظة أصل الفيش")
    ro_work_stub_note=fields.Char(string="ملاحظة كعب عمل")
    ro_insurance_printout_note=fields.Char(string="ملاحظة برينت تأميني")
    ro_driving_license_note=fields.Char(string="ملاحظة رخصة قيادة")
    ro_skill_measurement_note=fields.Char(string="ملاحظة قياس مهارة")
    ro_qualification_certificate_note=fields.Char(string="ملاحظة شهادة تأهيل")
    ro_union_card_note=fields.Char(string="ملاحظة كارنية النقابة")
    ro_form_111_note=fields.Char(string="ملاحظة نموذج 111")
    ro_health_certificate_note=fields.Char(string="ملاحظة الشهادة الصحية")
    ro_form_s6_note=fields.Char(string="ملاحظة س6")
    ro_form_s1_note=fields.Char(string="ملاحظة س1")
    
    
    ro_health_certificate_expiration_date=fields.Date(string="تاريخ انتهاء الشهادة الصحية")

   

    
