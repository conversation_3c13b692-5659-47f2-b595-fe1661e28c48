<odoo>
  <data>
    
    
    <record id="view_employee_division_tree" model="ir.ui.view">
      <field name="name">Employee Division</field>
      <field name="model">employee.division</field>
      <field name="arch" type="xml">
        <tree string="Divisions">
          <field name="name"/>
        </tree>
      </field>
    </record>

    <record id="view_employee_division_form" model="ir.ui.view">
      <field name="name">Employee Division</field>
      <field name="model">employee.division</field>
      <field name="arch" type="xml">
        <form>
          <sheet>
            <div>
              <h1>
                  <field name="name" placeholder="Name..."/>
              </h1>
            </div>
            <group>
              <group>
                <field name="company_id" />
              </group>
              <group></group>
            </group>
          </sheet>
          <!-- <div class="oe_chatter">
            <field name="message_follower_ids" widget="mail_followers" groups="base.group_user"/>
            <field name="activity_ids" widget="mail_activity"/>
            <field name="message_ids" widget="mail_thread"/>
          </div> -->
        </form>
      </field>
    </record>

    
    <!-- Employee Division -->
    <record id="employee_division_action_window" model="ir.actions.act_window">
      <field name="name">Employee Division</field>
      <field name="res_model">employee.division</field>
      <field name="type">ir.actions.act_window</field>
      <field name="view_mode">tree,form</field>
      <field name="help" type="html">
        <p class="o_view_nocontent_smiling_face">
          Create a Employee Division Order
        </p>
      </field>
    </record>    
    <menuitem action="employee_division_action_window" id="employee_division_menu" name="Employee Division" sequence="4" parent="hr.menu_config_employee"/>
    

  </data>
</odoo>