<?xml version="1.0" encoding="utf-8"?>
<odoo>
  <data>
     <record id="hr_department_form_inherit" model="ir.ui.view">
            <field name="name">hr.department.inherited</field>
            <field name="model">hr.department</field>
            <field name="inherit_id" ref="hr.view_department_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='parent_id']" position="after">
                    <field name="ro_department_code"/>
                </xpath>
            </field>
        </record>

         <record id="hr_department_search_view_inherit" model="ir.ui.view">
            <field name="name">hr.department.inherited</field>
            <field name="model">hr.department</field>
            <field name="inherit_id" ref="hr.view_department_filter"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='name']" position="attributes">
                    <!-- <field name="ro_department_code"/> -->
                  <attribute name="filter_domain">['|', ('name', 'ilike', self),('ro_department_code', 'ilike', self)]</attribute>
                </xpath>
            </field>
        </record>
</data>
</odoo>
