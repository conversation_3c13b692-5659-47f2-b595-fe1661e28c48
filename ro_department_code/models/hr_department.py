from odoo import models, fields, api

class HrDepartmentInherited(models.Model):
    _inherit = 'hr.department'

    ro_department_code = fields.Char(
        string='Code ',

    )


    def _name_search(self, name, args=None, operator='ilike', limit=100, name_get_uid=None):
        result = super()._name_search(name=name, args=args, operator=operator,
                                      limit=limit, name_get_uid=name_get_uid)
        if not result and name and operator in ('=', 'ilike', '=ilike', 'like', '=like'):
            args = args or []
            args = [('ro_department_code', operator, name)] + args
            return self._search(args, limit=limit, access_rights_uid=name_get_uid)
        return result
