<?xml version="1.0" encoding="UTF-8"?>
<templates id="ks_pos_low_stock_alert.template" xml:space="preserve">
    <t t-name="ProductItem" t-inherit="point_of_sale.ProductItem" t-inherit-mode="extension" owl="1">
        <xpath expr="//div[hasclass('product-img')]" position="before">
            <t t-if="(props.product.type == 'product' and (env.pos.config.allow_order_when_product_out_of_stock == false) and props.product.get_location_qty() lte 0)">
                <div class="overlay">
                    <t t-esc="addOverlay()"><span class="text">Out-Of-Stock</span></t>
                </div>
            </t>
        </xpath>

        <xpath expr="//div[hasclass('product-img')]" position="after">
            <t t-if="(props.product.type == 'product' and env.pos.config.display_stock)">
                <span t-attf-class="quantity-count {{props.product.get_location_qty() lte env.pos.config.minimum_stock_alert ? 'warn' : 'normal'}}">
<!--                    <t t-esc="props.product.qty_available"/>-->
                    <t t-esc="props.product.get_location_qty()"/>
                </span>
            </t>
        </xpath>

        <xpath expr="//div[hasclass('product-content')]" position="before">
            <t t-if="!props.product.image_128">
            <t t-if="( (props.product.type == 'product') and (env.pos.config.allow_order_when_product_out_of_stock == false) and props.product.get_location_qty() lte 0)">
                <div class="overlay">
                    <t t-esc="addOverlay()"><span class="text"/></t>
                </div>
            </t>
            </t>
        </xpath>

        <xpath expr="//div[hasclass('product-content')]" position="after">
            <t t-if="!props.product.image_128">
            <t t-if="(props.product.type == 'product' and env.pos.config.display_stock)">
                <span role="img" aria-label="Info" title="Info" class="product-info-button fa fa-info-circle"
                   t-on-click.stop="() => this.onProductInfoClick()"
                />
                <span style="margin-left: 30px;" t-attf-class="quantity-count {{props.product.get_location_qty() lte env.pos.config.minimum_stock_alert ? 'warn' : 'normal'}}">
                    <t t-esc="props.product.get_location_qty()"/>
                </span>
            </t>
            </t>
        </xpath>
    </t>
</templates>