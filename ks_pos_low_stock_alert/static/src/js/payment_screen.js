odoo.define("ks_pos_low_stock_alert.payment_screen", function (require) {
    "use strict";

    const PaymentScreen = require('point_of_sale.PaymentScreen');
    const Registries = require('point_of_sale.Registries');
    var { PosGlobalState, Order, Product } = require('point_of_sale.models');


    const PosUUIDPaymentScreen = PaymentScreen => class extends PaymentScreen {
        //@Override
        async validateOrder(isForceValidate) {
            var self = this.currentOrder;
            var company = self.pos.company;
            var order = this.env.pos.get_order();

            await super.validateOrder(...arguments);
                self.orderlines.forEach(mrm_line => {
                    // decrease on hand qty normal product
                    this.env.pos.quant_by_product[mrm_line.product.id] -= mrm_line.quantity;
                    // decrease BOM Product
                    var bom_id = this.env.pos.bom_by_product[mrm_line.product.product_tmpl_id];
                    if (bom_id != undefined){
                        var lines = this.env.pos.line_by_bom[bom_id.id]
                        lines.forEach(line => {
//                            console.log("linezzzzzzzzzzzzzzzzzzzz")
//                            console.log(line)
                            this.env.pos.quant_by_product[line.product_id] -= (mrm_line.quantity * line.product_qty);
                        })
                    }
                    // decrease Product effect in other bom
                    var efProducts = this.env.pos.product_in_bom[mrm_line.product.id]
//                    console.log("linezzzzzzzvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvz")
//                    console.log(efProducts)
                    efProducts.forEach(product => {
                        this.env.pos.quant_by_product[product.product] -= Math.ceil(mrm_line.quantity / product.line_qty);
                    })
                });
        }

    };

    Registries.Component.extend(PaymentScreen, PosUUIDPaymentScreen);

    return PosUUIDPaymentScreen;

});
