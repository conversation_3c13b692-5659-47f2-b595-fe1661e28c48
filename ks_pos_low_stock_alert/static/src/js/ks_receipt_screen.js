//odoo.define('ks_pos_low_stock_alert.ks_receipt_screen', function (require) {
//    "use strict";
//    const ReceiptScreen = require('point_of_sale.ReceiptScreen');
//    const { is_email } = require('web.utils');
//    const { useErrorHandlers } = require('point_of_sale.custom_hooks');
//    const Registries = require('point_of_sale.Registries');
//    const AbstractReceiptScreen = require('point_of_sale.AbstractReceiptScreen');
//
//    const { onMounted, useRef, status } = owl;
//
//    const receipt_screen = (ReceiptScreen) =>
//        class extends ReceiptScreen {
//
//            orderDone() {
////                this.currentOrder.orderlines.forEach(mrm_line => {
//                    // decrease on hand qty normal product
////                    this.env.pos.db.product_by_id[mrm_line.product.id].bi_on_hand -= mrm_line.quantity;
////                    this.env.pos.quant_by_product[mrm_line.product.id] -= mrm_line.quantity;
//                    // decrease BOM Product
////                    var bom_id = this.env.pos.bom_by_product[mrm_line.product.product_tmpl_id];
////                    if (bom_id != undefined){
////                        var lines = this.env.pos.line_by_bom[bom_id.id]
////                        lines.forEach(line => {
////                            console.log("linezzzzzzzzzzzzzzzzzzzz")
////                            console.log(line)
////                            this.env.pos.quant_by_product[line.product_id] -= (mrm_line.quantity * line.product_qty);
////                        })
////                    }
////                     decrease Product effect in other bom
////                    var efProducts = this.env.pos.product_in_bom[mrm_line.product.id]
////                    efProducts.forEach(product => {
////                        this.env.pos.quant_by_product[product.product] -= Math.ceil(mrm_line.quantity / product.line_qty);
////                    })
//
////                    console.log("ttttttttttttttttttttttttttttttxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx")
////                    console.log(mrm_line.product.product_tmpl_id)
////                    console.log(this.env.pos)
////                    console.log("ttttttttttttttttttttttttttttttxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx")
////                    console.log(this.env.pos.bom_by_product[55191])
////                    console.log(this.env.pos.line_by_bom[this.env.pos.bom_by_product[55191].id])
////                    console.log("ttttttttttttttttttttttttttttttxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx")
////                    console.log(this.env.pos.bom_by_product[mrm_line.product.product_tmpl_id])
////                });
//
//                this.env.pos.removeOrder(this.currentOrder);
//                this._addNewOrder();
//                const { name, props } = this.nextScreen;
//                this.showScreen(name, props);
//                if (this.env.pos.config.iface_customer_facing_display) {
//                    this.env.pos.send_current_order_to_customer_facing_display();
//                }
////                console.log("rrrrrrrrrrrrxxxxxxxxxxxxrrrrrrrrrrr")
////                console.log(this.env.pos.is_sync)
////                this.env.pos.is_sync = true
////                console.log(this.env.pos.is_sync)
////                console.log("ooooooooooooooooooooooooooo")
//            }
//
//    };
//
//    Registries.Component.extend(ReceiptScreen,receipt_screen);
//
//    return ReceiptScreen;
//    });