odoo.define('ks_pos_low_stock_alert.ks_low_stock', function (require) {
    "use strict";

    var ks_models = require('point_of_sale.models');
    var { PosGlobalState, Order, Product } = require('point_of_sale.models');
    const KsPaymentScreen = require('point_of_sale.PaymentScreen');
    const ks_utils = require('ks_pos_low_stock_alert.utils');
    const Registries = require('point_of_sale.Registries');
    var rpc = require('web.rpc');
    var { Gui } = require('point_of_sale.Gui');
    var core = require('web.core');
    var _t = core._t;
    const { format } = require('web.field_utils');
    const { round_precision: round_pr } = require('web.utils');
    var field_utils = require('web.field_utils');

    const PosQuantPosGlobalState = (PosGlobalState) => class PosQuantPosGlobalState extends PosGlobalState {
        constructor(obj) {
            super(obj);
            this.quant = [];
            this.bom = [];
            this.bomline = [];
            this.quant_by_product = {};
            this.bom_by_product = {};
            this.line_by_bom = {};
            this.product_in_bom = {};
        }
        async _processData(loadedData) {
            await super._processData(...arguments);
            this.quant = loadedData['stock.quant'];
            this.bom = loadedData['mrp.bom'];
            this.bomline = loadedData['mrp.bom.line'];
            this.quant_by_product = loadedData['quant_by_product'];
            this.bom_by_product = loadedData['bom_by_product'];
            this.line_by_bom = loadedData['line_by_bom'];
            this.product_in_bom = loadedData['product_in_bom'];
        }
    }
    Registries.Model.extend(PosGlobalState, PosQuantPosGlobalState);


    const PosQuantPosProduct = (Product) => class PosQuantPosProduct extends Product {
        get_location_qty() {
            var productId = this.id;
            if(!productId){
                return 0;
            }
            if(!this.pos){
                return 0;
            }
            var qtyy = this.pos.quant_by_product[productId]
            if(qtyy == undefined){
                return 0;
            }
//            return this.pos.quant_by_product[productId];
            return (this.pos.quant_by_product[productId]).toFixed(2);
        }
    }
    Registries.Model.extend(Product, PosQuantPosProduct);

});