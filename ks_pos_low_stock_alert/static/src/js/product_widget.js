//odoo.define('ks_pos_low_stock_alert.ProductsWidget', function (require) {
//    "use strict";
//
//    const Registries = require('point_of_sale.Registries');
//	const ProductsWidget = require('point_of_sale.ProductsWidget');
//
//	let prd_list_count = 0;
//
//	const BiProductsWidget = (ProductsWidget) =>
//		class extends ProductsWidget {
////			constructor() {
////				super(...arguments);
////			}
////
////			mounted() {
////				super.mounted();
////				this.env.pos.on('change:is_sync', this.render, this);
////			}
//			onWillUnmount() {
//			    this.env.pos.is_sync = false;
//				super.onWillUnmount();
////				this.env.pos.off('change:is_sync', null, this);
//
//			}
//
//			_switchCategory(event) {
//				this.env.pos.is_sync = true;
//				super._switchCategory(event);
//			}
////
////			get is_sync() {
////				return this.env.pos.get('is_sync');
////			}
//
//			get productsToDisplay() {
//
//				let self = this;
//				let location;
//				let prods = super.productsToDisplay;
//				console.log("ooooooooooooooooooooooooooooooooooooooooooooooooooo")
//				console.log(self.productsToDisplay)
//				let prod_ids = [];
//				let x_sync = self.env.pos.is_sync;
//
//                if(self.env.pos.config.picking_type_id){
//                    location = self.env.pos.config.picking_type_id[0];
//                }else{
//                    location = false;
//                }
//
////				if(x_sync == true || !("bi_on_hand" in prods) || !("bi_available" in prods)){
//				if(x_sync == true || prods[0].bi_on_hand == undefined || prods[0].bi_available == undefined ){
//                    console.log("uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu")
//				    $.each(prods, function( i, prd ){
//                        prod_ids.push(prd.id)
//                    });
//                    this.rpc({
//                        model: 'stock.quant',
//                        method: 'get_products_stock_location_qty',
//                        args: [1, location,prod_ids],
//                    }).then(function(output) {
//                        self.env.pos.loc_onhand = output[0];
//                        console.log("self.env.pos.loc_onhand")
//                        console.log(self.env.pos.loc_onhand)
//                        $.each(prods, function( i, prd ){
//
////                            prd['bi_on_hand'] = 0;
////                            prd['bi_available'] = 0;
//
//                            prd.bi_on_hand = 0;
//                            prd.bi_available = 0;
//                            for(let key in self.env.pos.loc_onhand){
//                                if(prd.id == key){
//                                    prd['bi_on_hand'] = self.env.pos.loc_onhand[key];
//                                    prd['updated_price'] = self.env.pos.loc_onhand[key];
//                                }
//                            }
//                        });
//                        self.env.pos.is_sync = false;
//                    });
//                }
//
////
//                console.log("rrrrrrrrrrrrrrrrrrrrrrrr")
////                console.log(location)
//				return prods
//			}
//		};
//
//	Registries.Component.extend(ProductsWidget, BiProductsWidget);
//
//	return ProductsWidget;
//});