odoo.define('ks_pos_low_stock_alert.ks_product_screen', function (require) {
    "use strict";
    const KsProductScreen = require('point_of_sale.ProductScreen');
    const ks_utils = require('ks_pos_low_stock_alert.utils');
    const Registries = require('point_of_sale.Registries');
    const PaymentScreen = require('point_of_sale.PaymentScreen');
//    const ClientListScreen = require('point_of_sale.ClientListScreen');
    const PosComponent = require('point_of_sale.PosComponent');
    const NumberBuffer = require('point_of_sale.NumberBuffer');
    var models = require('point_of_sale.models');
    var rpc = require('web.rpc');
    const { Gui } = require('point_of_sale.Gui');
    var core = require('web.core');
    var _t = core._t;
    const ks_product_screen = (KsProductScreen) =>
        class extends KsProductScreen {
            async _getAddProductOptions(product, base_code) {
            let price_extra = 0.0;
            let draftPackLotLines, weight, description, packLotLinesToEdit;

            if (this.env.pos.config.product_configurator && _.some(product.attribute_line_ids, (id) => id in this.env.pos.attributes_by_ptal_id)) {
                let attributes = _.map(product.attribute_line_ids, (id) => this.env.pos.attributes_by_ptal_id[id])
                                  .filter((attr) => attr !== undefined);
                let { confirmed, payload } = await this.showPopup('ProductConfiguratorPopup', {
                    product: product,
                    attributes: attributes,
                });

                if (confirmed) {
                    description = payload.selected_attributes.join(', ');
                    price_extra += payload.price_extra;
                } else {
                    return;
                }
            }

            // Gather lot information if required.
            if (['serial', 'lot'].includes(product.tracking) && (this.env.pos.picking_type.use_create_lots || this.env.pos.picking_type.use_existing_lots)) {
                const isAllowOnlyOneLot = product.isAllowOnlyOneLot();
                if (isAllowOnlyOneLot) {
                    packLotLinesToEdit = [];
                } else {
                    const orderline = this.currentOrder
                        .get_orderlines()
                        .filter(line => !line.get_discount())
                        .find(line => line.product.id === product.id);
                    if (orderline) {
                        packLotLinesToEdit = orderline.getPackLotLinesToEdit();
                    } else {
                        packLotLinesToEdit = [];
                    }
                }
                const { confirmed, payload } = await this.showPopup('EditListPopup', {
                    title: this.env._t('Lot/Serial Number(s) Required'),
                    isSingleItem: isAllowOnlyOneLot,
                    array: packLotLinesToEdit,
                });
                if (confirmed) {
                    // Segregate the old and new packlot lines
                    const modifiedPackLotLines = Object.fromEntries(
                        payload.newArray.filter(item => item.id).map(item => [item.id, item.text])
                    );
                    console.log('modifiedPackLotLines',modifiedPackLotLines)
                    const newPackLotLines = payload.newArray
                        .filter(item => !item.id)
                        .map(item => ({ lot_name: item.text }));
                    var existing_lot = await this.rpc({
                            model: 'product.product',
                            method: 'check_exist_lot',
                            args: [product,product.id,newPackLotLines[0],this.env.pos.config],
                                })
                    if (existing_lot === false){
                            Gui.showPopup('ErrorPopup', {
                            title: _t("Lot Number Error"),
                            body: _t("This Lot Number ("+newPackLotLines[0]['lot_name'] +") doesn't exist in the system."),
                        });
                        return ;
                        }
                    var lot_has_qty = await this.rpc({
                            model: 'product.product',
                            method: 'check_exist_lot_has_qty',
                            args: [product,product.id,newPackLotLines[0],this.env.pos.config, 0, false],
                                })
                    if (lot_has_qty === false){
                            Gui.showPopup('ErrorPopup', {
                            title: _t("Lot Number Error"),
                            body: _t("This Lot Number ("+newPackLotLines[0]['lot_name'] +") doesn't have enough Quantity in its allocated location."),
                        });
                        return ;
                        }


                    draftPackLotLines = { modifiedPackLotLines, newPackLotLines };
                } else {
                    // We don't proceed on adding product.
                    return;
                }
            }

            // Take the weight if necessary.
            if (product.to_weight && this.env.pos.config.iface_electronic_scale) {
                // Show the ScaleScreen to weigh the product.
                if (this.isScaleAvailable) {
                    const { confirmed, payload } = await this.showTempScreen('ScaleScreen', {
                        product,
                    });
                    if (confirmed) {
                        weight = payload.weight;
                    } else {
                        // do not add the product;
                        return;
                    }
                } else {
                    await this._onScaleNotAvailable();
                }
            }

            if (base_code && this.env.pos.db.product_packaging_by_barcode[base_code.code]) {
                weight = this.env.pos.db.product_packaging_by_barcode[base_code.code].qty;
            }

            return { draftPackLotLines, quantity: weight, description, price_extra };
        }

            _onClickPay() {
                var self = this;
                var order = self.env.pos.get_order();
                if(ks_utils.ks_validate_order_items_availability(self.env.pos.get_order(), self.env.pos.config)) {
                    var has_valid_product_lot = _.every(order.orderlines.models, function(line){
                        console.log('line pos', line)
                        return line.has_valid_product_lot();
                    });
                    if(!has_valid_product_lot){
                        self.showPopup('ConfirmPopup',{
                            'title': _t('Empty Serial/Lot Number'),
                            'body':  _t('One or more product(s) required serial/lot number.'),
                            confirm: function(){
                                self.showScreen('PaymentScreen');
                            },
                        });
                    } else{
                        this.showScreen('PaymentScreen');
                    }
                }

        }
    };

    Registries.Component.extend(KsProductScreen,ks_product_screen);

    return KsProductScreen;
    });