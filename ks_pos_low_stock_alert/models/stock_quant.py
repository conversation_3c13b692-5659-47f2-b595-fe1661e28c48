from odoo import api, fields, models

from odoo.addons.point_of_sale.models.account_payment import AccountPayment

def _get_valid_liquidity_accounts(self):
    result = super(AccountPayment, self)._get_valid_liquidity_accounts()
    return result | self.pos_payment_method_id.outstanding_account_id

AccountPayment._get_valid_liquidity_accounts = _get_valid_liquidity_accounts

class stock_quant(models.Model):
    _inherit = 'stock.quant'

    def get_products_stock_location_qty(self, pickType, products):
        res = {}

        picking_type = self.env['stock.picking.type'].sudo().browse(pickType)
        product_ids = self.env['product.product'].sudo().browse(products)
        if picking_type:
            location = picking_type.default_location_src_id
        else:
            location = False
        for product in product_ids:
            if location:
                quants = self.env['stock.quant'].sudo().search([('product_id', '=', product.id), ('location_id', '=', location.id)])
                quants = product.with_context( location=location.id).qty_available
                res.update({product.id: quants})
            else:
                res.update({product.id: product.qty_available})
        return [res]
