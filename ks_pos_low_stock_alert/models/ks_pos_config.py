#
# @Author: KSOLVES India Private Limited
# @Email: <EMAIL>
#


from odoo import api, fields, models


class ProductProduct(models.Model):
    _inherit = 'product.product'

    def check_exist_lot(self, product_id, lot_name, pos_config):
        if lot_name:
            lot_ids = self.env['stock.production.lot'].search(
                [('name', '=', lot_name['lot_name']), ('product_id', '=', product_id)])
            if lot_ids:
                return True
            else:
                return False
        else:
            return False

    def check_exist_lot_has_qty(self, product_id, lot_name, pos_config, validate_qty, is_validate):
        lot_id = False
        pos_product_id = self.env['product.product'].browse(product_id)
        pos_config_id = self.env['pos.config'].browse(pos_config['id'])
        # if not validate_qty:
        #     validate_qty=0
        if lot_name:
            if is_validate:
                valid_lot_name = lot_name
            else:
                valid_lot_name = lot_name['lot_name']
            lot_ids = self.env['stock.production.lot'].search(
                [('name', '=', valid_lot_name), ('product_id', '=', product_id)])
            if lot_ids:
                lot_id = lot_ids[0]
            else:
                return False

            available_quantity = self.env['stock.quant']._get_available_quantity(
                pos_product_id,
                pos_config_id.picking_type_id.default_location_src_id,
                lot_id=lot_id,
                strict=False,
            )
            if not is_validate:
                if available_quantity > 0:
                    return True
                else:
                    return False
            else:
                if available_quantity > 0 and available_quantity >= validate_qty:
                    return True
                else:
                    return False
        else:
            return True


class PosConfig(models.Model):
    _inherit = 'pos.config'

    display_stock = fields.Boolean(string='Display Stock of products in POS', default=True)
    minimum_stock_alert = fields.Integer(string='Minimum Limit to change the stock color for the product', default=0)
    allow_order_when_product_out_of_stock = fields.Boolean(string='Allow Order when Product is Out Of Stock',
                                                           default=True)


class PosConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    display_stock = fields.Boolean(related='pos_config_id.display_stock', readonly=False, store=True)
    minimum_stock_alert = fields.Integer(related='pos_config_id.minimum_stock_alert', readonly=False, store=True)
    allow_order_when_product_out_of_stock = fields.Boolean(related='pos_config_id.allow_order_when_product_out_of_stock', readonly=False, store=True)


class PosSession(models.Model):
    _inherit = 'pos.session'

    def _loader_params_stock_picking_type(self):
        params = super()._loader_params_stock_picking_type()
        params["search_params"]["fields"].extend(["default_location_src_id"])
        return params

    def _loader_params_product_product(self):
        res = super()._loader_params_product_product()
        res['search_params']['fields'].extend(['type', 'qty_available'])
        return res

    def _loader_params_stock_quant(self):
        return {
            'search_params': {
                'domain': [('location_id', '=', self.config_id.picking_type_id.default_location_src_id.id)],
                'fields': ['product_id', 'location_id', 'quantity'],
            },
            'context': {'active_test': False}
        }

    def _loader_params_mrp_bom(self):
        return {
            'search_params': {
                'fields': ['id', 'product_qty', 'product_tmpl_id'],
            },
            'context': {'active_test': False}
        }

    def _loader_params_mrp_bom_line(self):
        return {
            'search_params': {
                'fields': ['product_id', 'product_qty', 'bom_id'],
            },
            'context': {'active_test': False}
        }

    def _get_pos_ui_stock_quant(self, params):
        return self.env['stock.quant'].with_context(**params['context']).search_read(**params['search_params'])

    def _get_pos_ui_mrp_bom(self, params):
        return self.env['mrp.bom'].with_context(**params['context']).search_read(**params['search_params'])

    def _get_pos_ui_mrp_bom_line(self, params):
        return self.env['mrp.bom.line'].with_context(**params['context']).search_read(**params['search_params'])

    def _pos_data_process(self, loaded_data):
        super()._pos_data_process(loaded_data)
        # loaded_data['quant_by_product'] = {quant['product_id'][0]: quant for quant in loaded_data['stock.quant']}
        quants = {}
        product_in_bom = {}
        for pro in loaded_data['product.product']:
            # get qty
            product = self.env['product.product'].browse(pro['id'])
            quants[pro['id']] = product.with_context(
                location=self.config_id.picking_type_id.default_location_src_id.id).qty_available
            # check if product in bom for other one
            lines = []
            for line in self.env['mrp.bom.line'].search([('product_id', '=', pro['id'])]):
                if len(line.bom_id.product_tmpl_id.product_variant_ids):
                    lines.append({
                        'product': line.bom_id.product_tmpl_id.product_variant_ids[0].id,
                        'line_qty': line.product_qty
                    })
            product_in_bom[pro['id']] = lines
        loaded_data['quant_by_product'] = quants
        loaded_data['product_in_bom'] = product_in_bom

        loaded_data['bom_by_product'] = {bom['product_tmpl_id'][0]: bom for bom in loaded_data['mrp.bom']}

        bomLines = {}
        for bom in loaded_data['mrp.bom']:
            lines = []
            for line in self.env['mrp.bom.line'].search([('bom_id','=',bom['id'])]):
                lines.append({
                    'product_id': line.product_id.id,
                    'product_qty': line.product_qty,
                    'bom_id': line.bom_id.id
                })
            bomLines[bom['id']] = lines
        loaded_data['line_by_bom'] = bomLines

        # check if product in bom for other one
        # product_in_bom = {}
        # for pro in loaded_data['product.product']:
        #     lines = []
        #     for line in self.env['mrp.bom.line'].search([('product_id','=',pro['id'])]):
        #         lines.append(line.bom_id.product_id.id)
        #     product_in_bom[pro['id']] = lines
        # loaded_data['product_in_bom'] = product_in_bom

    @api.model
    def _pos_ui_models_to_load(self):
        models_to_load = super()._pos_ui_models_to_load()
        models_to_load.append('stock.quant')
        models_to_load.append('mrp.bom')
        models_to_load.append('mrp.bom.line')
        return models_to_load

    # def _get_pos_ui_product_product(self, params):
    #     self = self.with_context(**params['context'])
    #     if not self.config_id.limited_products_loading:
    #         products = self.env['product.product'].search_read(**params['search_params'])
    #     else:
    #         products = self.config_id.get_limited_products_loading(params['search_params']['fields'])
    #
    #     self._process_pos_ui_product_product(products)
    #     return products
