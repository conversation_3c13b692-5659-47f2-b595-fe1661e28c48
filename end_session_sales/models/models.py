# -*- coding: utf-8 -*-

from datetime import datetime

from odoo import models, fields, api, _, SUPERUSER_ID
from odoo.exceptions import ValidationError, UserError


class AccountMove(models.Model):
    _inherit = 'account.move'

    py_end_session_date = fields.Datetime(
        string='End Session Date',
        readonly=True
    )

    py_end_session_employee_id = fields.Many2one(
        string='End Session Employee',
        comodel_name='hr.employee',
        readonly=True
    )
    # system
    cash_amount = fields.Float(
        'System Cash Amount', digits='Product Price', readonly=True)
    py_total_full_amount_visa = fields.Float(
        'Total Full Visa', digits='Product Price', readonly=True)
    # actual
    py_total_amount_cash = fields.Float(
        'Total Amount Cash', digits='Product Price', readonly=True)
    py_total_amount_visa = fields.Float(
        'Total Amount Visa', digits='Product Price', readonly=True)

    # py_total_cost = fields.Float('Total Cost', digits='Product Price', readonly=True)
    # diff
    py_total_cash_computed = fields.Float(
        'Net Cash', digits='Product Price', readonly=True)
    py_total_visa_computed = fields.Float(
        'Net Visa', digits='Product Price', readonly=True)
    # total diff
    py_total_computed = fields.Float(
        'عجز وزيادة', digits='Product Price', readonly=True)

    py_is_end_session = fields.Boolean(
        'End Session', readonly=True, default=False)


class AccountPayment(models.Model):
    _inherit = 'account.payment'

    is_closed = fields.Boolean(default=False, readonly=True)

    py_end_session_entry_id = fields.Many2one(
        string='End Session Entry',
        comodel_name='account.move',
        ondelete='restrict',
        readonly=True
    )


class PosPayment(models.Model):
    _inherit = 'pos.payment'

    is_closed = fields.Boolean(default=False, readonly=True)

    py_end_session_entry_id = fields.Many2one(
        string='End Session Entry',
        comodel_name='account.move',
        ondelete='restrict',
        readonly=True
    )


class AccountAccount(models.Model):
    _inherit = 'account.account'

    is_main_treasury = fields.Boolean(default=False)

    py_crm_team_id = fields.Many2one(
        string='CRM Team',
        comodel_name='crm.team'
    )


class AccountMoveLine(models.Model):
    _inherit = 'account.move.line'

    actual_cash = fields.Float(related="move_id.py_total_amount_cash")
    system_cash = fields.Float(related="move_id.cash_amount")
    diff_cash = fields.Float(related="move_id.py_total_cash_computed")
    
    actual_visa = fields.Float(related="move_id.py_total_amount_visa")
    system_visa = fields.Float(related="move_id.py_total_full_amount_visa")
    diff_visa = fields.Float(related="move_id.py_total_visa_computed")
    py_end_session_date = fields.Datetime(related='move_id.py_end_session_date', string='End Session Date')

class EndSession(models.TransientModel):
    _name = 'end.session'
    _description = 'End Session'

    cash_amount = fields.Float(
        'System Cash Amount', digits='Product Price', readonly=True)
    visa_amount = fields.Float(
        'System Visa Amount', digits='Product Price', readonly=True)

    py_total_amount_cash = fields.Float(
        'Actual Cash Amount', digits='Product Price')
    py_total_amount_visa = fields.Float(
        'Actual Visa Amount', digits='Product Price')
    # py_total_cost = fields.Float('Total Cost', digits='Product Price')

    py_total_cash_computed = fields.Float(
        'Net Cash', digits='Product Price', readonly=True)
    py_total_visa_computed = fields.Float(
        'Net Visa', digits='Product Price', readonly=True)
    py_total_computed = fields.Float(
        'عجز وزيادة', digits='Product Price', readonly=True)

    py_team_id = fields.Many2one(
        'crm.team', 'Sales Team', compute='_compute_field_py_team_id')

    py_end_session_date = fields.Datetime(
        string='End Session Date',
        readonly=True
    )

    py_end_session_employee_id = fields.Many2one(
        string='End Session Employee',
        comodel_name='hr.employee',
        readonly=True
    )

    def _compute_field_py_team_id(self):
        for record in self:
            record.py_team_id = self.env['crm.team'].with_user(SUPERUSER_ID).search(
                [('member_ids', '=', self.env.user.id)])[:1]

    py_barcode = fields.Char(string="Code", required=True)

    is_move_created = fields.Boolean(default=False)

    note = fields.Char(readonly=True)

    def post_cash_to_treasry(self):
        self.note = ""

        employee = self.env['hr.employee'].search([('barcode', '=', self.py_barcode),
                                                   ('crm_team_id', '=', self.py_team_id.id)])

        if not employee:
            raise ValidationError(_('Wrong Code.'))

        member_ids = self.py_team_id.member_ids
        
        res = self.env['account.payment'].search(
            [('user_id','in',member_ids.ids),('is_closed', '=', False)])

        res_pos = self.env['pos.payment'].search(
            [('session_id.user_id', 'in', member_ids.ids), ('is_closed', '=', False)])

        cash_out_ids = cash_in_ids = bank_out_ids = bank_in_ids = 0
        cash_amount = visa_amount = 0

        self.note = "No payment to collect"

        if len(res) > 0 and not self.is_move_created:

            cash_out_ids = res.filtered(
                lambda x: x.journal_id.type == 'cash' and x.payment_type == 'outbound')
            cash_in_ids = res.filtered(
                lambda x: x.journal_id.type == 'cash' and x.payment_type == 'inbound')

            bank_out_ids = res.filtered(
                lambda x: x.journal_id.type == 'bank' and x.payment_type == 'outbound')
            bank_in_ids = res.filtered(
                lambda x: x.journal_id.type == 'bank' and x.payment_type == 'inbound')

            cash_amount = sum(cash_in_ids.mapped('amount')) - \
                sum(cash_out_ids.mapped('amount'))

            visa_amount = sum(bank_in_ids.mapped('amount')) - \
                sum(bank_out_ids.mapped('amount'))

        if len(res_pos) > 0 and not self.is_move_created:

            cash_out_pos_ids = res_pos.filtered(
                lambda x: x.payment_method_id.journal_id.type == 'cash' and x.amount < 0)
            cash_in_pos_ids = res_pos.filtered(
                lambda x: x.payment_method_id.journal_id.type == 'cash' and x.amount >= 0)

            bank_out_pos_ids = res_pos.filtered(
                lambda x: x.payment_method_id.journal_id.type == 'bank' and x.amount < 0)
            bank_in_pos_ids = res_pos.filtered(
                lambda x: x.payment_method_id.journal_id.type == 'bank' and x.amount >= 0)

            cash_amount += sum(cash_in_pos_ids.mapped('amount')) + \
                sum(cash_out_pos_ids.mapped('amount'))

            visa_amount += sum(bank_in_pos_ids.mapped('amount')) + \
                sum(bank_out_pos_ids.mapped('amount'))

        if len(res) > 0 or len(res_pos) > 0 and not self.is_move_created:

            self.cash_amount = cash_amount
            self.visa_amount = visa_amount

            # compute total (actual - system)
            # + self.py_total_cost
            self.py_total_cash_computed = self.py_total_amount_cash - self.cash_amount
            self.py_total_visa_computed = self.py_total_amount_visa - self.visa_amount
            # Net Visa + Net Cash
            self.py_total_computed = self.py_total_cash_computed + self.py_total_visa_computed

            ###Create Entry###
            main_treasury = self.env['account.account'].search(
                [('is_main_treasury', '=', True)])
            # ,('py_crm_team_id','=',self.py_team_id.id)

            if not main_treasury or len(main_treasury) > 1:
                raise UserError(
                    _('There are no treasury account or more than 1.'))

            if len(main_treasury) == 1:
                cash_ids = cash_in_ids if cash_in_ids else cash_out_ids
                if cash_ids:
                    values = {
                        'name': '/',
                        'date': fields.Date.today(),
                        'ref': 'End or Close Session',
                        'journal_id': self.env.ref('end_session_sales.end_session_journal').id,
                        'py_end_session_date': datetime.now(),
                        'py_end_session_employee_id': employee.id,
                        # actual
                        'py_total_amount_cash': self.py_total_amount_cash,
                        'py_total_amount_visa': self.py_total_amount_visa,
                        # new
                        # system
                        'cash_amount': self.cash_amount,
                        'py_total_full_amount_visa': self.visa_amount,
                        # diff
                        'py_total_cash_computed': self.py_total_cash_computed,
                        'py_total_visa_computed': self.py_total_visa_computed,
                        # total diff
                        'py_total_computed': self.py_total_computed,

                        # 'py_total_cost': self.py_total_cost,
                        'py_is_end_session': True,
                        'line_ids': [(0, 0, {
                                'name': 'End or Close Session',
                                'debit': self.cash_amount,
                                'account_id': main_treasury.id,
                                'partner_id': self.env.user.partner_id.id
                        }), (0, 0, {
                            'name': 'End or Close Session',
                            'credit': self.cash_amount,
                            'account_id': cash_ids[0].journal_id.default_account_id.id,
                            'partner_id': self.env.user.partner_id.id
                        })]
                    }

                    move = self.env['account.move'].create(values)

                    if len(res) > 0:
                        res.is_closed = True
                        res.py_end_session_entry_id = move

                    if len(res_pos) > 0:
                        res_pos.is_closed = True
                        res_pos.py_end_session_entry_id = move

                    self.py_end_session_date = datetime.now()
                    self.py_end_session_employee_id = employee.id

                    self.is_move_created = True
                    self.note = ""

        return {
            'context': self.env.context,
            'view_mode': 'form',
            'res_model': 'end.session',
            'res_id': self.id,
            'type': 'ir.actions.act_window',
            'target': 'new'
        }
