<odoo>
    <data>
        <template id="print_end_session_template">
            <t t-call="web.html_container">
                <t t-call="web.external_layout">
                    <div class="page" style="font_size:16pt !important;">
                        <t t-foreach="docs" t-as="doc">
                            <div class="col-md-6">
                                <br/>
                                <center>
                                    <u>تقفيل</u>
                                </center>
                                <br/>
                                <div style="font-size: 12px;direction: rtl;">
                                    <div style="float:right;width:50%">
                                        <p style="text-align: right; display:flex; flex-direction: column; align-items: center;margin-bottom: 0">

                                            تاريخ الإقفال:
                                            <span t-field="doc.py_end_session_date"/>
                                            <br/>
                                        </p>
                                    </div>
                                    <div style="float:left;width:50%">
                                        <p style="text-align: right; display:flex; flex-direction: column; align-items: center">
                                            
                                            الموظف:
                                            <span t-field="doc.py_end_session_employee_id.name"/>
                                            <br/>
                                        </p>
                                    </div>
                                </div>
                                <br/>
                                <table class="table table-bordered"
                                        style="table-layout: fixed;width:100%;direction: rtl;font-size: 12px;float: left;">
                                    <!-- <tr>
                                        <td style="text-align: center;">مبلغ نقدي للنظام</td>
                                        <td style="text-align:right;">
                                            <span t-field="doc.cash_amount"/>
                                        </td>


                                        <td style="text-align: center;">مبلغ فيزا للنظام</td>
                                        <td style="text-align:right;">
                                            <span t-field="doc.visa_amount"/>
                                        </td>

                                    </tr> -->
                                    <tr>
                                        <td style="text-align: center;">مبلغ نقدي فعلي</td>
                                        <td style="text-align:right;">
                                            <span t-field="doc.py_total_amount_cash"/>
                                        </td>


                                        <td style="text-align: center;">مبلغ فيزا فعلي</td>
                                        <td style="text-align:right;">
                                            <span t-field="doc.py_total_amount_visa"/>
                                        </td>
                                    </tr>
                                    <!-- <tr>
                                        <td style="text-align: center;">
                                            إجمالي المصاريف
                                        </td>
                                        <td style="text-align:right;">
                                            <span t-field="doc.py_total_cost"/>
                                        </td>


                                        <td style="text-align: center;"><span style="visibility: hidden;">إجمالي المصاريف</span></td>
                                        <td style="text-align:right;">
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="text-align: center;">صافي النقدي</td>
                                        <td style="text-align:right;">
                                            <span t-field="doc.py_total_cash_computed"/>
                                        </td>


                                        <td style="text-align: center;">صافي الفيزا</td>
                                        <td style="text-align:right;">
                                            <span t-field="doc.py_total_visa_computed"/>
                                        </td>
                                    </tr> -->
                                </table>
                                
                                <br/>
                                <!-- <center>
                                    عجز وزيادة:
                                    <span t-field="doc.py_total_computed"/>
                                </center> -->
                            </div>
                        </t>
                    </div>
                </t>
            </t>
        </template>
        <record id="action_report_end_session_sales" model="ir.actions.report">
            <field name="name">End Session Receipt</field>
            <field name="model">end.session</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">end_session_sales.print_end_session_template</field>
            <field name="report_file">end_session_sales.print_end_session_template</field>
<!--            <field name="paperformat_id" ref="pyramids_receipt_layout.paperformat_portrait" />-->
            <field name="binding_model_id" ref="model_end_session" />
            <field name="binding_type">report</field>
        </record>
    </data>
</odoo>