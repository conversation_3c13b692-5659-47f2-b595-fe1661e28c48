<odoo>
    <data>
        <record id="account_payment_inherit_closed" model="ir.ui.view">
            <field name="name">Account payment inherit closed</field>
            <field name="model">account.payment</field>
            <field name="inherit_id" ref="account.view_account_payment_form"/>
            <field name="arch" type="xml">
                <xpath expr="//group[@name='group2']" position="inside">
                    <field name='py_end_session_entry_id' />
                </xpath>
            </field>
        </record>
<!--        <record id="view_pos_payment_form_closed" model="ir.ui.view">-->
<!--            <field name="model">pos.payment</field>-->
<!--            <field name="inherit_id" ref="point_of_sale.view_pos_payment_form"/>-->
<!--            <field name="arch" type="xml">-->
<!--                <xpath expr="//field[@name='session_id']" position="after">-->
<!--                    <field name='py_end_session_entry_id' />-->
<!--                </xpath>-->
<!--            </field>-->
<!--        </record>-->
        <record id="account_account_inherit_closed" model="ir.ui.view">
            <field name="name">Account account inherit closed</field>
            <field name="model">account.account</field>
            <field name="inherit_id" ref="account.view_account_form"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='group_id']" position="after">
                    <field name="is_main_treasury"/>
                    <field name="py_crm_team_id"/>
                </xpath>
            </field>
        </record>
        <record id="account_move_inherit_closed" model="ir.ui.view">
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_move_form"/>
            <field name="arch" type="xml">
                <xpath expr="//notebook" position="inside">
                    <page string="Closed Session" attrs="{'invisible': [('py_is_end_session', '=', False)]}">
                        <group>
                            <group>
                                <field name="cash_amount"/>
                                <field name="py_total_amount_cash" readonly='1' force_save='1'/>
                                <!-- <field name="py_total_cost" readonly='1'/> -->
                                <field name="py_total_cash_computed" readonly='1' force_save='1'/>
                            </group>
                            <group>
                                <field name="py_total_full_amount_visa" string ='System Visa Amount' readonly='1' force_save='1'/>
                                <field name="py_total_amount_visa" readonly='1' force_save='1'/>
                                <field name="py_total_visa_computed" readonly='1' force_save='1'/>
                            </group>
                            <group>
                                <field name="py_total_computed" readonly='1' force_save='1'/>
                            </group>
                            <group>
                                <field name="py_end_session_date" readonly='1' force_save='1'/>
                                <field name="py_end_session_employee_id" readonly='1' force_save='1'/>
                                <field name="py_is_end_session" readonly='1' force_save='1'/>
                            </group>
                        </group>
<!-- 
                        <group>
                            <group>
                                <field name="py_end_session_date"/>
                                <field name="py_end_session_employee_id"/>
                            </group>
                            <group>
                                <field name="py_total_amount_cash"/>
                                <field name="py_total_amount_visa"/>
                                
                                <field name="py_total_full_amount_visa"/>
                                <field name="py_total_cost"/>
                                <field name="py_is_end_session"/>
                            </group>
                        </group> -->
                    </page>
                </xpath>

                <xpath expr="//field[@name='line_ids']//tree/field[@name='credit']" position="after">
                    <field name="actual_cash" optional="show" readonly='1' force_save='1' attrs="{'invisible': [('parent.py_is_end_session', '=', False)]}"/>
                    <field name="system_cash" optional="show" readonly='1' force_save='1' attrs="{'invisible': [('parent.py_is_end_session', '=', False)]}"/>
                    <field name="diff_cash" optional="show" readonly='1' force_save='1' attrs="{'invisible': [('parent.py_is_end_session', '=', False)]}"/>
                </xpath>
            </field>
        </record>

        <record id="account_move_line_inherit_closed" model="ir.ui.view">
            <field name="model">account.move.line</field>
            <field name="inherit_id" ref="account.view_move_line_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='credit']" position="after">
                    <field name="actual_cash" optional="show" readonly='1' force_save='1'/>
                    <field name="system_cash" optional="show" readonly='1' force_save='1'/>
                    <field name="diff_cash" optional="show" readonly='1' force_save='1'/>
                    <field name="actual_visa" optional="show" readonly='1' force_save='1'/>
                    <field name="system_visa" optional="show" readonly='1' force_save='1'/>
                    <field name="diff_visa" optional="show" readonly='1' force_save='1'/>
                </xpath>

                <xpath expr="//field[@name='move_id']" position="after">
                    <field name="py_end_session_date"/>
                </xpath>

            </field>
        </record>
        <record id="end_sesion_form_view" model="ir.ui.view">
            <field name="name">end_sesion view</field>
            <field name="model">end.session</field>
            <field name="arch" type="xml">
                <form string="End Sesion">
                    <field name="is_move_created" invisible="1"/>
                    <group>
                        <group>
                            <field name="py_total_amount_cash" attrs="{'readonly': [('is_move_created', '=', True)]}"/>
                            <field name="py_total_amount_visa" attrs="{'readonly': [('is_move_created', '=', True)]}"/>
                            <field name="py_barcode" password="1"/>
                            <field name="note" attrs="{'invisible': [('note','=',False)]}"/>
                        </group>
                    </group>
                    <group groups="account.group_account_manager">
                        <group>
                            <field name="cash_amount"/>
                            <!-- attrs="{'readonly': [('is_move_created', '=', True)]}" -->
                            <!-- <field name="py_total_cost" readonly='1'/> -->
                            <field name="py_total_cash_computed"/>
                        </group>
                        <group>
                            <field name="visa_amount"/>
                            <field name="py_total_amount_visa" attrs="{'readonly': [('is_move_created', '=', True)]}"/>
                            <field name="py_total_visa_computed"/>
                        </group>
                        <group>
                            <field name="py_total_computed"/>
                        </group>
                        <group>
                            <field name="py_end_session_date"/>
                            <field name="py_end_session_employee_id"/>
                        </group>
                        <!-- <field name="note" attrs="{'invisible': [('note','=',False)]}"/> -->
                        <field name="is_move_created" invisible='1'/>
                        <!-- <field name="py_barcode" password="1"/> -->
                    </group>
                    <footer>
                        <button name="post_cash_to_treasry" string="Post" type="object" class="oe_highlight" attrs="{'invisible': [('is_move_created','=',True)]}"/>
                        <button name="%(end_session_sales.action_report_end_session_sales)d" string="Print" type="action" class="oe_highlight" attrs="{'invisible': [('is_move_created','=',False)]}"/>
                        <button string="Cancel" class="oe_highlight" special="cancel" />
                    </footer>
                </form>
            </field>
        </record>
        <record model="ir.actions.act_window" id="action_end_sales_session">
            <field name="name">End Session</field>
            <field name="res_model">end.session</field>
            <!-- <field name="view_type">form</field> -->
            <field name="view_mode">form</field>
            <field name="view_id" ref="end_sesion_form_view"/>
            <field name="target">new</field>
        </record>
        <!-- <record model="ir.actions.server" id="action_end_sales_session">
      <field name="name">End&amp;Close session</field>
      <field name="model_id" ref="model_account_payment"/>
      <field name="type">ir.actions.server</field>
      <field name="state">code</field>
      <field name="code">model.end_session()</field>
    </record> -->
        <menuitem id="menu_end_sales_session_payments" name="End&amp;Close session" action="action_end_sales_session" parent="account.menu_finance_receivables" sequence="500"/>
    </data>
</odoo>
