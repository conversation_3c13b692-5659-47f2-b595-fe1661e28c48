<odoo>
  <data>

    <record id="action_report_whole_transfer_layout" model="ir.actions.report">
      <field name="name">Whole Sale</field>
      <field name="model">whole.transfer</field>
      <field name="report_type">qweb-pdf</field>
      <field name="report_name">whole_transfer.print_whole_transfer_template</field>
      <field name="report_file">whole_transfer.print_whole_transfer_template</field>
      <field name="binding_model_id" ref="whole_transfer.model_whole_transfer" />
      <field name="binding_type">report</field>
    </record>

    <template id="print_whole_transfer_template">
      <t t-call="web.html_container">
          <t t-call="web.external_layout">
            <t t-foreach="docs" t-as="doc">
              <!-- <t t-if="doc.stage_id.show_quotation or doc.stage_id.is_won"> -->

                <t t-set="doc" t-value="doc.with_context(lang=doc.partner_id.lang)"/>

                <t t-set="address">
                  <div t-field="doc.partner_id" t-options="{&quot;widget&quot;: &quot;contact&quot;, &quot;fields&quot;: [&quot;address&quot;, &quot;name&quot;], &quot;no_marker&quot;: True}"/>
                  <p t-if="doc.partner_id.vat"><t t-esc="doc.company_id.account_fiscal_country_id.vat_label or 'Tax ID'"/>: <span t-field="doc.partner_id.vat"/></p>
                </t>

                <div class="page">
                  <div class="oe_structure"/>

                  <h2 class="mt16">
                    <span t-field="doc.name"/>
                  </h2>

                  <div class="row mt32 mb32" id="informations">
                    
                    <div t-if="doc.date_order" class="col-auto col-3 mw-100 mb-2">
                        <strong>Order Date:</strong>
                        <p class="m-0" t-field="doc.date_order"/>
                    </div>

                    <div t-if="doc.user_id.name" class="col-auto col-3 mw-100 mb-2">
                        <strong>Salesperson:</strong>
                        <p class="m-0" t-field="doc.user_id"/>
                    </div>
                  </div>
                  <t t-set="display_discount" t-value="any(l.discount for l in doc.order_line)"/>

                  <table class="table table-sm o_main_table">
                    <!-- In case we want to repeat the header, remove "display: table-row-group" -->
                    <thead style="display: table-row-group">
                        <tr>
                            <th name="th_description" class="text-left">Description</th>
                            <th name="th_quantity" class="text-right">Quantity</th>
                            <th name="th_priceunit" class="text-right">Unit Price</th>
                            <th name="th_discount" t-if="display_discount" class="text-right" groups="product.group_discount_per_so_line">
                                <span>Disc.%</span>
                            </th>
                            <!-- <th name="th_taxes" class="text-right">Taxes</th> -->
                            <th name="th_subtotal" class="text-right">
                                <span groups="account.group_show_line_subtotals_tax_excluded">Amount</span>
                                <span groups="account.group_show_line_subtotals_tax_included">Total Price</span>
                            </th>
                        </tr>
                    </thead>
                    <tbody class="transfer_tbody">
    
                        <t t-set="current_subtotal" t-value="0"/>
    
                        <t t-foreach="doc.order_line" t-as="line">
    
                            <t t-set="current_subtotal" t-value="current_subtotal + line.price_subtotal" groups="account.group_show_line_subtotals_tax_excluded"/>
                            <t t-set="current_subtotal" t-value="current_subtotal + line.price_total" groups="account.group_show_line_subtotals_tax_included"/>
    
                            <tr t-att-class="'bg-200 font-weight-bold o_line_section' if line.display_type == 'line_section' else 'font-italic o_line_note' if line.display_type == 'line_note' else ''">
                                <t t-if="not line.display_type">
                                    <td name="td_name"><span t-field="line.name"/></td>
                                    <td name="td_quantity" class="text-right">
                                        <span t-field="line.product_uom_qty"/>
                                        <span t-field="line.product_uom"/>
                                    </td>
                                    <td name="td_priceunit" class="text-right">
                                        <span t-field="line.price_unit"/>
                                    </td>
                                    <td t-if="display_discount" class="text-right" groups="product.group_discount_per_so_line">
                                        <span t-field="line.discount"/>
                                    </td>
                                    <!-- <td name="td_taxes" class="text-right">
                                        <span t-esc="', '.join(map(lambda x: (x.description or x.name), line.tax_id))"/>
                                    </td> -->
                                    <td name="td_subtotal" class="text-right o_price_total">
                                        <span t-field="line.price_subtotal" groups="account.group_show_line_subtotals_tax_excluded"/>
                                        <span t-field="line.price_total" groups="account.group_show_line_subtotals_tax_included"/>
                                    </td>
                                </t>
                                <t t-if="line.display_type == 'line_section'">
                                    <td name="td_section_line" colspan="99">
                                        <span t-field="line.name"/>
                                    </td>
                                    <t t-set="current_section" t-value="line"/>
                                    <t t-set="current_subtotal" t-value="0"/>
                                </t>
                                <t t-if="line.display_type == 'line_note'">
                                    <td name="td_note_line" colspan="99">
                                        <span t-field="line.name"/>
                                    </td>
                                </t>
                            </tr>
    
                            <t t-if="current_section and (line_last or doc.order_line[line_index+1].display_type == 'line_section')">
                                <tr class="is-subtotal text-right">
                                    <td name="td_section_subtotal" colspan="99">
                                        <strong class="mr16">Subtotal</strong>
                                        <span t-esc="current_subtotal" t-options="{&quot;widget&quot;: &quot;monetary&quot;, &quot;display_currency&quot;: doc.pricelist_id.currency_id}"/>
                                    </td>
                                </tr>
                            </t>
                        </t>
                    </tbody>
                  </table>

                  <div class="clearfix" name="so_total_summary">
                    <div id="total" class="row" name="total">
                      <div t-attf-class="#{'col-4' if report_type != 'html' else 'col-sm-7 col-md-5'} ml-auto">
                        <table class="table table-sm">
                          <tr class="border-black o_total">
                            <td><strong>Total</strong></td>
                            <td class="text-right">
                                <span t-esc="doc.amount_total"/>
                            </td>
                        </tr>                        </table>
                      </div>
                    </div>
                  </div>
    
                  <div class="oe_structure"/>

                </div>
              </t>  
            <!-- </t> -->
          </t>
      </t>
    </template>


  </data>
</odoo>