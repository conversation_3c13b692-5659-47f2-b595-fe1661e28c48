# -*- coding: utf-8 -*-

from collections import defaultdict
from odoo import models, fields, api, _, SUPERUSER_ID
from datetime import datetime, timedelta
from odoo.exceptions import AccessError, UserError, ValidationError
from operator import itemgetter
from itertools import groupby


PAYMENT_STATE_SELECTION = [
    ('not_paid', 'Not Paid'),
    ('paid', 'Paid'),
    ('partial', 'Partially Paid')
]


class WholeTransfer(models.Model):
    _name = 'whole.transfer'
    _inherit = ['portal.mixin', 'mail.thread',
                'mail.activity.mixin', 'utm.mixin']
    _description = "Whole Transfer"
    _order = 'name, id'

    @api.model
    def _get_default_team(self):
        return self.env['crm.team']._get_default_team_id(self.env.user.id)

    name = fields.Char(
        'Reference', default='/',
        copy=False, index=True, readonly=True)

    note = fields.Html('Notes')

    date_order = fields.Datetime(string='Order Date', required=True, readonly=True, index=True, states={'draft': [('readonly', False)], 'sent': [(
        'readonly', False)]}, copy=False, default=fields.Datetime.now, help="Creation date of draft/sent orders,\nConfirmation date of confirmed orders.")

    state = fields.Selection([
        ('draft', 'Draft'),
        ('done', 'Locked'),
        ('cancel', 'Cancelled'),
    ], string='Status', readonly=True, copy=False, index=True, tracking=3, default='draft')

    # warehouse_id = fields.Many2one('stock.warehouse', domain="[('parent_warehouse_id','=',False)]")

    is_transfer_in = fields.Boolean()

    is_transfer_out = fields.Boolean()

    # group_location_id = fields.Many2one('group.location')

    src_group_location_id = fields.Many2one('group.location', required=True)
    dest_group_location_id = fields.Many2one('group.location', required=True)

    # picking_type_id = fields.Many2one(
    #     'stock.picking.type', 'Operation Type',
    #     required=True, readonly=True,
    #     states={'draft': [('readonly', False)]},
    #     domain="[('code','=','internal')]")

    order_line = fields.One2many('whole.transfer.line', 'order_id', string='Order Lines', states={
                                 'cancel': [('readonly', True)], 'done': [('readonly', True)]}, copy=True, auto_join=True)

    user_id = fields.Many2one(
        'res.users', string='Salesperson', index=True, tracking=2, copy=False, default=lambda self: self.env.user,
        domain=lambda self: [('groups_id', 'in', self.env.ref('sales_team.group_sale_salesman').id)])

    team_id = fields.Many2one(
        'crm.team', 'Sales Team',
        ondelete="set null", tracking=True, copy=False,
        change_default=True, default=_get_default_team)
    picking_ids = fields.Many2many('stock.picking', copy=False)

    @api.onchange('is_transfer_in', 'is_transfer_out')
    def add_default_operation_type(self):
        for this in self:
            # crm = self.env['crm.team'].search([('member_ids','=',self.env.user.id)])

            # if len(crm)>0 :
            # warehouse_id = self.env['stock.warehouse'].search([('crm_team_id','=',this.team_id.id)])
            # if len(warehouse_id)>0 :
            if this.is_transfer_in:
                # this.picking_type_id = self.env['stock.picking.type'].search([('code','=','internal'),('warehouse_id','=', warehouse_id[0].id)])
                # this.location_dest_id = this.picking_type_id.default_location_dest_id.id
                this.dest_group_location_id = this.team_id.group_location_id
            if this.is_transfer_out:
                # this.picking_type_id = self.env['stock.picking.type'].search([('code','=','internal'),('warehouse_id','=', warehouse_id[0].id)])
                # this.location_id = this.picking_type_id.default_location_src_id.id
                this.src_group_location_id = this.team_id.group_location_id

    @api.model
    def create(self, vals):
        if 'company_id' in vals:
            self = self.with_company(vals['company_id'])
        if vals.get('name', _('New')) == _('New'):
            seq_date = None
            if 'date_order' in vals:
                seq_date = fields.Datetime.context_timestamp(
                    self, fields.Datetime.to_datetime(vals['date_order']))
            vals['name'] = self.env['ir.sequence'].next_by_code(
                'whole.transfer', sequence_date=seq_date) or _('New')

        result = super(WholeTransfer, self).create(vals)
        return result

    def action_confirm(self):
        if len(self.env.companies) == 1:
            raise ValidationError(_('2 Company must be selected'))
            
        if len(self.order_line) == 0:
            raise ValidationError('No lines to create orders')

        # if self.src_group_location_id == self.dest_group_location_id:
        #     raise ValidationError(
        #         'Src and Dest group location can\'t be same.')

        # create picking internal transfer

        requisitions_values_by_location_src_dest = defaultdict(list)

        locations_level1_src = self.src_group_location_id.location_ids.mapped(
            'location_id')

        locations_level2_src = self.src_group_location_id.location_ids.mapped(
            'location_id.location_id')

        #From MS Then AW
        locations_src = (locations_level1_src + locations_level2_src).filtered(
            lambda loc: loc.usage == 'view' and loc.location_id).sorted(key=lambda loc: loc.company_id.id, reverse=True)

        locations_level1_dest = self.dest_group_location_id.location_ids.mapped(
            'location_id')

        locations_level2_dest = self.dest_group_location_id.location_ids.mapped(
            'location_id.location_id')

        locations_dest = (locations_level1_dest + locations_level2_dest).filtered(
            lambda loc: loc.usage == 'view' and loc.location_id)

        products_needed = self.order_line.mapped(lambda x: [
            x.product_id, x.product_uom_qty, x.product_uom])

        new_products_needed = products_needed.copy()

        picking_types = self.env['stock.picking.type'].search(
                                [('code', '=', 'internal'), ('default_location_dest_id', 'in', locations_dest.ids)])

        for location_id in locations_src:

            locations_dest_current = locations_dest.filtered(
                lambda loc: loc.company_id == location_id.company_id)
            picking_type = picking_types.filtered(
                lambda loc: loc.default_location_dest_id == locations_dest_current)

            if not locations_dest_current:
                raise ValidationError(
                    "No location with same company of " + str(locations_dest_current.name))
            if not picking_type:
                raise ValidationError(
                    "Check if the destination location in any operation type")

            for product in products_needed:
                product_qty_needed_from_location = 0
                product_id = product[0]
                product_qty = product[1]
                product_uom = product[2]
                have_qty = True

                quants_all = product_id.with_context(
                    {'location': location_id.id}).free_qty

                if quants_all >= product_qty:
                    product_qty_needed_from_location = product_qty
                    new_products_needed.remove(product)
                elif quants_all > 0:
                    product_qty_needed_from_location = quants_all
                    new_products_needed.remove(product)
                    new_products_needed.append(
                        [product_id, product_qty - quants_all, product_uom])
                elif location_id.company_id.company_rank == 'first':
                    # Has no qty and loop in MS location
                    product_qty_needed_from_location = product_qty
                    new_products_needed.remove(product)
                    have_qty = False

                else:
                    continue

                values = {
                    'name': product_id.name,
                    'product_id': product_id.id,
                    'description_picking': 'get ' + product_id.display_name,
                    'product_uom': product_uom.id,
                    'product_uom_qty': product_qty_needed_from_location,
                    'location_id': location_id.id,
                    'location_dest_id': locations_dest_current.id,
                    'picking_type_id': picking_type.id,
                    'company_id': location_id.company_id.id,
                    'origin': self.name,
                    'partner_id': False
                }

                requisitions_values_by_location_src_dest['{}_{}_{}_{}'.format(
                    'have_qty' if have_qty else 'have_no_qty', location_id.id, locations_dest_current.id, picking_type.id)].append(values)

            products_needed = new_products_needed.copy()

        for location_info, move_values in requisitions_values_by_location_src_dest.items():

            move_ids = self.env['stock.move'].with_user(
                SUPERUSER_ID).create(move_values)
            location_split = location_info.replace(
                'have_qty_', '').replace('have_no_qty_', '').split('_')

            new_picking = self.env['stock.picking'].create({
                'location_id': int(location_split[0]),
                'location_dest_id': int(location_split[1]),
                'picking_type_id': int(location_split[2]),
                'whole_transfer_id':self.id,
                'is_locked': False
            })

            move_ids.write({'picking_id': new_picking.id})

            self.picking_ids = [(4, new_picking.id)]
            self.picking_ids.action_confirm()

        self.state = 'done'
        return True

    def button_open_transfers(self):
        ''' Redirect the user to this order transfers.
        :return:    An action on stock.picking.
        '''
        self.ensure_one()
        return {
            'name': _("Transfers"),
            'type': 'ir.actions.act_window',
            'res_model': 'stock.picking',
            'context': {'create': False},
            'view_mode': 'list,form',
            'domain': [('id', 'in', self.picking_ids.ids)],
        }

    @api.onchange('order_line')
    def _check_lines(self):
        if self.order_line:
            last_line = self.order_line[-1]
            filtered = self.order_line.filtered(
                lambda x: x.product_id == last_line.product_id)
            if len(filtered) > 1:
                raise ValidationError(_('This Product Exists in lines'))


class WholeTransferLine(models.Model):
    _name = 'whole.transfer.line'
    _description = "Whole Transfer Line"
    _order = 'order_id, sequence, id'

    order_id = fields.Many2one('whole.transfer')
    sequence = fields.Integer(string='Sequence', default=10)

    # name = fields.Text(string='Description', required=True)

    product_id = fields.Many2one('product.product')

    product_uom_qty = fields.Float(
        string='Quantity', digits='Product Unit of Measure', required=True, default=1.0)
    product_uom_category_id = fields.Many2one(
        related='product_id.uom_id.category_id')
    product_uom = fields.Many2one('uom.uom', string='Unit of Measure',
                                  domain="[('category_id', '=', product_uom_category_id)]", ondelete="restrict")

    state = fields.Selection(related="order_id.state")

    @api.onchange('product_id')
    def product_id_change(self):
        if not self.product_id:
            return

        vals = {}
        if not self.product_uom or (self.product_id.uom_id.id != self.product_uom.id):
            vals['product_uom'] = self.product_id.uom_id
            vals['product_uom_qty'] = self.product_uom_qty or 1.0
        self.update(vals)

    src_qty = fields.Float(compute='get_onhand_qty')
    dest_qty = fields.Float(compute='get_onhand_qty')

    @api.onchange('product_id', 'order_id.src_group_location_id', 'order_id.dest_group_location_id')
    def get_onhand_qty(self):
        for this in self:

            this.src_qty = 0
            this.dest_qty = 0

            if this.order_id.src_group_location_id:
                qty = 0
                for location in this.order_id.src_group_location_id.location_ids:
                    qty += this.product_id.with_context(
                        {'location': location.id}).free_qty
                this.src_qty = qty

            if this.order_id.dest_group_location_id:
                qty = 0
                for location in this.order_id.dest_group_location_id.location_ids:
                    qty += this.product_id.with_context(
                        {'location': location.id}).free_qty
                this.dest_qty = qty
