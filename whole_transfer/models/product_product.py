# -*- coding: utf-8 -*-
from odoo import models, fields, api, _
from odoo.tools.misc import get_lang
from odoo.exceptions import UserError


class ProductProduct(models.Model):
    _inherit = 'product.product'

    
    # def name_get(self):
    #     result = super(ProductProduct, self).name_get()
    #     new_result = []
    #     src_group_id = self._context.get('group_from')
    #     dest_group_id = self._context.get('group_to')

    #     if src_group_id and dest_group_id:
    #         src_group_id = self.env['group.location'].browse(src_group_id)
    #         dest_group_id = self.env['group.location'].browse(dest_group_id)

    #         for product_set in result:
    #             product_set = list(product_set)

    #             product = self.browse(product_set[0])
                

    #             src_locations = src_group_id.location_ids.mapped(lambda x:x.location_id)
    #             dest_locations = dest_group_id.location_ids.mapped(lambda x:x.location_id)
                
    #             total_in_src = 0
    #             for location in src_locations:
    #                 if location.usage == 'view':
    #                     total_in_src = product.with_context({'location' : location.id}).free_qty
    #                 elif location.location_id and location.usage == 'internal':
    #                     total_in_src = product.with_context({'location' : location.location_id.id}).free_qty


    #             total_in_dest = 0
    #             for location in dest_locations:
    #                 if location.usage == 'view':
    #                     total_in_dest = product.with_context({'location' : location.id}).free_qty
    #                 elif location.location_id and location.usage == 'internal':
    #                     total_in_dest = product.with_context({'location' : location.location_id.id}).free_qty

    #             product_set[1] = product_set[1] + ' [(%s),(%s)]' % (str(total_in_src), str(total_in_dest))
    #             product_set = tuple(product_set)

    #             new_result.append(product_set)
    #         return new_result

    #     return result
        