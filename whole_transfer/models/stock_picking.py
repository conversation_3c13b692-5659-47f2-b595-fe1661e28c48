# -*- coding: utf-8 -*-
from odoo import models, fields, api, _
from odoo.tools.misc import get_lang
from odoo.exceptions import UserError


# class StockPicking(models.Model):
#     _inherit = 'stock.picking'

#     whole_transfer_id = fields.Many2one('whole.transfer')

    # def button_validate(self):

    #     incoming_pickings = self.filtered(
    #         lambda p: p.picking_type_code == 'incoming' and
    #         self.env.user != p.location_dest_id.warehouse_id.stock_user_id)

    #     outgoing_pickings = self.filtered(
    #         lambda p: p.picking_type_code == 'outgoing' and
    #         self.env.user != p.location_id.warehouse_id.stock_user_id)

    #     if not incoming_pickings and not outgoing_pickings:
    #         if incoming_pickings:
    #             incoming_pickings.activity_ids.action_done()

    #         return super(StockPicking, self).button_validate()
    #     else:
    #         raise UserError(
    #             _("Only location responsible user can validate this."))

    # def _should_show_transfers(self):
    #     """Whether the different transfers should be displayed on the pre action done wizards."""
    #     res = super(StockPicking, self)._should_show_transfers()
         
    #     return res or self.picking_type_code == 'outgoing'

class StockBackorder(models.TransientModel):
    _inherit = 'stock.backorder.confirmation'

    picking_type_code = fields.Char(compute='get_picking_type')
    @api.depends('pick_ids')
    def get_picking_type(self):
        for this in self:
            this.picking_type_code = this.pick_ids.picking_type_code