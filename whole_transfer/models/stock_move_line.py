# -*- coding: utf-8 -*-
from odoo import models, fields, api, _
from odoo.tools.misc import get_lang
from odoo.exceptions import UserError


class StockMoveLine(models.Model):
    _inherit = 'stock.move.line'

    @api.onchange('location_id')
    def _onchange_location_from(self):

        if self._origin.location_id and self.location_id and self.env.user != self.location_id.warehouse_id.stock_user_id:
            raise UserError(_('Only source location user can change location from.'))
        elif self.picking_id.state == 'validate2':
            raise UserError(_('Can\'t edit location in second validate'))

    @api.onchange('location_dest_id')
    def _onchange_location_to(self):
        
        if self._origin.location_dest_id and self.location_dest_id and self.env.user != self.location_dest_id.warehouse_id.stock_user_id:
            raise UserError(_('Only destination location user can change location to.'))
