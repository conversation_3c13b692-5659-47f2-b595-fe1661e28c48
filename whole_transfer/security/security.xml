<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="module_category_whole_transfer" model="ir.module.category">
            <field name="description">Helps you handle your whole transfers.</field>
            <field name="name">Whole Transfer</field>
            <field name="sequence" eval="3" />   
        </record>
        <record id="group_whole_transfer_user" model="res.groups">
            <field name="name">User</field>
            <field name="category_id" ref="module_category_whole_transfer"/>
            <field name="implied_ids" eval="[(4, ref('stock.group_stock_user'))]"/>
            <field name="comment">the user will have access to transfer.</field>
        </record>
        <record id="group_whole_transfer_responsible" model="res.groups">
            <field name="name">Responsible</field>
            <field name="category_id" ref="module_category_whole_transfer"/>
            <field name="implied_ids" eval="[(4, ref('group_whole_transfer_user'))]"/>
            <field name="comment">the user will have access to transfer as responsible.</field>
        </record>
    </data>
</odoo>