from odoo import models, api, _
from odoo.exceptions import ValidationError

class HrEmployee(models.Model):
    _inherit = 'hr.employee'

    @api.constrains('bank_account_id', 'identification_id')
    def _check_employee_unique_fields(self):
        for employee in self:
            # Check bank account uniqueness
            if employee.bank_account_id:
                duplicate_bank = self.search([
                    ('bank_account_id', '=', employee.bank_account_id.id),
                    ('id', '!=', employee.id)
                ])
                if duplicate_bank:
                    raise ValidationError(
                        _("The Bank Account '%s' is already assigned to another employee!") % employee.bank_account_id.name
                    )

            # Check identification ID uniqueness
            if employee.identification_id:
                duplicate_identification = self.search([
                    ('identification_id', '=', employee.identification_id),
                    ('id', '!=', employee.id)
                ])
                if duplicate_identification:
                    raise ValidationError(
                        _("The Identification No '%s' is already assigned to another employee!") % employee.identification_id
                    )


# from odoo import models, api, _
# from odoo.exceptions import ValidationError

# class HrEmployee(models.Model):
#     _inherit = 'hr.employee'

#     @api.constrains('bank_account_id', 'identification_id')
#     def _check_unique_fields(self):
#         for employee in self:
#             # Prepare domain for search
#             domain = []
#             messages = []

#             if employee.bank_account_id:
#                 domain.append(('bank_account_id', '=', employee.bank_account_id.id))
#                 messages.append(_("The Bank Account '%s' is already assigned to another employee!"))

#             if employee.identification_id:
#                 domain.append(('identification_id', '=', employee.identification_id))
#                 messages.append(_("The Identification No '%s' is already assigned to another employee!") % employee.identification_id)

#             # Perform search
#             if domain and self.search_count(domain) > 1:
#                 # Include messages based on which fields exist
#                 raise ValidationError("\n".join(messages))

