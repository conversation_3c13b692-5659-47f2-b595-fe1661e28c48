from odoo import models, fields, api

class HrDepartment(models.Model):
    _inherit = 'hr.department'

 
    @api.model
    def create(self, vals):
        if not vals.get('ro_department_code'):
            last_dept = self.search([], order='id desc', limit=1)
            if last_dept and last_dept.ro_department_code:
                try:
                    last_code_num = int(last_dept.ro_department_code)
                    new_code = str(last_code_num + 1).zfill(3)  
                except ValueError:
                    new_code = self.env['ir.sequence'].sudo().next_by_code('department.code.sequence')
            else:
                new_code = self.env['ir.sequence'].sudo().next_by_code('department.code.sequence')
            vals['ro_department_code'] = new_code

        return super(HrDepartment, self).create(vals)
