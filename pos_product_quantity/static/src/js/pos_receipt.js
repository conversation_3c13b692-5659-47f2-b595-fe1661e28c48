odoo.define('pos_product_quantity.pos_product_quantity', function (require) {
"use strict";

var OrderSummary = require('point_of_sale.OrderSummary');
var Registries = require('point_of_sale.Registries');

const PosQtyOrderSummary = OrderSummary => class extends OrderSummary {

        getTotalqty() {
            var order    = this.env.pos.get_order();
            var lines    = order.get_orderlines();
            var qty = 0;
            for (const line of lines) {
                qty += line.quantity;
            }
            return qty;
        }
        getProductCount() {
            var order    = this.env.pos.get_order();
            var lines    = order.get_orderlines();
            var qty = 0;
            for (const line of lines) {
                qty += 1;
            }
            return qty;
        }
    };
Registries.Component.extend(OrderSummary, PosQtyOrderSummary)
return OrderSummary;
});


odoo.define('pos_product_quantity.OrderReceipt', function (require) {
    'use strict';

    const OrderReceipt = require('point_of_sale.OrderReceipt')
    const Registries = require('point_of_sale.Registries');

    const OrderReceiptGCC = OrderReceipt =>
        class extends OrderReceipt {
            getTotalqty() {
                var order    = this.env.pos.get_order();
                var lines    = order.get_orderlines();
                var qty = 0;
                for (const line of lines) {
                    qty += line.quantity;
                }
                return qty;
            }
        }
    Registries.Component.extend(OrderReceipt, OrderReceiptGCC)
    return OrderReceiptGCC
});
