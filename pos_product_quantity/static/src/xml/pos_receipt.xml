<?xml version="1.0" encoding="UTF-8"?>

<templates id="template" xml:space="preserve">
    <t t-name="OrderReceipt" t-inherit="point_of_sale.OrderReceipt" t-inherit-mode="extension" owl="1">
        <xpath expr="//t[@t-if='!isTaxIncluded']/div[hasclass('pos-receipt-right-align')][1]" position="replace">
            <div class="pos-receipt-center-align">------------------------------------------------</div>
        </xpath>
        <xpath expr="//div[hasclass('pos-receipt')]/div[3]" position="replace">
            <div class="pos-receipt-center-align">------------------------------------------------</div>
        </xpath>

        <xpath expr="//div[hasclass('pos-receipt-amount')][1]" position="replace">
            <div style="font-size: 125%;">
                <span t-if="getTotalqty()" t-esc="'QTY '+getTotalqty()" style="text-align: left;"/>
                <span t-esc="'TOTAL  ' + env.pos.format_currency(receipt.total_with_tax)" class="pos-receipt-right-align"/>
            </div>
        </xpath>
    </t>

    <t t-name="OrderSummary" t-inherit="point_of_sale.OrderSummary" t-inherit-mode="extension" owl="1">
        <xpath expr="//div[@t-if='_tax.hasTax']" position="before">
            <div t-if="getProductCount()" class="subentry">
                Total Product QTY:
                <span class="value">
                    <t t-esc="getProductCount()" />
                </span>
            </div>
        </xpath>
    </t>
</templates>