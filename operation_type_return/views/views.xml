<odoo>
  <data>
    <record id="inherit_stock_picking_type_form" model="ir.ui.view">
      <field name="name">stock.picking.type.view.form.inherit</field>
      <field name="model">stock.picking.type</field>
      <field name="inherit_id" ref="stock.view_picking_type_form"/>
      <field name="arch" type="xml">
          <xpath expr="//field[@name='code']" position="after">
              <field name="is_return" />
          </xpath>        
      </field>
    </record>

    <record id="inherit_purchase_order_deliver_to_form" model="ir.ui.view">
      <field name="name">purchase.order.view.form.inherit</field>
      <field name="model">purchase.order</field>
      <field name="inherit_id" ref="purchase_stock.purchase_order_view_form_inherit"/>
      <field name="arch" type="xml">
          <xpath expr="//field[@name='picking_type_id']" position="attributes">
              <attribute name="domain">[('code','=','incoming'),('is_return','=',False), '|', ('warehouse_id', '=', False), ('warehouse_id.company_id', '=', company_id)]</attribute>
          </xpath>        
      </field>
    </record>

  </data>
</odoo>