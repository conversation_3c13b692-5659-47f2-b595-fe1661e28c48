# -*- coding: utf-8 -*-

from datetime import datetime, timedelta
from odoo import models, fields, api, _, SUPERUSER_ID


class CashierReportPrint(models.AbstractModel):
    _name = 'report.cashier_report.cashier_report_document'

    @api.model
    def _get_report_values(self, docids, data=None):
        print(data)
        return {

            # 'doc_ids': data['ids'],
            'doc_model': data['model'],
            'start_balance': data['start_balance'],
            'all_record':self.env['account.payment'].with_user(SUPERUSER_ID).search([('id', 'in', data['all_record'])]),

            'docs': docids,
        }
