# -*- coding: utf-8 -*-

from odoo import models, fields, api, SUPERUSER_ID


class AccountPayment(models.Model):
    _inherit = 'account.payment'

    operation_type = fields.Char(string="Operation Type")

    def change_size_page(self, items):
        paper_format = self.env.ref('cashier_report.cashier_report_paperformat_portrait')
        paper_format.with_user(SUPERUSER_ID).page_height = 120 + (len(items) * 10)


class AccountJournal(models.Model):
    _inherit = 'account.journal'

    cashier_report = fields.Boolean(string="Cashier Report")