<odoo>
    <data>
      <record id="cashier_report_paperformat_portrait" model="report.paperformat">
        <field name="name">Cashier Report Layout</field>
        <field name="default" eval="True"/>
        <field name="format">custom</field>
        <field name="page_height">105</field>
        <field name="page_width">74</field>
        <field name="orientation">Portrait</field>
        <field name="margin_top">0</field>
        <field name="margin_bottom">0</field>
        <field name="margin_left">0</field>
        <field name="margin_right">0</field>
        <field name="header_line" eval="False"/>
        <field name="header_spacing">0</field>
        <field name="dpi">95</field>
      </record>

      <record id="action_cashier_report_layout" model="ir.actions.report">
          <field name="name">Cashier Report</field>
          <field name="model">account.payment</field>
          <field name="report_type">qweb-pdf</field>
          <field name="report_name">cashier_report.cashier_report_document</field>
          <field name="report_file">cashier_report.cashier_report_document</field>
          <field name="paperformat_id" ref="cashier_report.cashier_report_paperformat_portrait" />
          <field name="binding_model_id" eval="False" />
          <field name="binding_type">report</field>
      </record>


      <template id="custom_external_layout_footer_cashier_report">
          <div/>
      </template>

      <template id="custom_external_layout_cashier_report">

          <t t-if="not company">
              <t t-if="company_id">
                  <t t-set="company" t-value="company_id"/>
              </t>
              <t t-elif="o and 'company_id' in o">
                  <t t-set="company" t-value="o.company_id.sudo()"/>
              </t>
              <t t-else="else">
                  <t t-set="company" t-value="res_company"/>
              </t>
          </t>

          <div t-attf-class="o_company_#{company.id}_layout article o_report_layout_background" t-att-data-oe-model="o and o._name" t-att-data-oe-id="o and o.id" t-att-data-oe-lang="o and o.env.context.get('lang')">
              <t t-call="web.address_layout"/>
              <t t-raw="0"/>
              
          </div>
          <t t-call="cashier_report.custom_external_layout_footer_cashier_report"/>
      </template>

      <template id="cashier_report_document">
        <t t-call="web.html_container">
            <t t-call="cashier_report.custom_external_layout_cashier_report">
              <div class="page">


                <br/>
                <div class="row" style="direction:rtl">
                    <div style="width:50%;" >
                        <h5 style="float:right">التاريخ:
                            <span t-esc="all_record[0].sudo().date"/>
                        </h5>
                    </div>
                    <div style="width:50%;">
                        <img t-if="all_record[0].sudo().company_id.logo"
                            t-att-src="image_data_uri(all_record[0].sudo().company_id.logo)"
                            style="max-height: 320px;max-width: 170px;float:left" alt="Logo"/>
                    </div>
                </div>
                <div class="row">
                    <div style="width:100%">
                        <center>
                            <h3 style = "text-align:center" t-field="all_record[0].sudo().company_id.name"/>
                        </center>
                    </div>
                </div>

                <div class="row">
                    <div style="width:100%">
                        <center>
                            <h3 style = "text-align:center">
                              بيان الخزينة
                            </h3>

                            <h5 style = "text-align:center">
                              رصيد ماقبله
                              <span t-esc="start_balance"/>
                              
                            </h5>
                        </center>
                    </div>
                </div>
                <br/>

                <table class="table table-bordered " style='direction: rtl;'>
                  <thead>
                    <tr>
                        <th class="text-right" ><span>نوع العملية</span></th>
                        <th class="text-right"><span style="float:right">رقم العملية</span></th>
                        <th class="text-right"><span>مستفيد</span></th>
                        <th class="text-right"><span style="float:right">المبلغ</span></th>
                    </tr>
                  </thead>
                  <tbody>
                    <t t-set="balance" t-value="0"/>
                    <t t-foreach="all_record" t-as="line">
                      <tr>
                        <td class="text-right">
                          <t t-if="line.sudo().operation_type">
                              <span t-esc="line.sudo().operation_type"/>
                          </t>
                          <t t-else="">

                            <t t-if="line.sudo().payment_type == 'outbound'">
                                <span>ارسال</span>
                            </t>

                            <t t-if="line.sudo().payment_type == 'inbound'">
                              <span>استقبال</span>
                            </t>
                          </t>
                        </td>

                        <td class="text-right">
                          <span t-field="line.sudo().name"/>
                        </td>
                        
                        <td class="text-right">
                          <t t-if="line.sudo().writeoff_multi_acc_ids">
                            <t t-foreach="line.sudo().writeoff_multi_acc_ids" t-as="writeoff">
                              <span t-field="writeoff.sudo().writeoff_partner_id.name"/>
                            </t>
                          </t>
                          <t t-else="">
                            <span t-field="line.sudo().partner_id.name"/>
                          </t>
                        </td>

                        <td class="text-right">
                          <span style="float:right" t-field="line.sudo().amount"/>
                          <t t-if="line.sudo().payment_type == 'outbound'">
                            <t t-set="balance" t-value="balance-line.sudo().amount"/>
                          </t>

                          <t t-if="line.sudo().payment_type == 'inbound'">
                            <t t-set="balance" t-value="balance+line.sudo().amount"/>
                          </t>
                        </td>
                      </tr>
                    </t>

                  </tbody>
                </table>
                <br/>

                <div class="row">
                  <div style="width:100%">
                    <center>
                        <h5 style = "text-align:center">
                          الرصيد 
                          <span t-esc="balance"/>
                          <!-- <span t-esc="balance" t-options="{&quot;widget&quot;: &quot;monetary&quot;, &quot;display_currency&quot;: all_record[0].sudo().currency_id}"/> -->
                          <!-- <span t-esc="start_balance"/> -->
                          
                        </h5>
                    </center>
                  </div>
                </div>

                <br/>
                <div class="row">
                  <div style="width:100%">
                    <center>
                        <h5 style = "text-align:center">
                          الاجمالي 
                          <span t-esc="balance+start_balance"/>
                          <!-- <span t-esc="start_balance"/> -->
                          
                        </h5>
                    </center>
                  </div>
                </div>

                <t t-if="len(all_record)>0" t-esc="all_record[0].change_size_page(all_record)"/>

              </div>
            </t>
        </t>
      </template>
    </data>
</odoo>