<odoo>
  <data>
    <record id="add_operation_type_in_payment" model="ir.ui.view">
      <field name="model">account.payment</field>
      <field name="inherit_id" ref="account.view_account_payment_form"/>
      <field name="arch" type="xml">
        <xpath expr="//group[@name='group2']" position="inside">
          <field name="operation_type" />
        </xpath>
      </field>
    </record>

    <record id="check_is_cashier_report" model="ir.ui.view">
      <field name="model">account.journal</field>
      <field name="inherit_id" ref="account.view_account_journal_form"/>
      <field name="arch" type="xml">
        <xpath expr="//page[@name='bank_account']/group/group[1]" position="inside">
          <group>
            <field name="cashier_report"/>
          </group>
        </xpath>
      </field>
    </record>
  </data>
</odoo>