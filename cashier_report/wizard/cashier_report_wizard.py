# -*- coding: utf-8 -*-

import time
from odoo import models, fields, api, _, exceptions, SUPERUSER_ID
from odoo.exceptions import ValidationError, UserError
import datetime
import base64
from datetime import date
from io import BytesIO

class CashierReportWizard(models.TransientModel):
    _name = 'chasier.report.wizard'

    date = fields.Date(string='Start Date',default=fields.Date.context_today)
    team_id = fields.Many2one('crm.team',string='Sales Team')
    
    def get_chasier_report_report(self):
        team_id = self.team_id or self.env['crm.team'].with_user(SUPERUSER_ID).search(['|', ('member_ids', '=', self.env.user.id),('user_id', '=', self.env.user.id)])[:1]
        journal = self.env['account.journal'].with_user(SUPERUSER_ID).search([('type','=','cash'),('cashier_report','=', True),('ro_crm_team_id','=', team_id.id)])
        all_record = self.env['account.payment'].with_user(SUPERUSER_ID).search([('date', '=', self.date), ('state','=','posted'),('journal_id','in',journal.ids)])
        
        if not all_record:
            raise UserError(_('No Posted Entries'))
        data = {
            'model': self._name,
            'start_balance': journal.default_account_id.current_balance - abs(sum(all_record.mapped(lambda x:x.amount))),
            'all_record':all_record.ids
        }
        return self.env.ref('cashier_report.action_cashier_report_layout').report_action(self, data=data)
