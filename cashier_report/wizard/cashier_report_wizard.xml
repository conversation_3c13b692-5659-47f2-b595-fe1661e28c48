<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <data>
    
        <record model="ir.ui.view" id="chasier_report_wizard_form">
            <field name="name">chasier_report_wizard.form</field>
            <field name="model">chasier.report.wizard</field>
            <field name="type">form</field>
            <field name="arch" type="xml">
            <form string="Chasier Report">
                <group>
                    <field name="team_id" groups="account.group_account_manager"/>                
                </group>
                <group>
                    <field name="date" required="1"/>
                    
                </group>
                <footer>
                    <button name="get_chasier_report_report" string="Print" type="object" />
                </footer>
            </form>
            </field>
        </record>

          <record id="chasier_report_wizard_action_window" model="ir.actions.act_window">
            <field name="name">Chasier Report</field>
            <field name="res_model">chasier.report.wizard</field>
            <field name="type">ir.actions.act_window</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
            <field name="view_id" ref="chasier_report_wizard_form"/>
        </record>
        
        <menuitem action="chasier_report_wizard_action_window" id="chasier_report_wizard_menu_cash_in_out" sequence="66" parent="send_receive_request.menu_in_out_payment"/>
        
    
    </data>
    

</odoo>
