<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_move_eta_receipt_form" model="ir.ui.view">
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_move_form"/>
        <field name="arch" type="xml">
            <xpath expr="//header" position="inside">
                <field name="eta_state" widget="statusbar" statusbar_visible="signed,accepted,valid,invalid"/>
            </xpath>
            <xpath expr="//button[@name='button_draft']" position="after">
                <field name="eta_is_ereceipt" invisible='1'/>
                <button name="action_move_generate_eta" style="background-color: #5c5963;color: white;" 
                type="object" string="ETA Receipt Generate" attrs="{'invisible':['|','|','|',('move_type', 'not in', 
                ('out_invoice', 'out_refund')), ('state', '!=', 'posted'), ('eta_state', '!=', 'draft'),('eta_is_ereceipt', '=', False)]}" groups="account.group_account_invoice"/>
                <button name="action_move_send_eta" style="background-color: #5c5963;color: white;" 
                type="object" string="ETA Receipt Send" attrs="{'invisible':['|',('eta_state', '!=', 'generated'),('eta_is_ereceipt', '=', False)]}" groups="account.group_account_invoice"/>
                <button name="action_move_get_state" style="background-color: #5c5963;color: white;" 
                            type="object" 
                            string="Get Receipt State" attrs="{'invisible':['|',
                            ('eta_state', 'not in', ('accepted','submitted')),('eta_is_ereceipt', '=', False)]}" 
                            groups="account.group_account_invoice" />
            </xpath>
            <xpath expr="//notebook" position="inside">
                <page string="ETA Receipt" attrs="{'invisible': ['|',('eta_is_ereceipt', '=', False),('move_type', 'not in', ('out_invoice', 'out_refund'))]}">
                    <group>
                        <group>
                            <field name="eta_date_time_issued" 
                            attrs="{'readonly': [('eta_state', 'not in', ('draft','invalid', 'rejected'))]}"/>
                            <field name="eta_company_branch_id" 
                            options='{"no_open": True, "no_create": True}' 
                            />
                            <field name="eta_receipt_serial_id" 
                            options='{"no_open": True, "no_create": True}' 
                            />
                            <field name="eta_taxpayer_activity_code_id" 
                            options='{"no_open": True, "no_create": True}' 
                            />
                        </group>
                        <group>
                            <field name="eta_receipt_reference_uuid" attrs="{'invisible': [('move_type','!=','out_refund')]}"/>
                            <field name="eta_receipt_uuid"
                        attrs="{'invisible': [('eta_receipt_uuid','=',False)]}"
                        />
                        </group>
                        <group>
                            <field name="eta_receipt_reject_reason" 
                        attrs="{'invisible': [('eta_state','!=','rejected')]}"
                        />
                            <field name="eta_receipt_invalid_reason" 
                        attrs="{'invisible': [('eta_state','!=','invalid')]}"
                        />
                        </group>
                    </group>
                </page>
            </xpath>
        </field>
    </record>
    <record id="eta_generate_eta_recipt" model="ir.actions.server">
        <field name="name">ETA Recipt Generate</field>
        <field name="model_id" ref="account.model_account_move"/>
        <field name="binding_model_id" ref="account.model_account_move"/>
        <field name="groups_id" eval="[(4, ref('account.group_account_invoice'))]"/>
        <field name="state">code</field>
        <field name="code">
        if records:
            action = records.action_move_generate_eta()
        </field>
    </record>
    <record id="eta_send_eta_recipt" model="ir.actions.server">
        <field name="name">ETA Recipt Send</field>
        <field name="model_id" ref="account.model_account_move"/>
        <field name="binding_model_id" ref="account.model_account_move"/>
        <field name="groups_id" eval="[(4, ref('account.group_account_invoice'))]"/>
        <field name="state">code</field>
        <field name="code">
        if records:
            action = records.action_move_send_eta()
        </field>
    </record>
    <record id="eta_get_eta_recipt_state" model="ir.actions.server">
        <field name="name">ETA Recipt Get State</field>
        <field name="model_id" ref="account.model_account_move"/>
        <field name="binding_model_id" ref="account.model_account_move"/>
        <field name="groups_id" eval="[(4, ref('account.group_account_invoice'))]"/>
        <field name="state">code</field>
        <field name="code">
        if records:
            action = records.action_move_get_state()
        </field>
    </record>
    
</odoo>