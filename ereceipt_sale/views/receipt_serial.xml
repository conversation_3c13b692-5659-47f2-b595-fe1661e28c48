<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_receipt_serial_form" model="ir.ui.view">
        <field name="model">receipt.serial</field>
        <field name="arch" type="xml">
            <form string="Receipt Serials">
                <sheet>
                    <div class="oe_title">
                        <label for="name" class="oe_edit_only"/>
                        <h1>
                            <field name="name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="eta_receipt_serial"/>
                            <field name="eta_receipt_os_version"/>
                            <field name="eta_receipt_model_framework"/>
                            <field name="eta_receipt_pre_shared_key"/>
                        </group>
                        <group>
                            <field name="eta_receipt_api_base_url"/>
                            <field name="eta_receipt_id_srv_base_url"/>
                            <field name="eta_receipt_client_id"/>
                            <field name="eta_receipt_client_secret"/>
                            <field name="eta_receipt_type_person_minimum"/>
                            <field name="eta_receipt_company_branch_id" options='{"no_open": True, "no_create": True}'/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
    <record id="view_receipt_serial_tree" model="ir.ui.view">
        <field name="model">receipt.serial</field>
        <field name="arch" type="xml">
            <tree string="Receipt Serials">
                <field name="active" invisible="1"/>
                <field name="name"/>
                <field name="eta_receipt_serial"/>
            </tree>
        </field>
    </record>
    <record id="view_receipt_serial_search" model="ir.ui.view">
        <field name="model">receipt.serial</field>
        <field name="arch" type="xml">
            <search>
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
            </search>
        </field>
    </record>
    <record id="action_receipt_serial_action" model="ir.actions.act_window">
        <field name="name">Receipt Serials</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">receipt.serial</field>
        <field name="view_mode">tree,form</field>
        <field name="view_id" ref="view_receipt_serial_tree"/>
    </record>

    <menuitem id="menu_receipt_serial" groups="base.group_system" name="Receipt Serials" sequence="100" 
    parent="ereceipt_pos.eta_main_menu" action="action_receipt_serial_action"/>
</odoo>