# -*- coding: utf-8 -*-

import json
import requests
import urllib3
import ssl
import re
import base64
from datetime import datetime, timedelta, date
import collections.abc
from hashlib import sha256

from odoo import fields, models, _, SUPERUSER_ID
from odoo.exceptions import UserError, ValidationError

# As https://sdk.sit.invoicing.eta.gov.eg/standard-error-response/
status_code_gn = {
    '200': 'Success',
    '400': 'NotReady or BadRequest or BadArgument',
    '401': 'Unauthorized',
    '403': 'Forbidden',
    '404': 'NotFound',
    '429': 'TooManyRequests',
    '500': 'InternalServerError',
    '501': 'NotImplemented',
    '503': 'ServiceUnavailable'
}

status_code_gn_pdf = {
    '200': 'Success',
    '400': 'NotReady',
    '404': 'NotFound',
}

status_code_gn_cancel = {
    '200': 'Success',
    '400': 'OperationPeriodOver or IncorrectState or ActiveReferencingDocuments',
    '403': 'Forbidden',
}


def round_to_decimal(num, rounding):

    if int(num) != num and len(str(num).split('.')[1]) > rounding:
        num = str(num)[:str(num).index('.')+rounding+2]
        if num[-1] >= '5':
            a = num[:-2-(not rounding)]       # integer part
            b = int(num[-2-(not rounding)])+1  # decimal part
            res = float(a)+b**(-rounding +
                               1) if a and b == 10 else float(a+str(b))
            return round(abs(res), rounding)

        return abs(float(num[:-1]))
    else:
        return abs(num)


class ROCustomHttpAdapter(requests.adapters.HTTPAdapter):
    # "Transport adapter" that allows us to use custom ssl_context.

    def __init__(self, ssl_context=None, **kwargs):
        self.ssl_context = ssl_context
        super().__init__(**kwargs)

    def init_poolmanager(self, connections, maxsize, block=False):
        self.poolmanager = urllib3.poolmanager.PoolManager(
            num_pools=connections, maxsize=maxsize,
            block=block, ssl_context=self.ssl_context)


def ro_get_legacy_session():
    ctx = ssl.create_default_context(ssl.Purpose.SERVER_AUTH)
    ctx.options |= 0x4  # OP_LEGACY_SERVER_CONNECT
    session = requests.session()
    session.mount('https://', ROCustomHttpAdapter(ctx))
    return session

class EtaReceiptManageSale(models.Model):
    _name = 'eta.receipt.manage.sale'
    _description = 'ETA Receipt Manage Sale'

    def _create_eta_token(self, company):

        token_url = '%s/connect/token' % company.eta_id_srv_base_url

        eta_client_id = company.eta_client_id
        eta_client_secret = company.eta_client_secret

        payload = 'grant_type=client_credentials&client_id=%s&client_secret=%s&scope=InvoicingAPI' % (
            eta_client_id, eta_client_secret)

        headers = {
            "content-type": "application/x-www-form-urlencoded"
        }

        token_response = ro_get_legacy_session().post(
            token_url, data=payload, headers=headers)

        token = token_response.json().get('access_token')
        expires_in = token_response.json().get('expires_in')

        if not token:
            error = token_response.json().get('error')
            error_description = token_response.json().get('error_description')
            error_uri = token_response.json().get('error_uri')

            raise UserError(_('Error %s description %s uri %s' %
                              (error, error_description, error_uri)))

        company.with_user(SUPERUSER_ID).write({
            'eta_generated_access_token': token,
            'eta_token_timeout': datetime.utcnow() + timedelta(seconds=expires_in)
        })

        return token

    def _send_eta_receipt(self, moves):

        current_company = moves.mapped('company_id')

        if len(current_company) > 1:
            raise UserError(_('Please choose only one company.'))

        url = '%s/api/v1/receiptsubmissions' % (
            current_company.eta_api_base_url)

        if not current_company.eta_token_timeout or datetime.utcnow() > current_company.eta_token_timeout:
            token = self._create_eta_token(current_company)
        else:
            token = current_company.eta_generated_access_token

        total_moves = []
        moves = moves[::-1]

        for move in moves:

            if move.state != 'posted' or move.eta_state != 'generated' or not move.eta_receipt_uuid:
                continue

            eta_receipt_json_text = move.eta_receipt_json_text
            eta_receipt_json_text = eta_receipt_json_text.replace("'", '"')
            move_receipt = json.loads(eta_receipt_json_text)

            total_moves.append(move_receipt)

        if not total_moves:
            return False

        payload = {
            "receipts":
            total_moves
        }

        payload = json.dumps(payload, ensure_ascii=False)

        headers = {
            'Accept-Language': 'en',
            'Authorization': 'Bearer %s' % (token),
            'Content-Type': 'application/json'
        }

        response = ro_get_legacy_session().post(
            url, headers=headers, data=payload.encode('utf-8'))

        if response.status_code in [202, 200]:
            accepted = response.json().get('acceptedDocuments')
            rejected = response.json().get('rejectedDocuments')

            if accepted:
                move = self.env['account.move']

                for move_item in accepted:
                    move += moves.filtered(
                        lambda mv: mv.name == move_item['receiptNumber'])

                move.write({'eta_state': 'accepted',
                            'eta_receipt_reject_reason': False,
                            'eta_receipt_invalid_reason': False,
                            #'eta_receipt_json_text': False,
                            'eta_receipt_submission_uuid': response.json().get('submissionId')})

            elif rejected:

                for move_item in rejected:
                    move = moves.filtered(
                        lambda mv: mv.name == move_item['receiptNumber'])
                    errors = move_item.get('error')['details']

                    err = ''

                    for error in errors:
                        err += '%s\n' % (error['message'])

                    move.write({'eta_state': 'rejected',
                                'eta_receipt_reject_reason': err})
            # On 202 response if validation not complete
            else:
                raise UserError(response)

        elif response.status_code == 401:
            raise ValidationError(_('Not Authorized'))
        else:
            error_description = status_code_gn.get(
                str(response.status_code)) or response.text
            raise UserError(_('Error %s %s' %
                              (response.status_code, error_description)))

        return True

    def get_eta_receipt_document_state(self, moves):
        current_company = moves.mapped('company_id')

        if len(current_company) > 1:
            raise UserError(_('Please choose only one company.'))

        if not current_company.eta_token_timeout or datetime.utcnow() > current_company.eta_token_timeout:
            token = self._create_eta_token(current_company)
        else:
            token = current_company.eta_generated_access_token

        moves = moves.filtered(lambda mv: mv.eta_state in [
                               'accepted', 'submitted', 'valid'])

        receipt_submission_uuids = moves.mapped('eta_receipt_submission_uuid')

        for receipt_submission_uuid in receipt_submission_uuids:

            url = '%s/api/v1/receiptsubmissions/%s/details?PageNo=1&PageSize=100' % (
                current_company.eta_api_base_url, receipt_submission_uuid)

            headers = {
                'Accept-Language': 'en',
                'Authorization': 'Bearer %s' % (token),
                'Content-Type': 'application/json'
            }

            payload = {}

            response = ro_get_legacy_session().get(url, headers=headers, data=payload)

            if response.status_code in [202, 200]:

                status = response.json().get('status').lower()

                if status == 'valid':
                    moves.filtered(lambda mv: mv.eta_receipt_submission_uuid == response.json().get(
                        'submissionUuid')).write({'eta_state': 'valid'})

                elif status == 'invalid':
                    """ errors = []
                    if response.json().get('receipt').get('validationResults'):
                        errors = response.json().get('receipt').get(
                            'validationResults').get('validationSteps')

                    err = ''
                    for error in errors:
                        if error['status'] and error['status'].lower() == 'invalid':
                            msgs = error['error']['innerError']
                            for msg in msgs:
                                msg = msg['error']

                                product_eta_code = msg[msg.find(
                                    "[")+1:msg.find("]")]

                                product = self.env['product.product'].search(
                                    [('eta_item_code', '=ilike', product_eta_code)])

                                if product:
                                    err += '%s: Product %s %s \n' % (
                                        error['stepName'], product.name, msg)
                                else:
                                    err += '%s: %s \n' % (
                                        error['stepName'], msg) """

                    moves.filtered(lambda mv: mv.eta_receipt_submission_uuid == response.json().get('submissionUuid')).write(
                        {'eta_state': 'invalid'})

                elif status == 'submitted':
                    moves.filtered(lambda mv: mv.eta_receipt_submission_uuid == response.json().get('submissionUuid')).write({
                        'eta_state': 'submitted'
                    })

                else:
                    raise UserError(_('Check Later'))

            elif response.status_code == 401:
                raise ValidationError(_('Not Authorized'))

            elif response.status_code not in [202, 200]:
                error_description = status_code_gn.get(
                    str(response.status_code)) or response

                error = 'Error %s %s' % (
                    response.status_code, error_description)

                if response.status_code == 404:
                    error += ' or check later'
                raise UserError(_(error))

        return True

    def serializeUUID(self, documentStructure):

        if isinstance(documentStructure, (str, int, float, bool, date, datetime)):

            return '"' + str(documentStructure) + '"'

        serializedString = ""

        for key, value in documentStructure.items():

            if not isinstance(value, (list, tuple, set, dict)):
                serializedString += '"' + str(key).upper() + '"'
                serializedString += self.serializeUUID(value)

            if isinstance(value, (list, tuple, set)):

                serializedString += '"' + str(key).upper() + '"'

                for val in value:

                    serializedString += '"' + key.upper() + '"'

                    serializedString += self.serializeUUID(val)

            if isinstance(value, dict):

                serializedString += '"' + str(key).upper() + '"'

                for minkey, minval in value.items():

                    serializedString += '"' + minkey.upper() + '"'

                    serializedString += self.serializeUUID(minval)

        return serializedString

    def _send_generate_receipt(self, moves):

        moves = moves[::-1]

        rounding = self.env['decimal.precision'].precision_get(
            'Product Price')
        rounding_uom = self.env['decimal.precision'].precision_get(
            'Product Unit of Measure')

        rounding = rounding if rounding > rounding_uom else rounding_uom
        rounding = rounding if rounding <= 5 else 5

        for move in moves:

            if move.state != 'posted' or move.eta_state in ['accepted', 'submitted', 'valid']:
                continue

            move_lines_dict = []
            move_line_ids_all = move.invoice_line_ids.filtered(
                lambda line: line.display_type == 'product' and line.price_unit > 0)
            discount_lines = move.invoice_line_ids.filtered(
                lambda line: line.display_type == 'product' and line.price_unit < 0)

            extraDiscountAmount = 0
            if discount_lines:
                extraDiscountAmount = abs(
                    sum(discount_lines.mapped('price_unit')))

            move_line_ids = move_line_ids_all

            
            if not move_line_ids:
                continue

            
            totalMoveDiscount = totalMoveSales = totalMoveItemDiscount = 0
            totalMoveAmount = netMoveAmount = 0

            
            moveTaxs = []
            errors = []

           

            reward_extra_discount_fixed_price = extraDiscountAmount

            
            for line in move_line_ids:

                taxs = []

                TotalTaxableFees = totalLineTaxs = 0
                line_tax_ids = line.tax_ids

                # TODO:for forign currency need to work same
                ro_new_price = line.ro_new_price

                amountEGP = round_to_decimal(ro_new_price, rounding)
                line_quantity = round_to_decimal(line.quantity, rounding)

                if move.currency_id.name != 'EGP':

                    move_currency_rate = self.env['res.currency']._get_conversion_rate(
                        move.company_id.currency_id, move.currency_id, move.company_id, move.date or fields.Date.context_today(self))

                    if not move_currency_rate:
                        move_currency_rate = 1
                        errors.append(
                            'Line product [%s] Currency Rate' % line.product_id.name)
                    else:
                        move_currency_rate = round_to_decimal(
                            1/move_currency_rate, rounding)
                        amountEGP = round_to_decimal(
                            amountEGP * move_currency_rate, rounding)

                discountAmount = (line_quantity * amountEGP) * \
                    (line.discount / 100)

                price_without_tax = line.price_subtotal

               
                reward_line_discount = 0

               
                netTotal = (line_quantity * amountEGP) - \
                    discountAmount - reward_line_discount

                valueDifference = totalLineDiscount = totalLineSales = 0.0

                tax_include_base_amount_ids = line_tax_ids.filtered(
                    lambda tax: tax.include_base_amount)
                tax_not_include_base_amount_ids = line_tax_ids.filtered(
                    lambda tax: not tax.include_base_amount)
                total_after_tax_include = netTotal

                for tax in tax_include_base_amount_ids:
                    # TODO: add other taxes
                    eta_tax_amount = 0

                    # T1/T2/T4 tax amount
                    if tax.eta_code in ['T1', 'T2', 'T4']:
                        eta_tax_amount = round_to_decimal(
                            (abs(tax.amount)/100)*total_after_tax_include, rounding)

                    if tax.amount == 8:
                        total_after_tax_include += eta_tax_amount

                    if tax.eta_code in ['T1', 'T2']:
                        totalLineTaxs += eta_tax_amount
                    elif tax.eta_code == 'T4':
                        totalLineTaxs -= eta_tax_amount

                    taxs.append({
                        "taxType": tax.eta_code if tax.eta_code else errors.append('Line product [%s] Tax Type' % line.product_id.name),
                        "amount": round_to_decimal(eta_tax_amount, rounding),
                        "subType": tax.eta_sub_code if tax.eta_sub_code else errors.append('Line product [%s] Tax Type' % line.product_id.name),
                        "rate": round_to_decimal(abs(tax.amount), rounding)
                    })

                    duplicateTax = False

                    for item in moveTaxs:
                        if item['taxType'] == tax.eta_code:
                            duplicateTax = item

                    if duplicateTax:

                        duplicateTax['amount'] += eta_tax_amount

                    else:
                        moveTaxs.append({
                            "taxType": tax.eta_code if tax.eta_code else errors.append('Line product [%s] Tax Type' % line.product_id.name),
                            "amount": eta_tax_amount,
                        })

                for tax in tax_not_include_base_amount_ids:
                    # TODO: add other taxes
                    eta_tax_amount = 0

                    # T1/T2/T4 tax amount
                    if tax.eta_code in ['T1', 'T2', 'T4']:
                        eta_tax_amount = round_to_decimal(
                            (abs(tax.amount)/100)*total_after_tax_include, rounding)

                    if tax.eta_code in ['T1', 'T2']:
                        totalLineTaxs += eta_tax_amount
                    elif tax.eta_code == 'T4':
                        totalLineTaxs -= eta_tax_amount

                    taxs.append({
                        "taxType": tax.eta_code if tax.eta_code else errors.append('Line product [%s] Tax Type' % line.product_id.name),
                        "amount": round_to_decimal(eta_tax_amount, rounding),
                        "subType": tax.eta_sub_code if tax.eta_sub_code else errors.append('Line product [%s] Tax Type' % line.product_id.name),
                        "rate": round_to_decimal(abs(tax.amount), rounding)
                    })

                    duplicateTax = False

                    for item in moveTaxs:
                        if item['taxType'] == tax.eta_code:
                            duplicateTax = item

                    if duplicateTax:

                        duplicateTax['amount'] += eta_tax_amount

                    else:
                        moveTaxs.append({
                            "taxType": tax.eta_code if tax.eta_code else errors.append('Line product [%s] Tax Type' % line.product_id.name),
                            "amount": eta_tax_amount,
                        })

                # TODO: item discount after tax
                itemsLineDiscount = 0
                # totalMoveItemDiscount += round_to_decimal(
                #    itemsLineDiscount, rounding)

                product_uom_eta_code = line.product_uom_id.eta_code if line.product_uom_id.eta_code else line.product_id.uom_id.eta_code
                if not product_uom_eta_code:
                    errors.append(
                        'Line product [%s] UOM Code' % line.product_id.name)

                if line.product_id.eta_code_type:
                    eta_code_type = line.product_id.eta_code_type.upper()
                else:
                    eta_code_type = False

                eta_item_code = line.product_id.eta_item_code

                if len(line.name) > 500:
                    errors.append(
                        'Line product [%s] Description must be less than 500 char.' % line.product_id.name)

                move_line_without_tax = {
                    'internalCode': str(line.product_id.default_code) if line.product_id.default_code else errors.append('Line product [%s] Internal Reference.' % line.product_id.name),
                    "description": line.name.replace('"', '|').replace("'", "|").replace('\xa0', ' ').replace('\n', ' ') if line.name else errors.append('Line product [%s] Description' % line.product_id.name),
                    "itemType": eta_code_type,
                    "unitType": product_uom_eta_code,
                    "unitPrice": ro_new_price,
                    "quantity": line_quantity,
                    "totalSale": round_to_decimal(line_quantity * amountEGP, rounding),
                    "netSale": round_to_decimal(netTotal, rounding)
                }

                if line.discount > 0 or reward_line_discount > 0:
                    move_line_without_tax["commercialDiscountData"] = [{
                        "amount": round_to_decimal(discountAmount + reward_line_discount, rounding),
                        'description': 'Discount'
                    }]

                if eta_code_type == 'EGS':
                    move_line_without_tax["itemCode"] = "EG-{}-{}".format(
                        move.company_id.eta_id, eta_item_code) if eta_item_code else errors.append('Line product [%s] Product Code' % line.product_id.name)
                elif eta_code_type == 'GS1':
                    move_line_without_tax["itemCode"] = eta_item_code if eta_item_code else errors.append(
                        'Product Code')
                else:
                    errors.append(
                        'Line product [%s] Code Type or Product Code' % line.product_id.name)

                totalMoveDiscount += round_to_decimal(
                    discountAmount+reward_line_discount, rounding)
                totalMoveSales += round_to_decimal(
                    line_quantity * amountEGP, rounding)

                totalLineDiscount += discountAmount+reward_line_discount
                totalLineSales += line_quantity * amountEGP

                netMoveAmount += round_to_decimal(
                    totalLineSales - totalLineDiscount, rounding)
                totalMoveAmount += round_to_decimal(
                    totalLineSales - totalLineDiscount + totalLineTaxs, rounding)

                move_line_without_tax['total'] = round_to_decimal(
                    netTotal + TotalTaxableFees + valueDifference + totalLineTaxs, rounding)

                move_line_without_tax['taxableItems'] = taxs

                move_lines_dict.append(move_line_without_tax)

            for moveTax in moveTaxs:
                moveTax['amount'] = round_to_decimal(
                    moveTax['amount'], rounding)

            eta_date_time_issued = ''

            if move.eta_date_time_issued:
                eta_date_time_issued = move.eta_date_time_issued.strftime(
                    "%Y-%m-%dT%H:%M:%SZ")
            else:
                errors.append('Date Time Issued')

            current_move = {
                "header": {
                    'dateTimeIssued': eta_date_time_issued,
                    'receiptNumber': move.name,
                    'uuid': '',
                    'previousUUID': move.eta_receipt_serial_id.eta_receipt_last_uuid or '',
                    'currency': move.currency_id.name,
                    'orderdeliveryMode': 'FC'
                },
                "seller": {
                    "branchAddress": {
                        "country": move.eta_company_branch_id.eta_country_id.code if move.eta_company_branch_id.eta_country_id else errors.append('Company Country'),
                        "governate": move.eta_company_branch_id.eta_governate if
                        move.eta_company_branch_id.eta_governate else errors.append('Company Governate'),
                        "regionCity": move.eta_company_branch_id.eta_region_city if
                        move.eta_company_branch_id.eta_region_city else errors.append('Company City'),
                        "street": move.eta_company_branch_id.eta_street.replace(
                            '"', '|').replace("'", "|").replace('\xa0', ' ') if
                        move.eta_company_branch_id.eta_street else errors.append('Company Street'),
                        "buildingNumber": move.eta_company_branch_id.eta_building_number if move.eta_company_branch_id.eta_building_number else errors.append('Company Building Number')
                    },
                    "rin": move.company_id.eta_id if move.company_id.eta_id else errors.append('Company Id'),
                    "companyTradeName": move.company_id.eta_name.replace('"', '|').replace("'", "|").replace('\xa0', ' ') if
                    move.company_id.eta_name else errors.append('Name'),
                    "activityCode": str(move.eta_taxpayer_activity_code_id.code) if move.eta_taxpayer_activity_code_id else errors.append('Taxpayer Activity Code'),
                    "branchCode": move.eta_company_branch_id.eta_branchId if move.eta_company_branch_id.eta_branchId else errors.append('Company branchId'),
                    "deviceSerialNumber": move.eta_receipt_serial_id.eta_receipt_serial if move.eta_receipt_serial_id.eta_receipt_serial else errors.append('Company deviceSerialNumber'),

                },
                "buyer": {
                    "type": move.partner_id.eta_type if move.partner_id.eta_type else 'P'
                },
                "documentType": {
                    'receiptType': 'SR',
                    'typeVersion': move.company_id.eta_receipt_version
                },
                "itemData":
                move_lines_dict,
                "totalSales": round_to_decimal(totalMoveSales, rounding),
                "netAmount": round_to_decimal(netMoveAmount, rounding),
                "taxTotals":
                moveTaxs,
                "totalAmount": round_to_decimal(totalMoveAmount - reward_extra_discount_fixed_price, rounding)

            }

            if (reward_extra_discount_fixed_price > 0):
                current_move['extraReceiptDiscountData'] = [{
                    'amount': round_to_decimal(reward_extra_discount_fixed_price, rounding),
                    'description': 'Extra Discount'
                }]

            if totalMoveDiscount > 0:
                current_move["totalCommercialDiscount"] = round_to_decimal(
                    totalMoveDiscount, rounding)

            if move.payment_id:
                if move.payment_id[0].payment_method_id.journal_id.type == 'cash':
                    payment_method = 'C'
                elif move.payment_id[0].payment_method_id.journal_id.type == 'bank':
                    payment_method = 'V'
                else:
                    payment_method = 'O'
            else:
                payment_method = 'O'

            current_move['paymentMethod'] = payment_method

            if move.eta_receipt_reference_uuid and move.move_type == 'out_refund':
                current_move['documentType']['receiptType'] = 'RR'
                current_move['header']['referenceUUID'] = move.eta_receipt_reference_uuid
            elif move.move_type == 'out_refund':
                raise ValidationError(
                    _("Receipt Reference required."))

            if move.partner_id.eta_type:
                current_move["buyer"]["type"] = move.partner_id.eta_type
            elif move.partner_id.eta_type == "B" or totalMoveAmount >= move.company_id.eta_receipt_type_person_minimum:
                errors.append('Partner Type')

            if move.partner_id.eta_name:
                current_move["buyer"]["name"] = move.partner_id.eta_name.replace(
                    '"', '|').replace("'", "|").replace('\xa0', ' ')
            elif move.partner_id.eta_type == "B" or totalMoveAmount >= move.company_id.eta_receipt_type_person_minimum:
                errors.append('Partner Name')

            if move.partner_id.eta_id:
                current_move['buyer']['id'] = move.partner_id.eta_id
            elif move.partner_id.eta_type == "B" or totalMoveAmount >= move.company_id.eta_receipt_type_person_minimum:
                errors.append('Partner Id')

            if len(errors) > 0:
                raise ValidationError(
                    _('Fields are required {}'.format(errors)))

            # date can't be in future and utc time
            now = datetime.utcnow()
            then = datetime.utcfromtimestamp(
                int(datetime.timestamp(move.eta_date_time_issued)))
            diff = (now - then) / timedelta(seconds=1)

            if diff < 0:
                raise ValidationError(
                    _("Time should be in UTC and not future."))

            # Keep first uuid
            to_write = {}
            if move.eta_state not in ['draft', 'generated'] and not move.eta_receipt_reference_old_uuid:
                current_move['header']['referenceOldUUID'] = move.eta_receipt_uuid
                to_write['eta_receipt_reference_old_uuid'] = move.eta_receipt_uuid
            elif move.eta_state not in ['draft', 'generated'] and move.eta_receipt_reference_old_uuid:
                current_move['header']['referenceOldUUID'] = move.eta_receipt_reference_old_uuid

            # generate uuid
            serializeUUID = self.serializeUUID(current_move)
            uuid = sha256(serializeUUID.encode('utf-8')).hexdigest()

            current_move['header']['uuid'] = uuid

            to_write['eta_receipt_json_text'] = current_move
            to_write['eta_receipt_uuid'] = uuid
            to_write['eta_state'] = 'generated'
            to_write['eta_receipt_previous_uuid'] = move.eta_receipt_serial_id.eta_receipt_last_uuid
            to_write['eta_receipt_qr_string'] = ''.join((move.company_id.eta_portal_url, '/receipts/search/', uuid, '/share/',
                                                        eta_date_time_issued, '/roaya/Total:', str(totalMoveAmount - reward_extra_discount_fixed_price), ',IssuerRIN:', move.company_id.eta_id))

            move.eta_receipt_serial_id.eta_receipt_last_uuid = uuid

            move.write(to_write)

        return True
