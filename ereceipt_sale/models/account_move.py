# -*- coding: utf-8 -*-

from datetime import datetime
from odoo import api, fields, models, _
from odoo.exceptions import UserError, ValidationError


def round_to_decimal(num, rounding):

    if int(num) != num and len(str(num).split('.')[1]) > rounding:
        num = str(num)[:str(num).index('.')+rounding+2]
        if num[-1] >= '5':
            a = num[:-2-(not rounding)]       # integer part
            b = int(num[-2-(not rounding)])+1  # decimal part
            res = float(a)+b**(-rounding +
                               1) if a and b == 10 else float(a+str(b))
            return round(res, rounding)

        return float(num[:-1])
    else:
        return num


class AccountMove(models.Model):
    _inherit = 'account.move'

    eta_receipt_uuid = fields.Char(
        string='Receipt UUID',
        readonly=True,
        copy=False
    )

    eta_receipt_submission_uuid = fields.Char(
        string='Submission UUID',
        readonly=True,
        copy=False
    )

    eta_receipt_previous_uuid = fields.Char(
        string='Previous UUID',
        readonly=True,
        copy=False
    )

    # For return
    eta_receipt_reference_uuid = fields.Char(
        string='Reference UUID',
        copy=False
    )

    # For resend receipt
    eta_receipt_reference_old_uuid = fields.Char(
        string='Reference Old UUID',
        readonly=True,
        copy=False,
        tracking=True
    )

    eta_state = fields.Selection(selection=[
        ('draft', 'Draft'),
        ('generated', 'generated'),
        ('accepted', 'Accepted'),
        ('submitted', 'Submitted'),
        ('valid', 'Valid'),
        ('invalid', 'Invalid'),
        ('rejected', 'Rejected'),
        ('tocancel', 'tocancel'),
        ('canceled', 'Canceled')
    ], string='ETA Status', readonly=True, copy=False, tracking=True, default='draft')

    eta_date_time_issued = fields.Datetime(string='Date Time Issued',
                                           help='The date and time when the document was issued. \
                                             Date and time cannot be in future. \
                                            Time to be supplied in UTC timezone.[2015-02-13T13:15:00Z]')

    eta_company_branch_id = fields.Many2one(
        string='Company Branch',
        comodel_name='eta.company.branch',
        ondelete='restrict',
        help='Mandatory when issuer is of type B, \
            otherwise optional. The code of the branch as registered with tax authority for the \
            company submitting the document.[1234]'
    )

    @api.onchange('team_id')
    def _onchange_field(self):
        eta_company_branch_id = self.team_id.eta_company_branch_id
        if eta_company_branch_id:
            self.eta_company_branch_id = self.team_id.eta_company_branch_id

    # As https://sdk.sit.invoicing.eta.gov.eg/codes/activity-types/
    eta_taxpayer_activity_code_id = fields.Many2one(
        string='Taxpayer Activity Code',
        comodel_name='eta.activity.types',
        domain="[('eta_company_branch_id', '=', eta_company_branch_id)]",
        ondelete='restrict',
        help='Tax activity code of the business issuing the document \
            representing the activity that caused it to be issued.[9478]'
    )

    eta_receipt_json_text = fields.Text(
        string='Json Text',
        readonly=True,
        copy=False
    )

    eta_receipt_reject_reason = fields.Text(
        string='Reject Reason',
        readonly=True,
        copy=False,
        tracking=True
    )

    eta_receipt_invalid_reason = fields.Text(
        string='Invalid Reason',
        readonly=True,
        copy=False,
        tracking=True
    )

    eta_receipt_serial_id = fields.Many2one(
        string='Receipt Serial',
        comodel_name='receipt.serial',
        ondelete='restrict',
        domain="[('eta_receipt_company_branch_id', '=', eta_company_branch_id)]",
    )

    eta_receipt_qr_string = fields.Char(
        string='QR String',
        readonly=True,
        copy=False
    )

    eta_is_ereceipt = fields.Boolean(
        related='journal_id.eta_is_ereceipt', store=True)

    @api.onchange('eta_receipt_serial_id')
    def _onchange_eta_receipt_serial_id(self):
        self.eta_company_branch_id = self.eta_receipt_serial_id.eta_receipt_company_branch_id

    def action_move_generate_eta(self):
        self.env['eta.receipt.manage.sale']._send_generate_receipt(
            self.filtered(lambda move: move.eta_is_ereceipt))

    def action_move_send_eta(self):
        self.env['eta.receipt.manage.sale']._send_eta_receipt(
            self.filtered(lambda move: move.eta_is_ereceipt)[:50])

    def action_move_get_state(self):
        self.env['eta.receipt.manage.sale'].get_eta_receipt_document_state(
            self.filtered(lambda move: move.eta_is_ereceipt))

    def _post(self, soft=True):
        for move in self:
            if not move.eta_date_time_issued:
                move.eta_date_time_issued = datetime.now()

        return super()._post(soft)

    def button_draft(self):
        for move in self:
            if move.eta_state in ['accepted', 'submitted']:
                raise ValidationError(_('Please get eta state first.'))
            elif move.eta_state != 'valid':
                move.write({'eta_receipt_json_text': False,
                           'eta_state': 'draft'})

        super().button_draft()

    def write(self, vals):
        for move in self:
            new_state = vals.get('state')
            if move.eta_state == 'valid' and new_state and new_state != 'draft' and not self.env.user.has_group('account.group_account_manager'):
                raise ValidationError(
                    _('Can\'t edit valid document cancel first.'))

        return super().write(vals)


class AccountMoveLine(models.Model):
    _inherit = 'account.move.line'

    ro_new_price = fields.Float(string='New Unit Price', digits='Product Price',
                                compute='_compute_ro_new_price')

    @api.depends('price_unit', 'tax_ids')
    def _compute_ro_new_price(self):
        rounding = self.env['decimal.precision'].precision_get(
            'Product Price')

        rounding_uom = self.env['decimal.precision'].precision_get(
            'Product Unit of Measure')

        rounding = rounding if rounding > rounding_uom else rounding_uom
        rounding = rounding if rounding <= 5 else 5

        for record in self:
            ro_new_price = record.price_unit

            for tax in self.tax_ids.filtered(lambda tax: tax.price_include):
                ro_new_price = round_to_decimal(
                    ro_new_price / (1+(abs(tax.amount)/100)), rounding)

            record.ro_new_price = ro_new_price
