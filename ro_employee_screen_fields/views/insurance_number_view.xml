<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="insurance_number_form_view" model="ir.ui.view">
        <field name="name">insurance number form view</field>
        <field name="model">insurance.number</field>
        <field name="arch" type="xml">
           
            <form string="رقم المنشأة التأميني">
            
            <sheet>            
                    <div class="oe_title">
                        <group>
                                  
                            <field name="name" string="رقم المنشأة التأميني"/>
                            <!-- <field name="active" invisible="1"/> -->

                        </group>        
                    </div>
            </sheet>            
            </form>
        </field>
    </record>

    <record id="insurance_number_list_view" model="ir.ui.view">
        <field name="name">nsurance number tree</field>
        <field name="model">insurance.number</field>
        <field name="arch" type="xml">
            <tree name="رقم المنشأة التأميني">
                <field name="name"/>
                <!-- <field name="active" invisible="1"/> -->

               
               
            </tree>
        </field>
    </record>

    <record id="insurance_number_search_view" model="ir.ui.view">
        <field name="name">insurance number search view</field>
        <field name="model">insurance.number</field>

        <field name="arch" type="xml">
            <search>
                <field name="name"/>
               
                <filter string="رقم المنشأة التأميني" name="name" context="{'group_by': 'name'}"/>
               
                <separator/>
                
            </search>
        </field>
    </record>

    <record id="insurance_number_action" model="ir.actions.act_window">
            <field name="name">insurance establishment number</field>
            <field name="res_model">insurance.number</field>
            <field name="view_mode">tree,form</field>
            <field name="search_view_id" ref="ro_employee_screen_fields.insurance_number_search_view"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create new insurance establishment Number
                </p>
               
            </field>
        </record>
      <menuitem
            id="menu_insurance_establishment_number"
            name="رقم المنشأة التأميني"
            action="insurance_number_action"
            parent="hr.menu_human_resources_configuration"
            sequence="51"
            
            />
</odoo>