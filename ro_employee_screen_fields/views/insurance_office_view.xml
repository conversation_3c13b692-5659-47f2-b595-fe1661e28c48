<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="insurance_office_form_view" model="ir.ui.view">
        <field name="name">insurance office form view</field>
        <field name="model">insurance.office</field>
        <field name="arch" type="xml">
           
            <form string="المكتب التأميني">
            
            <sheet>            
                    <div class="oe_title">
                        <group>
                                  
                            <field name="name" string="المكتب التأميني"/>
                            <!-- <field name="active" invisible="1"/> -->

                        </group>        
                    </div>
            </sheet>            
            </form>
        </field>
    </record>

    <record id="insurance_office_list_view" model="ir.ui.view">
        <field name="name">nsurance office tree</field>
        <field name="model">insurance.office</field>
        <field name="arch" type="xml">
            <tree name=" المكتب التأميني">
                <field name="name"/>
                <!-- <field name="active" invisible="1"/> -->

               
               
            </tree>
        </field>
    </record>

    <record id="insurance_office_search_view" model="ir.ui.view">
        <field name="name">insurance office search view</field>
        <field name="model">insurance.office</field>

        <field name="arch" type="xml">
            <search>
                <field name="name"/>
               
                <filter string=" المكتب التأميني" name="name" context="{'group_by': 'name'}"/>
               
                <separator/>
                
            </search>
        </field>
    </record>

    <record id="insurance_office_action" model="ir.actions.act_window">
            <field name="name">insurance office</field>
            <field name="res_model">insurance.office</field>
            <field name="view_mode">tree,form</field>
            <field name="search_view_id" ref="ro_employee_screen_fields.insurance_office_search_view"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create new insurance office
                </p>
               
            </field>
        </record>
      <menuitem
            id="menu_insurance_office"
            name=" المكتب التأميني"
            action="insurance_office_action"
            parent="hr.menu_human_resources_configuration"
            sequence="52"
            
            />
</odoo>