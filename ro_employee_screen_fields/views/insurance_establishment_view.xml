<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="insurance_establishment_form_view" model="ir.ui.view">
        <field name="name">insurance establishment form view</field>
        <field name="model">insurance.establishment</field>
        <field name="arch" type="xml">
           
            <form string="اسم المنشأة التأميني">
            
            <sheet>            
                    <div class="oe_title">
                        <group>
                                  
                            <field name="name" string="اسم المنشأة التأميني"/>
                            <!-- <field name="active" invisible="1"/> -->

                        </group>        
                    </div>
            </sheet>            
            </form>
        </field>
    </record>

    <record id="insurance_establishment_list_view" model="ir.ui.view">
        <field name="name">nsurance establishment tree</field>
        <field name="model">insurance.establishment</field>
        <field name="arch" type="xml">
            <tree name="اسم المنشأة التأميني">
                <field name="name"/>
                <!-- <field name="active" invisible="1"/> -->

               
               
            </tree>
        </field>
    </record>

    <record id="insurance_establishment_search_view" model="ir.ui.view">
        <field name="name">insurance establishment search view</field>
        <field name="model">insurance.establishment</field>

        <field name="arch" type="xml">
            <search>
                <field name="name"/>
               
                <filter string="اسم المنشأة التأميني" name="name" context="{'group_by': 'name'}"/>
               
                <separator/>
                
            </search>
        </field>
    </record>

    <record id="insurance_establishment_action" model="ir.actions.act_window">
            <field name="name">insurance establishment name</field>
            <field name="res_model">insurance.establishment</field>
            <field name="view_mode">tree,form</field>
            <field name="search_view_id" ref="ro_employee_screen_fields.insurance_establishment_search_view"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create new insurance establishment name
                </p>
               
            </field>
        </record>
      <menuitem
            id="menu_insurance_establishment"
            name="اسم المنشأة التأميني"
            action="insurance_establishment_action"
            parent="hr.menu_human_resources_configuration"
            sequence="50"
            
            />
</odoo>