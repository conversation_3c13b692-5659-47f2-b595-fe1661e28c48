.pos .numpad button {
    float: left
        /*rtl:ignore*/
    ;
    /* rtlcss forced to keep ltr */
    height: 40px;
    vertical-align: middle;
    color: $gray-700;
    border-radius: 0;
    border: none;
    border-right: 1px solid;
    border-bottom: 1px solid;
    border-color: $gray-300;
    transition: all 150ms linear;
    padding: unset;
    width: 100%;
    font-weight: bold;
}

.pos .control-button {
    -webkit-flex-grow: 1;
    flex-grow: 1;
    background: #fff;
    border-left: solid 1px $gray-300;
    border-bottom: solid 1px $gray-300;
    display: inline-block;
    line-height: 37px;
    min-width: 80px;
    text-align: center;
    padding: 0px 10px;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    overflow: hidden;
    transition: all linear 150ms;
}

.pos .actionpad .button .fa-user {
    position: absolute;
    left: 13px;
    top: 9px;
    margin-right: 8px;
    font-size: 18px;
    background: rgba(255, 255, 255, 0.5);
    line-height: 20px;
    width: 30px;
    border-radius: 100%;
}

.pos {
    --btn-height-size: 38px;
    direction: ltr;
    font-family: "Lato", "Lucida Grande", Helvetica, Verdana, Arial;
    color: #4A4F59;
    font-size: 12px;
    text-shadow: none;
}
.pos .button.validation {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    height: calc(var(--btn-height-size) * 2);
    border: none;
    background: $primary;
    color: #fff;
    font-size: 14px;
    font-weight: bold;
    transition: all 150ms linear;
}
