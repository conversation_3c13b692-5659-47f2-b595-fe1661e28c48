<?xml version="1.0" encoding="UTF-8"?>

<templates>

    <t t-name="ProductScreen" t-inherit="point_of_sale.ProductScreen" t-inherit-mode="primary" owl="1">
        <xpath expr="//t[@t-name='ProductScreen']" position="replace">
            <div class="product-screen screen" t-att-class="{ oe_hidden: !props.isShown }">
            <div class="screen-full-width">
                <div class="leftpane pane-border" t-if="!env.isMobile || state.mobile_pane === 'left'">
                    <OrderWidget/>
                    <div class="pads">
                    
                    </div>
                </div>
                <div class="rightpane" t-if="!env.isMobile || state.mobile_pane === 'right'">
                    <ProductsWidget mobileSearchBarIsShown="props.mobileSearchBarIsShown"/>
                    <div class="pads">
                        <div class="control-buttons">
                            <t t-if="env.isMobile and controlButtons.length > 3">
                                <div class="control-button" t-on-click="_displayAllControlPopup">More...</div>
                            </t>
                            <t t-else="">
                                <t t-foreach="controlButtons" t-as="cb" t-key="cb.name">
                                    <t t-component="cb.component" t-key="cb.name"/>
                                </t>
                            </t>
                        </div> 
                        <div class="control-buttons">
                            <ActionpadWidget partner="partner" actionName="constructor.numpadActionName" onSwitchPane.bind="switchPane" />
                            <NumpadWidget activeMode="env.pos.numpadMode" />
                        </div>
                    </div>
                    <MobileOrderWidget t-if="env.isMobile" pane="state.mobile_pane" onSwitchPane.bind="switchPane"/>
                </div>
            </div>
        </xpath>
    </t>

</templates>
