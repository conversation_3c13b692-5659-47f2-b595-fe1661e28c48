# -*- coding: utf-8 -*-
from odoo import models, fields, api


class ProductCategory(models.Model):
    _inherit = 'product.category'

    is_hide_print = fields.Boolean(string="Hide from Print POS and Sale")


class PosSession(models.Model):
    _inherit = 'pos.session'

    def _loader_params_product_category(self):
        res = super()._loader_params_product_category()
        res['search_params']['fields'].extend(['is_hide_print'])
        return res
