odoo.define('pos_product_quantity.models', function (require) {
"use strict";

var { PosGlobalState, Orderline} = require('point_of_sale.models');
const Registries = require('point_of_sale.Registries');


const CategOrderline = (Orderline) => class CategOrderline extends Orderline {
    export_for_printing() {
        var result = super.export_for_printing(...arguments);
        result.product = this.get_product();
        result.is_hide_categ = this.get_product().categ.is_hide_print;

        return result;
    }
}
Registries.Model.extend(Orderline, CategOrderline);

});
