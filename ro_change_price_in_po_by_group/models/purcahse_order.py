from odoo import models, fields,api

class PurchaseOrder<PERSON><PERSON><PERSON>t(models.Model):
    _inherit = 'purchase.order'

    ro_user_in_group = fields.Boolean(compute="_compute_user_in_group")
    
    @api.depends('product_id')
    def _compute_user_in_group(self):
        for record in self:
            ro_user_in_group=self.env.user.has_group('ro_change_price_in_po_by_group.group_change_price_in_po')
            record.ro_user_in_group=ro_user_in_group
            

    @api.onchange('product_id')
    def _onchange_users_in_group(self):
        for record in self:
            ro_user_in_group=self.env.user.has_group('ro_change_price_in_po_by_group.group_change_price_in_po')
            record.ro_user_in_group=ro_user_in_group
            