<odoo>


  <!-- <template id="report_simple_label3x2">
    <t t-set="barcode_size" t-value="'width:33mm;height:14mm'"/>
    <t t-set="table_style" t-value="'width:63mm;height:37mm;' + table_style"/>
    <td t-att-style="make_invisible and 'visibility:hidden;'">
      <div class="o_label_full" t-att-style="table_style">
        <div class="o_label_name">
          <strong t-field="product.display_name"/>
        </div>
        <div class="o_label_data">
          <div class="text-center o_label_left_column">
            <span class="text-nowrap" t-field="product.default_code"/>
            <t t-if="barcode">
              <div t-out="barcode" t-options="{'widget': 'barcode', 'symbology': 'auto', 'img_style': barcode_size}"/>
              <span class="text-center" t-out="barcode"/>
            </t>
          </div>
          <div class="text-end" style="line-height:normal">
            <div class="o_label_extra_data">
              <t t-out="extra_html"/>
            </div>
            <t t-if="product.is_product_variant">
              <strong class="o_label_price" t-field="product.lst_price" t-options="{'widget': 'monetary', 'label_price': True}"/>
            </t>
            <t t-else="">
              <strong class="o_label_price" t-field="product.list_price" t-options="{'widget': 'monetary', 'label_price': True}"/>
            </t>
          </div>
          <div class="o_label_clear"></div>
        </div>
      </div>
    </td>
  </template>





  <template id="report_productlabel_inh" name="report_productlabel_inh" inherit_id="product.report_productlabel">
    <xpath expr="//t[@t-if='columns and rows']/t[2]" position="after">
      <t t-if="columns == 3 and rows == 2">
        <t t-set="padding_page" t-value="'padding: 14mm 3mm'"/>
        <t t-set="report_to_call" t-value="'ro_print_label.report_simple_label3x2'"/>
      </t>

    </xpath>
  </template> -->




  <template id="report_simple_label_2_5x5">
    <div class="o_label_sheet" style="font-size: 50%;width: 50mm;height: 19mm;">
      <div class="o_label_full" t-att-style="table_style">
        <div class= "text-start o_label_small_barcode">
          <t t-if="barcode">
            <!-- `quiet=0` to remove the left and right margins on the barcode -->
            <div t-out="barcode" style="padding:0" t-options="{'widget': 'barcode', 'quiet': 0, 'symbology': 'auto', 'img_style': barcode_size}"/>
            <div class="o_label_name" style="height:1.7em;background-color: transparent;">
              <span t-out="barcode"/>
            </div>
          </t>
        </div>
        <div class="o_label_name" style="line-height: 100%;background-color: transparent;padding-top: 1px;">
          <span t-if="product.is_product_variant" t-field="product.display_name"/>
          <span t-else="" t-field="product.name"/>
        </div>
        <div class="o_label_print_time text-center" style="text-align:center;font-size:6px;">
        <!-- <t t-esc="time.strftime('%Y-%m-%d %H:%M:%S')"/> -->
        <t t-esc="time.strftime('%d-%m-%Y %H:%M:%S')"/>
      </div>
        <div class="o_label_left_column">
          <small class="text-nowrap" t-field="product.default_code"/>
        </div>
        
        <div class="text-end" style="padding: 0 4px;">
          <t t-if="product.is_product_variant">
            <strong class="o_label_price_small" t-field="product.lst_price" t-options="{'widget': 'monetary', 'label_price': True}"/>
          </t>
          <t t-else="">
            <strong class="o_label_price_small" t-field="product.list_price" t-options="{'widget': 'monetary', 'label_price': True}"/>
          </t>
          <div class="o_label_extra_data">
            <t t-out="extra_html"/>
          </div>
        </div>
      
      </div>
    </div>
  </template>

  <template id="report_productlabel_2_5x5">
    <t t-call="web.html_container">
      <t t-set="barcode_size" t-value="'width:50mm;height:6mm'"/>
      <t t-set="table_style" t-value="'width:50mm;height:25mm;'"/>
      
      <t t-foreach="quantity.items()" t-as="barcode_and_qty_by_product">
        <t t-set="product" t-value="barcode_and_qty_by_product[0]"/>
        <t t-foreach="barcode_and_qty_by_product[1]" t-as="barcode_and_qty">
          <t t-set="barcode" t-value="barcode_and_qty[0]"/>
          <t t-foreach="range(barcode_and_qty[1])" t-as="qty">
            <t t-call="ro_print_label.report_simple_label_2_5x5"/>
          </t>
        </t>
      </t>
    </t>
  </template>

  <template id="report_producttemplatelabel_2_5x5">
    <t t-call="web.basic_layout">
      <div class="page">
        <t t-call="ro_print_label.report_productlabel_2_5x5">
          <t t-set="products" t-value="products"/>
        </t>
      </div>
    </t>
  </template>
</odoo>