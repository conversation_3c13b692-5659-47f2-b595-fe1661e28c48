<?xml version='1.0' encoding='utf-8'?>
<odoo>
    <data>
        <record id="paperformat_label_sheet_2_5x5" model="report.paperformat">
            <field name="name">2,5x5 Label Sheet</field>
            <field name="default" eval="True" />
            <field name="format">custom</field>
            <field name="page_height">50</field>
            <field name="page_width">25</field>
            <field name="orientation">Landscape</field>
            <field name="margin_top">2</field>
            <field name="margin_bottom">0</field>
            <field name="margin_left">0</field>
            <field name="margin_right">0</field>
            <field name="disable_shrinking" eval="True"/>
            <field name="dpi">96</field>
        </record>

        <record id="report_product_template_label_2_5x5" model="ir.actions.report">
            <field name="name">Product Label (PDF)</field>
            <field name="model">product.template</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">ro_print_label.report_producttemplatelabel_2_5x5</field>
            <field name="report_file">ro_print_label.report_producttemplatelabel_2_5x5</field>
            <field name="paperformat_id" ref="ro_print_label.paperformat_label_sheet_2_5x5"/>
            <field name="print_report_name">'Products Labels - %s' % (object.name)</field>
            <field name="binding_type">report</field>
        </record>

    </data>
</odoo>