from odoo import fields, models
from odoo.addons.product.report.product_label_report import _prepare_data

class ProductLabelLayout(models.TransientModel):
    _inherit = 'product.label.layout'
    
    print_format = fields.Selection(selection_add=[
        ('2x5', '2.5 x 5 with price'),
    ], ondelete={'2x5': 'set default'})

    def _prepare_report_data(self):
        xml_id, data = super()._prepare_report_data()

        if '2x5' in self.print_format:
            xml_id = 'ro_print_label.report_product_template_label_2_5x5'
       
        return xml_id, data
    

class ReportProductTemplateLabelDymo(models.AbstractModel):
    _name = 'report.ro_print_label.report_producttemplatelabel_2_5x5'
    _description = 'Product Label Report'

    def _get_report_values(self, docids, data):
        return _prepare_data(self.env, data)
