from odoo import models, fields, api,_, SUPERUSER_ID
from num2words import num2words


class AccountMove(models.Model):
    _inherit = "account.move"
    

    ro_total_html = fields.Html('Total Price', compute='_ro_get_total_html')

    @api.depends('invoice_line_ids')
    def _ro_get_total_html(self):
        total_discount=all_qty=all_line_total = 0
        for record in self:
            for rec in record.invoice_line_ids:
                all_line_total += rec.price_unit/1.14*rec.quantity
                total_discount += rec.price_unit/1.14*rec.quantity*(rec.discount/100)
                all_qty += rec.quantity
                
            ro_total_discount = round(float(total_discount),2)
            ro_all_qty = round(float(all_qty),2)
            ro_all_net = round(all_line_total-float(total_discount),2)
            ro_all_taxs = round(ro_all_net*.14,2)
            ro_all_gross = round(ro_all_net + ro_all_taxs,2)
            
            html_discount = """
            <td style="text-align: right;">
                                                <span style="padding:2px;">{}</span>
                                                <span>:</span>
                                                <label style="margin-bottom:0">الخصم</label>
                                            </td>
                                            """.format(ro_total_discount) if ro_total_discount >0 else "<td></td>"
            html_text = """
            <table style="width:100%;font-size:14px;">
                                    <tbody>
                                        <tr>
                                            <td style="text-align: right; ">
                                                <span style="padding:2px;">{}</span>
                                                <span>:</span>
                                                <label style="margin-bottom:0">عدد
                                                    <span> </span>الاصناف
                                                </label>
                                            </td>
                                            {}
                                        </tr>
                                        <tr>
                                            <td style="text-align: right; ">
                                                <span style="padding:2px;">{}</span>
                                                <span>:</span>
                                                <label style="margin-bottom:0">الكميه</label>
                                            </td>
                                            
                                            <td style="text-align: right;" colspan="2">
                                                <span style="padding:2px;">{}</span>
                                                <span>:</span>
                                                <label style="margin-bottom:0">الصافى</label>
                                            </td>
                                            
                                        </tr>
                                        <tr>
                                          <td></td>
                                            <td style="text-align: right;">
                                                <span style="padding:2px;">{}</span>
                                                <span>:</span>
                                                <label style="margin-bottom:0"> 14%الضرائب</label>
                                            </td>
                                        </tr>
                                        <tr>
                                          <td></td>
                                            <td style="text-align: right;">
                                                <span style="padding:2px;">{}</span>
                                                <span>:</span>
                                                <label style="margin-bottom:0">الإجمالى</label>
                                            </td>
                                        </tr>
                                        
                                    </tbody>
                                </table>
            """.format(len(record.invoice_line_ids), html_discount, ro_all_qty,ro_all_net, ro_all_taxs, ro_all_gross)
            
            
            record.ro_total_html = html_text
            
    def change_size_page(self, items):
        paper_format = self.env.ref('ereceipt_layout.eta_paperformat_portrait')
        paper_format.with_user(SUPERUSER_ID).page_height = 120 + (len(items) * 30)
        