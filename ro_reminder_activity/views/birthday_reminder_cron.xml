<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="ir_cron_send_birthday_notification" model="ir.cron">
            <field name="name">Send Birthday Notifications</field>
            <field name="model_id" ref="model_hr_employee"/>
            <field name="state">code</field>
            <field name="code">model.send_birthday_notification()</field>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="numbercall">-1</field>
            <field name="doall" eval="False"/>
            <field name="active" eval="True"/>
        </record>
    </data>
</odoo>
