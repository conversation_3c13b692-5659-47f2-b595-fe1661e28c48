from odoo import api, fields, models
from datetime import datetime, timedelta

class Employee(models.Model):
    _inherit = 'hr.employee'

    @api.model
    def send_birthday_notification(self):
        tomorrow = fields.Date.today() + timedelta(days=1)
        
        users_in_group = self.env['res.users'].search([('groups_id', 'in', self.env.ref('ro_reminder_activity.group_reminder_birthday').id)])

        employees = self.search([]).filtered(lambda x: x.birthday and x.birthday.day == tomorrow.day and x.birthday.month == tomorrow.month)

        for employee in employees:
            for user in users_in_group:
                activity_type = self.env.ref('mail.mail_activity_data_todo')
                activity = {
                    'activity_type_id': activity_type.id,
                    'summary': 'Birthday Reminder',
                    'note': "Tomorrow is the birthday of %s!" % employee.name,
                    'res_id': employee.id,
                    'res_model_id': self.env.ref('hr.model_hr_employee').id,
                    'date_deadline': tomorrow,
                    'user_id': user.id  
                }
                self.env['mail.activity'].create(activity)

        return True
